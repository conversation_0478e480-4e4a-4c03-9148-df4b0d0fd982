#!/usr/bin/env node

// Simple browser automation script to test authentication
const http = require('http');

// Test data
const testCases = [
    {
        name: "Registration Test",
        endpoint: "/users/register",
        data: {
            username: "browser_test_" + Date.now(),
            email: "browser_test_" + Date.now() + "@example.com",
            password: "BrowserTest123!"
        }
    },
    {
        name: "Login Test",
        endpoint: "/users/login", 
        data: {
            email: "<EMAIL>", // Use a previously registered email
            password: "BrowserTest123!"
        }
    }
];

async function runBrowserTests() {
    console.log('🌐 Running Browser-like Authentication Tests...\n');
    
    for (const test of testCases) {
        console.log(`🧪 Testing: ${test.name}`);
        console.log(`📤 Request: POST ${test.endpoint}`);
        console.log(`📋 Data: ${JSON.stringify(test.data, null, 2)}`);
        
        const result = await makeRequest(test.endpoint, 'POST', test.data);
        
        if (result.success) {
            console.log(`✅ Response: ${JSON.stringify(result.data, null, 2)}`);
            if (result.data.status) {
                console.log(`🎉 ${test.name} PASSED: ${result.data.msg}\n`);
            } else {
                console.log(`⚠️ ${test.name} FAILED: ${result.data.msg}\n`);
            }
        } else {
            console.log(`❌ ${test.name} ERROR: ${result.error}\n`);
        }
        
        console.log('-'.repeat(60) + '\n');
    }
}

function makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 4000,
            path: '/api/Upsocial' + endpoint,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk.toString();
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ success: true, data: jsonData, status: res.statusCode });
                } catch (error) {
                    resolve({ success: false, error: 'Invalid JSON response', body });
                }
            });
        });

        req.on('error', (error) => {
            resolve({ success: false, error: error.message });
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

// Run the tests
runBrowserTests().then(() => {
    console.log('🏁 Browser-like tests completed!');
    console.log('🌐 Visit http://localhost:3000 to see the interactive test interface');
});