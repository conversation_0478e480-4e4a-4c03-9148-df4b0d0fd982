# UpSocial Helia - Short Form Video Platform Requirements

## Executive Summary

UpSocial Helia is a decentralized short-form video platform built on Next.js 15 with Helia/IPFS for content storage and distribution. This document outlines the requirements to recreate and modernize the original UpSocial app using modern web technologies and decentralized infrastructure.

## Current Architecture Foundation

### Technology Stack
- **Frontend**: Next.js 15.4.4 with React 19.1.0
- **Styling**: Tailwind CSS 4
- **TypeScript**: Full type safety across the application
- **Storage**: Helia IPFS for decentralized content storage
- **State Management**: Zustand for lightweight state management
- **Deployment**: Vercel with static generation

### Existing Components Structure
```
src/
├── components/
│   ├── auth/           # SimpleAuthModal (✓ Implemented)
│   ├── video/          # VideoCard, SwipeVideoModal
│   ├── feed/           # CategoryTabs (✓ Updated)
│   ├── upload/         # VideoUploadModal (✓ Implemented)
│   ├── dashboard/      # User dashboard components
│   └── ui/            # Reusable UI components
├── types/             # TypeScript interfaces
├── lib/              # Utilities and data management
└── hooks/            # Custom React hooks
```

## Core Requirements

### 1. User Authentication & Profiles

#### 1.1 Authentication System
- **Multi-modal auth**: Email/password, social login (Google, GitHub), Web3 wallet connection
- **Anonymous browsing**: Allow content consumption without registration
- **Session management**: Persistent login across browser sessions
- **Profile creation**: Username, bio, avatar, social links

#### 1.2 User Profiles
- **Profile customization**: Avatar upload to IPFS, bio, display name
- **Content management**: View uploaded videos, playlists, liked content
- **Privacy controls**: Public/private profile options
- **Statistics**: Follower count, total views, video count

### 2. Video Content Management

#### 2.1 Video Upload System
**Current Status**: ✓ VideoUploadModal implemented with progress tracking

**Requirements**:
- **File support**: MP4, WebM, MOV formats (max 100MB)
- **Multiple upload methods**: 
  - File picker
  - Drag & drop
  - Camera recording (web API)
- **Metadata capture**:
  - Title (required, max 100 chars)
  - Description (optional, max 1000 chars)
  - Categories (single selection from 24 predefined)
  - Tags (multi-select, max 10)
  - Thumbnail auto-generation
- **IPFS integration**: Store video files and metadata on IPFS via Helia
- **Progress tracking**: Real-time upload progress with error handling

#### 2.2 Video Processing
- **Thumbnail generation**: Auto-extract frame at 2s mark
- **Compression**: Client-side video optimization for web delivery
- **Format validation**: Ensure compatibility across devices
- **Duration limits**: Maximum 60 seconds for short-form content

#### 2.3 Content Categories
Based on old app analysis, support these 24 categories:
```
Animation, Autos & Vehicles, Beauty & Fashion, Comedy, 
Cooking & Food, DIY & Crafts, Documentary, Education, 
Entertainment, Film & Animation, Gaming, Health & Fitness, 
How-to & Style, Kids & Family, Music, News & Politics, 
Nonprofits & Activism, People & Blogs, Pets & Animals, 
Science & Technology, Sports, Travel & Events, 
Unboxing & Reviews, Blogs
```

### 3. Video Feed & Discovery

#### 3.1 Feed Interface (TikTok-style)
**Current Status**: ✓ Basic masonry layout, needs swipe implementation

**Requirements**:
- **Vertical scroll feed**: Primary browsing experience
- **Auto-play**: Videos start playing when in viewport
- **Swipe gestures**: 
  - Swipe left: Like video
  - Swipe right: Dislike/skip
  - Swipe up: Next video
  - Swipe down: Previous video
- **Video controls**:
  - Tap to pause/play
  - Volume control
  - Progress indicator
  - Fullscreen option

#### 3.2 Category Filtering
**Current Status**: ✓ CategoryTabs component implemented

**Requirements**:
- **Horizontal scrollable tabs**: "NEWEST", "FOR YOU", "SUBSCRIPTIONS" + categories
- **Smart filtering**: Real-time content updates
- **Personalization**: "FOR YOU" algorithm based on user interactions

#### 3.3 Search & Discovery
- **Text search**: Search by title, description, tags, creator
- **Filter options**: Duration, upload date, popularity
- **Sort options**: Newest, most viewed, most liked, trending
- **Search suggestions**: Auto-complete based on popular searches

### 4. Video Playback & Interaction

#### 4.1 Playback Features
- **Progressive loading**: Stream from IPFS with buffering
- **Quality adaptation**: Auto-adjust based on connection speed
- **Picture-in-picture**: Continue watching while browsing
- **Keyboard shortcuts**: Space (pause), arrow keys (seek)

#### 4.2 Interaction System
- **Like/Unlike**: Heart animation on tap
- **Share**: Native web share API + copy link
- **Comments**: Threaded comment system (future phase)
- **Save to playlist**: Bookmark functionality
- **Report content**: Moderation tools

#### 4.3 Analytics Tracking
- **View metrics**: Track complete views, watch time
- **Engagement rates**: Likes, shares, saves per video
- **User behavior**: Most watched categories, session duration

### 5. IPFS/Helia Integration

#### 5.1 Content Storage
**Current Status**: ✓ Helia configured in package.json

**Requirements**:
- **Video files**: Store on IPFS with content addressing
- **Metadata**: JSON objects with video information
- **Thumbnails**: Separate IPFS storage for quick loading
- **User profiles**: Avatar and profile data on IPFS

#### 5.2 Content Delivery
- **Gateway failover**: Multiple IPFS gateways for reliability
- **Caching strategy**: LocalStorage + IndexedDB for offline access
- **Preloading**: Smart prefetch of next videos in feed
- **Performance optimization**: Lazy loading and compression

#### 5.3 Decentralization Features
- **Pinning services**: Integration with Pinata or Apillon
- **Content availability**: Ensure popular content remains accessible
- **Redundancy**: Multiple storage providers
- **Censorship resistance**: Distributed content cannot be easily removed

### 6. User Interface & Experience

#### 6.1 Responsive Design
**Current Status**: ✓ Tailwind CSS with responsive utilities

**Requirements**:
- **Mobile-first**: Optimized for touch devices
- **Desktop support**: Mouse and keyboard interactions
- **Tablet optimization**: Hybrid touch/cursor interface
- **PWA capabilities**: Installable web app with offline support

#### 6.2 Dark Theme
**Current Status**: ✓ Dark theme implemented in components

**Requirements**:
- **Consistent styling**: Gray-900/black backgrounds, purple accents
- **High contrast**: Accessible color ratios
- **Theme persistence**: Remember user preference
- **Component consistency**: All UI elements follow dark theme

#### 6.3 Performance Requirements
- **Initial load**: < 3 seconds on 3G networks
- **Video start**: < 2 seconds to first frame
- **Smooth scrolling**: 60fps feed interactions
- **Memory efficiency**: Handle 50+ videos in feed without degradation

### 7. Data Architecture

#### 7.1 Video Data Model
```typescript
interface Video {
  id: string                    // Unique identifier
  title: string                // Max 100 characters
  creator: string              // User ID
  thumbnail: string            // IPFS hash
  videoUrl: string             // IPFS hash
  views: number                // View count
  likes: number                // Like count
  duration: string             // Format: "MM:SS"
  ipfsHash: string             // Content hash
  createdAt: Date              // Upload timestamp
  categories: string[]         // Selected categories
  tags: string[]               // User-defined tags
  description?: string         // Optional description
  
  metadata: {
    resolution: string         // "1080p", "720p", etc.
    bitrate: number           // kbps
    codec: string             // "h264", "vp9", etc.
    fps: number               // Frames per second
    size: number              // File size in bytes
  }
  
  interactions: {
    liked: boolean            // User's like status
    shared: boolean           // User shared this video
    saved: boolean            // User saved to playlist
    reported: boolean         // User reported content
  }
  
  creatorInfo: {
    avatar: string            // IPFS hash
    verified: boolean         // Verification status
    followers: number         // Follower count
    bio: string              // Creator biography
  }
}
```

#### 7.2 User Data Model
```typescript
interface User {
  id: string                   // Unique user ID
  username: string             // Display name
  email?: string               // Email (if registered)
  avatar?: string              // IPFS hash
  bio?: string                 // User biography
  verified: boolean            // Verification status
  
  stats: {
    followers: number          // Follower count
    following: number          // Following count
    totalVideos: number        // Uploaded video count
    totalViews: number         // Cumulative views
    totalLikes: number         // Cumulative likes
  }
  
  preferences: {
    theme: 'dark' | 'light'    // UI theme
    autoplay: boolean          // Video autoplay
    notifications: boolean     // Push notifications
    privacy: 'public' | 'private' // Profile visibility
  }
  
  createdAt: Date              // Registration date
  lastActive: Date             // Last activity timestamp
}
```

### 8. Implementation Phases

#### Phase 1: Core Video Platform (Current Sprint)
**Status**: 🔄 In Progress
- ✅ Authentication system (SimpleAuthModal)
- ✅ Video upload interface (VideoUploadModal)
- ✅ Basic feed layout with CategoryTabs
- ✅ Video card components with dark theme
- 🔄 IPFS integration for video storage
- 🔄 Swipe gesture implementation
- 🔄 Video playback optimization

#### Phase 2: Enhanced User Experience (Next Sprint)
- Search and filtering functionality
- User profiles and channel management
- Like/share/save interactions
- Progressive video loading
- Offline content caching

#### Phase 3: Social Features (Future)
- Comments and discussions
- User following/subscriptions
- Playlist creation and management
- Content recommendations algorithm
- Push notifications

#### Phase 4: Advanced Features (Future)
- Live streaming capabilities
- Monetization options
- Advanced analytics dashboard
- Content moderation tools
- API for third-party integrations

### 9. Technical Specifications

#### 9.1 Browser Support
- **Chrome**: 90+ (primary target)
- **Firefox**: 88+ (full support)
- **Safari**: 14+ (WebKit optimizations)
- **Edge**: 90+ (Chromium-based)
- **Mobile browsers**: iOS Safari 14+, Chrome Mobile 90+

#### 9.2 Performance Targets
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Time to Interactive**: < 3.5s
- **Video start time**: < 2s on 4G, < 4s on 3G
- **Memory usage**: < 100MB for feed with 20 videos

#### 9.3 Storage Requirements
- **Local storage**: User preferences, auth tokens
- **IndexedDB**: Video metadata cache, thumbnails
- **IPFS**: All video content and user-generated data
- **CDN**: Static assets, app shell caching

### 10. Quality Assurance

#### 10.1 Testing Strategy
- **Unit tests**: Component functionality, utility functions
- **Integration tests**: IPFS operations, video upload flow
- **E2E tests**: Complete user journeys, cross-browser testing
- **Performance tests**: Load testing, memory leak detection

#### 10.2 Accessibility Requirements
- **WCAG 2.1 AA compliance**: Screen reader support, keyboard navigation
- **Color contrast**: Minimum 4.5:1 ratio for all text
- **Focus indicators**: Clear visual focus for all interactive elements
- **Alt text**: Descriptive text for all images and videos

#### 10.3 Security Considerations
- **Content validation**: Malware scanning for uploaded videos
- **Input sanitization**: XSS protection for user-generated content
- **Rate limiting**: Prevent spam uploads and API abuse
- **Privacy protection**: GDPR compliance, data encryption

## Success Metrics

### User Engagement
- **Daily Active Users**: Target 1000+ within 6 months
- **Session Duration**: Average 10+ minutes per session
- **Video Completion Rate**: 70%+ videos watched to completion
- **User Retention**: 40%+ return within 7 days

### Technical Performance
- **Uptime**: 99.9% availability
- **Load Time**: 95th percentile < 3 seconds
- **Video Quality**: 720p minimum, 1080p preferred
- **Error Rate**: < 1% failed video uploads

### Content Growth
- **Video Uploads**: 100+ new videos per week
- **Category Distribution**: Even distribution across all 24 categories
- **Content Quality**: Average 4+ star rating
- **IPFS Network Health**: 95%+ content availability

## Conclusion

This requirements document provides a comprehensive roadmap for developing UpSocial Helia as a modern, decentralized short-form video platform. The implementation leverages our existing Next.js/Helia foundation while incorporating proven features from the original React Native app.

The phased approach ensures rapid deployment of core features while maintaining high code quality and user experience standards. The focus on decentralization via IPFS provides unique value in the competitive social media landscape.

**Next Steps**: 
1. Complete Phase 1 implementation (video upload + swipe interface)
2. Integrate real IPFS storage with Helia
3. Implement comprehensive testing suite
4. Deploy MVP for beta testing

---

*Document Version: 1.0*  
*Last Updated: January 29, 2025*  
*Created for: UpSocial Helia Development Team*
