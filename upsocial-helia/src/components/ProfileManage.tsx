'use client'

import { useState, useEffect } from 'react'
import Sidebar from './layout/Sidebar'
import Home from './pages/Home'
import MyVideos from './pages/MyVideos'
import Profile from './pages/Profile'
import Upload from './pages/Upload'
import Settings from './pages/Settings'

export default function ProfileManage() {
  const [currentPage, setCurrentPage] = useState('Home')
  const [username, setUsername] = useState('User')
  const [sidebarOpen, setSidebarOpen] = useState(false)

  useEffect(() => {
    // Get username from auth state
    const storedUsername = localStorage.getItem('username') || 'User'
    setUsername(storedUsername)
  }, [])

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'Home':
        return <Home />
      case 'My Videos':
        return <MyVideos />
      case 'Profile':
        return <Profile />
      case 'Add a Video':
        return <Upload />
      case 'Settings':
        return <Settings />
      default:
        return <Home />
    }
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <Sidebar
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        username={username}
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Mobile Header */}
        <div className="lg:hidden bg-purple-700 text-white p-4 flex items-center justify-between">
          <button
            onClick={() => setSidebarOpen(true)}
            className="text-white"
            title="Menu"
          >
            ☰
          </button>
          <h1 className="text-xl font-bold">{currentPage}</h1>
          <div></div>
        </div>

        {/* Page Content */}
        <div className="flex-1 overflow-auto">
          {renderCurrentPage()}
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-50"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}
