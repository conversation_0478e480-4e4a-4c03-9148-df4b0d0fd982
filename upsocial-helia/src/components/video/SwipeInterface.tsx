'use client'

import { useState, useRef, useEffect } from 'react'
import VideoPlayer from './VideoPlayer'

interface Video {
  id: string
  title: string
  creator: string
  thumbnail: string
  videoUrl: string
  views: number
  likes: number
  duration: string
  ipfsHash?: string
}

interface SwipeInterfaceProps {
  videos: Video[]
  initialIndex?: number
  onVideoChange?: (index: number) => void
  onLike?: (videoId: string) => void
  onDislike?: (videoId: string) => void
  onShare?: (videoId: string) => void
  onClose?: () => void
}

export default function SwipeInterface({
  videos,
  initialIndex = 0,
  onVideoChange,
  onLike,
  onDislike,
  onShare,
  onClose
}: SwipeInterfaceProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [swipeAction, setSwipeAction] = useState<'like' | 'dislike' | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const startX = useRef(0)
  const startY = useRef(0)
  const currentX = useRef(0)
  const currentY = useRef(0)
  const isDragging = useRef(false)

  const currentVideo = videos[currentIndex]

  useEffect(() => {
    if (onVideoChange) {
      onVideoChange(currentIndex)
    }
  }, [currentIndex, onVideoChange])

  const handleTouchStart = (e: React.TouchEvent) => {
    startX.current = e.touches[0].clientX
    startY.current = e.touches[0].clientY
    isDragging.current = true
    setIsTransitioning(false)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging.current) return
    
    currentX.current = e.touches[0].clientX
    currentY.current = e.touches[0].clientY
    const deltaX = currentX.current - startX.current
    const deltaY = currentY.current - startY.current
    
    if (containerRef.current) {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        containerRef.current.style.transform = `translateX(${deltaX}px) rotate(${deltaX * 0.1}deg)`
      } else {
        containerRef.current.style.transform = `translateY(${deltaY}px)`
      }
    }
  }

  const handleTouchEnd = () => {
    if (!isDragging.current) return
    
    const deltaX = currentX.current - startX.current
    const deltaY = currentY.current - startY.current
    const threshold = 100

    setIsTransitioning(true)
    isDragging.current = false

    if (containerRef.current) {
      containerRef.current.style.transform = 'translateX(0) translateY(0) rotate(0deg)'
    }

    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
      // Horizontal swipe - like/dislike
      if (deltaX > 0) {
        // Swipe right - like
        setSwipeAction('like')
        handleLike()
        // Move to next video after a brief delay
        setTimeout(() => {
          setSwipeAction(null)
          if (currentIndex < videos.length - 1) {
            setCurrentIndex(prev => prev + 1)
          }
        }, 800)
      } else {
        // Swipe left - dislike
        setSwipeAction('dislike')
        handleDislike()
        // Move to next video after a brief delay
        setTimeout(() => {
          setSwipeAction(null)
          if (currentIndex < videos.length - 1) {
            setCurrentIndex(prev => prev + 1)
          }
        }, 800)
      }
    } else if (Math.abs(deltaY) > threshold) {
      // Vertical swipe - navigation
      if (deltaY > 0 && currentIndex > 0) {
        // Swipe down - go to previous video
        setCurrentIndex(prev => prev - 1)
      } else if (deltaY < 0 && currentIndex < videos.length - 1) {
        // Swipe up - go to next video
        setCurrentIndex(prev => prev + 1)
      }
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    startX.current = e.clientX
    startY.current = e.clientY
    isDragging.current = true
    setIsTransitioning(false)
    e.preventDefault()
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging.current) return
    
    currentX.current = e.clientX
    currentY.current = e.clientY
    const deltaX = currentX.current - startX.current
    const deltaY = currentY.current - startY.current
    
    if (containerRef.current) {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        containerRef.current.style.transform = `translateX(${deltaX}px) rotate(${deltaX * 0.1}deg)`
      } else {
        containerRef.current.style.transform = `translateY(${deltaY}px)`
      }
    }
  }

  const handleMouseUp = () => {
    if (!isDragging.current) return
    
    const deltaX = currentX.current - startX.current
    const deltaY = currentY.current - startY.current
    const threshold = 100

    setIsTransitioning(true)
    isDragging.current = false

    if (containerRef.current) {
      containerRef.current.style.transform = 'translateX(0) translateY(0) rotate(0deg)'
    }

    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
      // Horizontal swipe - like/dislike
      if (deltaX > 0) {
        // Swipe right - like
        setSwipeAction('like')
        handleLike()
        // Move to next video after a brief delay
        setTimeout(() => {
          setSwipeAction(null)
          if (currentIndex < videos.length - 1) {
            setCurrentIndex(prev => prev + 1)
          }
        }, 800)
      } else {
        // Swipe left - dislike
        setSwipeAction('dislike')
        handleDislike()
        // Move to next video after a brief delay
        setTimeout(() => {
          setSwipeAction(null)
          if (currentIndex < videos.length - 1) {
            setCurrentIndex(prev => prev + 1)
          }
        }, 800)
      }
    } else if (Math.abs(deltaY) > threshold) {
      // Vertical swipe - navigation
      if (deltaY > 0 && currentIndex > 0) {
        setCurrentIndex(prev => prev - 1)
      } else if (deltaY < 0 && currentIndex < videos.length - 1) {
        setCurrentIndex(prev => prev + 1)
      }
    }
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowUp':
        if (currentIndex > 0) {
          setCurrentIndex(prev => prev - 1)
        }
        break
      case 'ArrowDown':
        if (currentIndex < videos.length - 1) {
          setCurrentIndex(prev => prev + 1)
        }
        break
      case 'Escape':
        if (onClose) {
          onClose()
        }
        break
    }
  }

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [currentIndex, videos.length, onClose])

  const handleLike = () => {
    if (onLike && currentVideo) {
      onLike(currentVideo.id)
    }
  }

  const handleDislike = () => {
    if (onDislike && currentVideo) {
      onDislike(currentVideo.id)
    }
  }

  const handleShare = () => {
    if (onShare && currentVideo) {
      onShare(currentVideo.id)
    }
  }

  if (!currentVideo) {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center">
        <div className="text-white text-xl">No videos available</div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black z-50 overflow-hidden">
      {/* Close Button */}
      {onClose && (
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 text-white hover:text-gray-300 transition-colors"
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}

      {/* Video Container */}
      <div
        ref={containerRef}
        className={`w-full h-full ${isTransitioning ? 'transition-transform duration-300' : ''}`}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <VideoPlayer
          videoUrl={currentVideo.videoUrl}
          thumbnail={currentVideo.thumbnail}
          autoPlay={true}
          onLike={handleLike}
          onDislike={handleDislike}
          onShare={handleShare}
          className="w-full h-full"
        />
      </div>

      {/* Video Info Overlay */}
      <div className="absolute bottom-20 left-4 right-4 text-white">
        <div className="mb-2">
          <h2 className="text-lg font-semibold mb-1">{currentVideo.title}</h2>
          <p className="text-sm opacity-80">@{currentVideo.creator}</p>
        </div>
        
        <div className="flex items-center space-x-4 text-sm opacity-80">
          <span>{currentVideo.views.toLocaleString()} views</span>
          <span>{currentVideo.likes.toLocaleString()} likes</span>
          <span>{currentVideo.duration}</span>
        </div>
      </div>

      {/* Navigation Indicators */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col space-y-2">
        {videos.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full transition-colors ${
              index === currentIndex ? 'bg-white' : 'bg-white/30'
            }`}
          />
        ))}
      </div>

      {/* Swipe Action Feedback */}
      {swipeAction && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className={`text-white text-6xl font-bold animate-pulse ${
            swipeAction === 'like' ? 'text-green-400' : 'text-red-400'
          }`}>
            {swipeAction === 'like' ? '❤️' : '💔'}
          </div>
        </div>
      )}

      {/* Swipe Hints */}
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/50 text-xs">
        {currentIndex > 0 && (
          <div className="flex items-center mb-2">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
            </svg>
            <span>Previous</span>
          </div>
        )}
        {currentIndex < videos.length - 1 && (
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            <span>Next</span>
          </div>
        )}
      </div>

      {/* Swipe Instructions */}
      <div className="absolute bottom-4 left-4 right-4 text-center text-white/60 text-sm">
        <p>Swipe right to ❤️ • Swipe left to 💔 • Swipe up/down to navigate</p>
      </div>

      {/* Progress Indicator */}
      <div className="absolute top-4 left-4 right-16 h-1 bg-white/20 rounded">
        <div
          className="h-full bg-purple-500 rounded transition-all duration-300"
          style={{ width: `${((currentIndex + 1) / videos.length) * 100}%` }}
        />
      </div>
    </div>
  )
}