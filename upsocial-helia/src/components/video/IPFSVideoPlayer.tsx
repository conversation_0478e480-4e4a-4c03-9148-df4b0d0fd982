'use client'

import React, { useState, useRef, useEffect } from 'react'
import { videoStorage } from '@/lib/ipfs/video-storage'
import { VideoMetadata } from '@/lib/ipfs/video-storage'

interface VideoPlayerProps {
  cid: string
  metadata?: VideoMetadata
  autoPlay?: boolean
  controls?: boolean
  className?: string
  onError?: (error: string) => void
  onLoadStart?: () => void
  onLoadEnd?: () => void
}

export default function IPFSVideoPlayer({
  cid,
  metadata,
  autoPlay = false,
  controls = true,
  className = '',
  onError,
  onLoadStart,
  onLoadEnd
}: VideoPlayerProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [videoUrl, setVideoUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadVideo()
    return () => {
      // Cleanup blob URL when component unmounts
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl)
      }
    }
  }, [cid])

  const loadVideo = async () => {
    if (!cid) return

    setIsLoading(true)
    setError(null)
    onLoadStart?.()

    try {
      console.log('🎬 Loading video from IPFS:', cid)
      
      // Initialize video storage if needed
      await videoStorage.initialize()
      
      // Create blob URL for the video
      const url = await videoStorage.createBlobUrl(cid)
      setVideoUrl(url)
      
      console.log('✅ Video loaded successfully')
      onLoadEnd?.()
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load video'
      console.error('❌ Failed to load video:', err)
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime
      const total = videoRef.current.duration
      setCurrentTime(current)
      setProgress(total > 0 ? (current / total) * 100 : 0)
    }
  }

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration)
    }
  }

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
    }
  }

  const handleSeek = (percentage: number) => {
    if (videoRef.current) {
      const newTime = (percentage / 100) * duration
      videoRef.current.currentTime = newTime
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
    }
  }

  const handleMute = () => {
    setIsMuted(!isMuted)
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
    }
  }

  const handleFullscreen = () => {
    if (!containerRef.current) return

    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  if (error) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>
        <div className="text-center p-8">
          <div className="text-4xl mb-4">❌</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">Failed to Load Video</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadVideo}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}>
        <div className="text-center p-8">
          <div className="animate-spin text-4xl mb-4">⭕</div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">Loading Video</h3>
          <p className="text-gray-600">Retrieving from IPFS network...</p>
          <div className="mt-4 text-sm text-gray-500">
            CID: {cid}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className={`relative bg-black rounded-lg overflow-hidden group ${className}`}>
      {/* Video Element */}
      <video
        ref={videoRef}
        src={videoUrl || undefined}
        autoPlay={autoPlay}
        controls={false} // We'll use custom controls
        className="w-full h-full object-contain"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        onError={() => setError('Video playback error')}
      />

      {/* Video Metadata Overlay */}
      {metadata && (
        <div className="absolute top-4 left-4 right-4 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-black bg-opacity-75 text-white p-3 rounded">
            <h3 className="font-medium text-lg">{metadata.title}</h3>
            {metadata.description && (
              <p className="text-sm text-gray-300 mt-1">{metadata.description}</p>
            )}
            {metadata.tags && metadata.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {metadata.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-600 text-xs rounded"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Custom Controls */}
      {controls && (
        <div className="absolute bottom-0 left-0 right-0 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-gradient-to-t from-black to-transparent p-4">
            {/* Progress Bar */}
            <div className="mb-3">
              <div
                className="w-full bg-gray-600 h-1 rounded cursor-pointer"
                onClick={(e) => {
                  const rect = e.currentTarget.getBoundingClientRect()
                  const percentage = ((e.clientX - rect.left) / rect.width) * 100
                  handleSeek(percentage)
                }}
              >
                <div
                  className="bg-blue-500 h-1 rounded transition-all"
                  style={{
                    width: `${progress}%`
                  }}
                />
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-between text-white">
              <div className="flex items-center space-x-4">
                {/* Play/Pause */}
                <button
                  onClick={handlePlayPause}
                  className="text-xl hover:text-blue-400 transition-colors"
                  title={isPlaying ? 'Pause' : 'Play'}
                >
                  {isPlaying ? '⏸️' : '▶️'}
                </button>

                {/* Time Display */}
                <div className="text-sm font-mono">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Volume Control */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleMute}
                    className="hover:text-blue-400 transition-colors"
                    title={isMuted ? 'Unmute' : 'Mute'}
                  >
                    {isMuted ? '🔇' : volume > 0.5 ? '🔊' : '🔉'}
                  </button>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={volume}
                    onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                    className="w-16 h-1 bg-gray-600 rounded"
                    title="Volume"
                  />
                </div>

                {/* Fullscreen */}
                <button
                  onClick={handleFullscreen}
                  className="hover:text-blue-400 transition-colors"
                  title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
                >
                  {isFullscreen ? '🗗' : '⛶'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading Overlay for Video */}
      {videoRef.current && videoRef.current.readyState < 3 && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="text-white text-center">
            <div className="animate-spin text-2xl mb-2">⭕</div>
            <div>Buffering...</div>
          </div>
        </div>
      )}

      {/* IPFS Info */}
      <div className="absolute top-4 right-4 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
          <div>📡 IPFS</div>
          <div className="font-mono text-xs">{cid.slice(0, 8)}...</div>
        </div>
      </div>
    </div>
  )
}
