'use client'

import { useEffect, useRef } from 'react'
import { XMarkIcon } from '@heroicons/react/24/solid'
import { Video } from '../../types/video'
import VideoPlayerNew from './VideoPlayerNew'

interface VideoModalProps {
  video: Video
  isOpen: boolean
  onClose: () => void
  onLike?: (videoId: string) => void
  onShare?: (videoId: string) => void
}

export default function VideoModal({
  video,
  isOpen,
  onClose,
  onLike,
  onShare
}: VideoModalProps) {
  const modalRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.addEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  const handleLike = () => {
    if (onLike) {
      onLike(video.id)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: video.title,
          text: `Check out this video: ${video.title}`,
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: Copy to clipboard
      await navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
    
    if (onShare) {
      onShare(video.id)
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  const formatLikes = (likes: number) => {
    if (likes >= 1000000) {
      return `${(likes / 1000000).toFixed(1)}M`
    } else if (likes >= 1000) {
      return `${(likes / 1000).toFixed(1)}K`
    }
    return likes.toString()
  }

  const getTimeAgo = (date?: Date) => {
    if (!date) return '2 days ago'
    
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}mo ago`
    return `${Math.floor(diffInSeconds / 31536000)}y ago`
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div
        ref={modalRef}
        className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white truncate">
            {video.title}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            title="Close modal"
            aria-label="Close modal"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Video Player */}
        <div className="relative">
          <VideoPlayerNew
            videoCID={video.ipfsHash || video.videoUrl}
            thumbnail={video.thumbnail}
            className="w-full aspect-video"
          />
        </div>

        {/* Video Info */}
        <div className="p-4">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-white mb-2">
                {video.title}
              </h3>
              
              <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                <div className="flex items-center">
                  <span>{formatViews(video.views)} views</span>
                </div>
                <div className="flex items-center">
                  <span>{formatLikes(video.likes)} likes</span>
                </div>
                <div className="flex items-center">
                  <span>{getTimeAgo(video.createdAt)}</span>
                </div>
              </div>

              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">
                    {video.creator.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <p className="text-purple-400 font-medium">@{video.creator}</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleLike}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
                <span>Like</span>
              </button>
              
              <button
                onClick={handleShare}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
                <span>Share</span>
              </button>
            </div>
          </div>

          {/* Description */}
          {video.description && (
            <div className="border-t border-gray-700 pt-4">
              <h4 className="text-white font-medium mb-2">Description</h4>
              <p className="text-gray-300 text-sm whitespace-pre-wrap">
                {video.description}
              </p>
            </div>
          )}

          {/* Categories */}
          {video.categories && video.categories.length > 0 && (
            <div className="border-t border-gray-700 pt-4 mt-4">
              <h4 className="text-white font-medium mb-2">Categories</h4>
              <div className="flex flex-wrap gap-2">
                {video.categories.map((category, index) => (
                  <span
                    key={index}
                    className="bg-purple-600 text-white text-xs px-3 py-1 rounded-full"
                  >
                    {category}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Technical Info */}
          {video.metadata && (
            <div className="border-t border-gray-700 pt-4 mt-4">
              <h4 className="text-white font-medium mb-2">Technical Details</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-400">
                {video.metadata.resolution && (
                  <div>
                    <span className="text-gray-300">Resolution:</span>
                    <br />
                    {video.metadata.resolution}
                  </div>
                )}
                {video.metadata.codec && (
                  <div>
                    <span className="text-gray-300">Codec:</span>
                    <br />
                    {video.metadata.codec}
                  </div>
                )}
                {video.metadata.fps && (
                  <div>
                    <span className="text-gray-300">FPS:</span>
                    <br />
                    {video.metadata.fps}
                  </div>
                )}
                {video.metadata.size && (
                  <div>
                    <span className="text-gray-300">Size:</span>
                    <br />
                    {(video.metadata.size / (1024 * 1024)).toFixed(1)} MB
                  </div>
                )}
              </div>
            </div>
          )}

          {/* IPFS Info */}
          {video.ipfsHash && (
            <div className="border-t border-gray-700 pt-4 mt-4">
              <h4 className="text-white font-medium mb-2">IPFS Information</h4>
              <div className="bg-gray-800 p-3 rounded-lg">
                <p className="text-green-400 text-xs font-mono break-all">
                  {video.ipfsHash}
                </p>
                <p className="text-gray-400 text-xs mt-1">
                  This video is stored on the InterPlanetary File System (IPFS)
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
