'use client'

import { useState, useRef, useEffect } from 'react'
import { PlayIcon, PauseIcon, SpeakerWaveIcon, SpeakerXMarkIcon } from '@heroicons/react/24/solid'
import { VideoStorageService } from '@/lib/ipfs/video-storage'

interface VideoPlayerProps {
  videoUrl?: string
  videoCID?: string
  thumbnail?: string
  autoPlay?: boolean
  onLike?: () => void
  onDislike?: () => void
  onShare?: () => void
  onClick?: () => void
  className?: string
}

export default function VideoPlayer({
  videoUrl,
  videoCID,
  thumbnail,
  autoPlay = false,
  onLike,
  onDislike,
  onShare,
  onClick,
  className = ''
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  
  const [videoSrc, setVideoSrc] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)
  const [showControls, setShowControls] = useState(true)
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null)

  // Load video from IPFS or direct URL
  useEffect(() => {
    const loadVideo = async () => {
      setLoading(true)
      setError('')

      try {
        if (videoCID) {
          console.log('Loading video from IPFS:', videoCID)
          const storageService = VideoStorageService.getInstance()
          await storageService.initialize()
          
          const videoBlob = await storageService.getVideo(videoCID)
          const url = URL.createObjectURL(videoBlob)
          setVideoSrc(url)
        } else if (videoUrl) {
          setVideoSrc(videoUrl)
        } else {
          setError('No video source provided')
          setLoading(false)
          return
        }
        
        setLoading(false)
      } catch (err) {
        console.error('Failed to load video:', err)
        setError('Failed to load video')
        setLoading(false)
      }
    }

    if (videoCID || videoUrl) {
      loadVideo()
    }

    // Cleanup blob URL when component unmounts
    return () => {
      if (videoSrc && videoSrc.startsWith('blob:')) {
        URL.revokeObjectURL(videoSrc)
      }
    }
  }, [videoCID, videoUrl])

  // Video event handlers
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
      if (autoPlay) {
        video.play().catch(console.error)
      }
    }

    const handleTimeUpdate = () => {
      if (video.duration > 0) {
        setProgress((video.currentTime / video.duration) * 100)
      }
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      setProgress(0)
    }

    const handleError = () => {
      setError('Video playback error')
      setLoading(false)
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('ended', handleEnded)
    video.addEventListener('error', handleError)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('ended', handleEnded)
      video.removeEventListener('error', handleError)
    }
  }, [videoSrc, autoPlay])

  // Auto-hide controls
  useEffect(() => {
    if (controlsTimeout) {
      clearTimeout(controlsTimeout)
    }

    if (showControls && isPlaying) {
      const timeout = setTimeout(() => {
        setShowControls(false)
      }, 3000)
      setControlsTimeout(timeout)
    }

    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout)
      }
    }
  }, [showControls, isPlaying])

  const togglePlayPause = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play().catch(console.error)
    }
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    video.muted = !video.muted
    setIsMuted(video.muted)
  }

  const seek = (percentage: number) => {
    const video = videoRef.current
    if (!video || !duration) return

    video.currentTime = (percentage / 100) * duration
  }

  const handleContainerClick = () => {
    if (onClick) {
      onClick()
    } else {
      togglePlayPause()
    }
    setShowControls(true)
  }

  const handleMouseMove = () => {
    setShowControls(true)
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (loading) {
    return (
      <div className={`bg-gray-800 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-2"></div>
          <div className="text-sm">Loading video...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-gray-800 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-red-400 text-center">
          <div className="text-2xl mb-2">⚠️</div>
          <div className="text-sm">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className={`relative bg-black rounded-lg overflow-hidden cursor-pointer ${className}`}
      onClick={handleContainerClick}
      onMouseMove={handleMouseMove}
    >
      <video
        ref={videoRef}
        src={videoSrc}
        className="w-full h-full object-cover"
        playsInline
        muted={isMuted}
        poster={thumbnail}
        onError={() => setError('Video format not supported')}
      />

      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      )}

      {/* Play/Pause overlay for tap to play */}
      {!isPlaying && !loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black bg-opacity-50 rounded-full p-3">
            <PlayIcon className="w-8 h-8 text-white fill-white" />
          </div>
        </div>
      )}

      {/* Action buttons */}
      <div className="absolute bottom-16 right-4 flex flex-col space-y-4 z-10">
        {onLike && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onLike()
            }}
            className="bg-black bg-opacity-50 rounded-full p-3 text-white hover:bg-opacity-70 transition-all"
          >
            ❤️
          </button>
        )}
        
        {onShare && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onShare()
            }}
            className="bg-black bg-opacity-50 rounded-full p-3 text-white hover:bg-opacity-70 transition-all"
          >
            📤
          </button>
        )}
      </div>

      {/* Controls */}
      {(showControls || !isPlaying) && (
        <div 
          className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Progress bar */}
          <div className="mb-3">
            <div 
              className="w-full h-1 bg-gray-600 rounded cursor-pointer"
              onClick={(e) => {
                const rect = e.currentTarget.getBoundingClientRect()
                const percentage = ((e.clientX - rect.left) / rect.width) * 100
                seek(percentage)
              }}
            >
              <div 
                className="h-full bg-purple-500 rounded transition-all duration-200"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Control buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={togglePlayPause}
                className="text-white hover:text-purple-400 transition-colors"
              >
                {isPlaying ? (
                  <PauseIcon className="w-5 h-5" />
                ) : (
                  <PlayIcon className="w-5 h-5" />
                )}
              </button>

              <button
                onClick={toggleMute}
                className="text-white hover:text-purple-400 transition-colors"
              >
                {isMuted ? (
                  <SpeakerXMarkIcon className="w-5 h-5" />
                ) : (
                  <SpeakerWaveIcon className="w-5 h-5" />
                )}
              </button>

              <div className="text-white text-sm">
                {formatTime(videoRef.current?.currentTime || 0)} / {formatTime(duration)}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
