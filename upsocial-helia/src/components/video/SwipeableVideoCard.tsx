'use client'

import { useState, useRef } from 'react'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { Video } from '../../types/video'

interface SwipeableVideoCardProps {
  video: Video
  onVideoSelect: (video: Video) => void
  onLike: (videoId: string) => void
  onDislike: (videoId: string) => void
  className?: string
}

export default function SwipeableVideoCard({
  video,
  onVideoSelect,
  onLike,
  onDislike,
  className = ''
}: SwipeableVideoCardProps) {
  const [isLiked, setIsLiked] = useState(false)
  const [isDisliked, setIsDisliked] = useState(false)
  const [swipeAction, setSwipeAction] = useState<'like' | 'dislike' | null>(null)
  const cardRef = useRef<HTMLDivElement>(null)

  const handleSwipeLeft = () => {
    setSwipeAction('dislike')
    setIsDisliked(true)
    onDislike(video.id)
    
    // Clear the action after animation
    setTimeout(() => {
      setSwipeAction(null)
    }, 1000)
  }

  const handleSwipeRight = () => {
    setSwipeAction('like')
    setIsLiked(true)
    onLike(video.id)
    
    // Clear the action after animation
    setTimeout(() => {
      setSwipeAction(null)
    }, 1000)
  }

  const { elementRef } = useSwipeGestures({
    onSwipeLeft: handleSwipeLeft,
    onSwipeRight: handleSwipeRight,
    threshold: 50
  })

  // Combine refs
  const setRefs = (element: HTMLDivElement | null) => {
    cardRef.current = element
    if (elementRef) {
      elementRef.current = element
    }
  }

  return (
    <div
      ref={setRefs}
      className={`relative group cursor-pointer ${className}`}
      onClick={() => onVideoSelect(video)}
    >
      {/* Swipe Action Feedback */}
      {swipeAction && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20 bg-black bg-opacity-50 rounded-lg">
          <div className={`text-6xl font-bold animate-bounce ${
            swipeAction === 'like' ? 'text-green-400' : 'text-red-400'
          }`}>
            {swipeAction === 'like' ? '❤️' : '💔'}
          </div>
        </div>
      )}

      {/* Video Thumbnail */}
      <div className="relative aspect-video bg-gray-800 rounded-lg overflow-hidden">
        <img
          src={video.thumbnail}
          alt={video.title}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.src = '/assets/default.png'
          }}
        />
        
        {/* Play Button Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center">
          <div className="w-12 h-12 bg-purple-600 bg-opacity-90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>

        {/* Duration */}
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {video.duration}
        </div>

        {/* Like/Dislike Status Indicators */}
        {isLiked && (
          <div className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">
            ❤️ Liked
          </div>
        )}
        {isDisliked && (
          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full animate-pulse">
            💔 Disliked
          </div>
        )}
      </div>

      {/* Video Info */}
      <div className="mt-2">
        <h3 className="text-white font-medium text-sm line-clamp-2 mb-1">
          {video.title}
        </h3>
        <p className="text-gray-400 text-xs">
          @{video.creator}
        </p>
        <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
          <span>{video.views.toLocaleString()} views</span>
          <span>•</span>
          <span>{video.likes.toLocaleString()} likes</span>
        </div>
      </div>

      {/* Menu Button */}
      <button 
        type="button"
        className="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-1 hover:bg-opacity-75"
        onClick={(e) => {
          e.stopPropagation()
          // Handle menu click
        }}
        title="More options"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
        </svg>
      </button>

      {/* Swipe Hint */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <p className="text-center">← Swipe left to dislike • Swipe right to like →</p>
      </div>
    </div>
  )
}