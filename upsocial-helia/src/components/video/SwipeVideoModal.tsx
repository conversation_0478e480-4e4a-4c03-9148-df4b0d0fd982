'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { X, Share2, Heart, MessageCircle, MoreVertical, ArrowLeft } from 'lucide-react'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import { Video } from '../../types/video'

interface SwipeVideoModalProps {
  isOpen: boolean
  videos: Video[]
  currentIndex: number
  onClose: () => void
  onSwipe: (direction: 'left' | 'right', video: Video) => void
  onLike: (videoId: string) => void
  onShare: (videoId: string) => void
  onComment: (videoId: string) => void
}

export default function SwipeVideoModal({
  isOpen,
  videos,
  currentIndex,
  onClose,
  onSwipe,
  onLike,
  onShare,
  onComment
}: SwipeVideoModalProps) {
  const [activeVideoIndex, setActiveVideoIndex] = useState(currentIndex)
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null)
  const [isPlaying, setIsPlaying] = useState(true)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])
  const containerRef = useRef<HTMLDivElement>(null)

  // Initialize video refs array
  useEffect(() => {
    videoRefs.current = videos.map(() => null)
  }, [videos])

  // Update active video when currentIndex changes
  useEffect(() => {
    if (isOpen) {
      setActiveVideoIndex(currentIndex)
    }
  }, [currentIndex, isOpen])

  // Handle video play/pause when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      playCurrentVideo()
    } else {
      pauseAllVideos()
    }
  }, [isOpen, activeVideoIndex])

  const playCurrentVideo = useCallback(() => {
    const currentVideo = videoRefs.current[activeVideoIndex]
    if (currentVideo) {
      currentVideo.play().catch(console.error)
      setIsPlaying(true)
    }
  }, [activeVideoIndex])

  const pauseAllVideos = useCallback(() => {
    videoRefs.current.forEach(video => {
      if (video) {
        video.pause()
      }
    })
    setIsPlaying(false)
  }, [])

  const handleSwipeLeft = useCallback(() => {
    const currentVideo = videos[activeVideoIndex]
    if (currentVideo) {
      setSwipeDirection('left')
      onSwipe('left', currentVideo)
      
      // Move to next video
      setTimeout(() => {
        const nextIndex = (activeVideoIndex + 1) % videos.length
        setActiveVideoIndex(nextIndex)
        setSwipeDirection(null)
      }, 300)
    }
  }, [activeVideoIndex, videos, onSwipe])

  const handleSwipeRight = useCallback(() => {
    const currentVideo = videos[activeVideoIndex]
    if (currentVideo) {
      setSwipeDirection('right')
      onSwipe('right', currentVideo)
      
      // Move to next video
      setTimeout(() => {
        const nextIndex = (activeVideoIndex + 1) % videos.length
        setActiveVideoIndex(nextIndex)
        setSwipeDirection(null)
      }, 300)
    }
  }, [activeVideoIndex, videos, onSwipe])

  const togglePlayPause = () => {
    const currentVideo = videoRefs.current[activeVideoIndex]
    if (currentVideo) {
      if (isPlaying) {
        currentVideo.pause()
      } else {
        currentVideo.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const { elementRef } = useSwipeGestures({
    onSwipeLeft: handleSwipeLeft,
    onSwipeRight: handleSwipeRight,
    threshold: 100
  })

  // Combine refs
  const setContainerRef = useCallback((element: HTMLDivElement | null) => {
    containerRef.current = element
    if (elementRef) {
      elementRef.current = element
    }
  }, [elementRef])

  if (!isOpen || videos.length === 0) return null

  const currentVideo = videos[activeVideoIndex]

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 flex items-center justify-between p-4 bg-gradient-to-b from-black/50 to-transparent">
        <button
          onClick={onClose}
          className="p-2 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors"
          title="Close video"
        >
          <ArrowLeft className="w-6 h-6" />
        </button>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => onShare(currentVideo.id)}
            className="p-2 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors"
            title="Share video"
          >
            <Share2 className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Video Container */}
      <div 
        ref={setContainerRef}
        className="relative w-full h-full overflow-hidden touch-pan-y"
      >
        {/* Current Video */}
        <div 
          className={`absolute inset-0 transition-transform duration-300 ${
            swipeDirection === 'left' ? '-translate-x-full rotate-12' :
            swipeDirection === 'right' ? 'translate-x-full -rotate-12' :
            'translate-x-0'
          }`}
        >
          <video
            ref={(el) => { videoRefs.current[activeVideoIndex] = el }}
            src={currentVideo.videoUrl}
            poster={currentVideo.thumbnail}
            className="w-full h-full object-cover"
            loop
            muted={false}
            playsInline
            onClick={togglePlayPause}
            onLoadedData={() => playCurrentVideo()}
            onError={(e) => console.error('Video error:', e)}
          />

          {/* Swipe Indicators */}
          {swipeDirection === 'right' && (
            <div className="absolute top-20 left-8 transform -rotate-12 pointer-events-none z-10">
              <div className="bg-green-500 text-white px-6 py-3 rounded-lg border-4 border-green-400 shadow-lg">
                <span className="text-3xl font-bold">UP!</span>
              </div>
            </div>
          )}

          {swipeDirection === 'left' && (
            <div className="absolute top-20 right-8 transform rotate-12 pointer-events-none z-10">
              <div className="bg-red-500 text-white px-6 py-3 rounded-lg border-4 border-red-400 shadow-lg">
                <span className="text-3xl font-bold">DOWN</span>
              </div>
            </div>
          )}

          {/* Video Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-6">
            <div className="flex items-end justify-between">
              {/* Creator Info & Description */}
              <div className="flex-1 mr-4">
                <div className="flex items-center mb-3">
                  {currentVideo.creatorInfo?.avatar ? (
                    <img
                      src={currentVideo.creatorInfo.avatar}
                      alt={currentVideo.creator}
                      className="w-12 h-12 rounded-full border-2 border-white"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold border-2 border-white">
                      {currentVideo.creator.charAt(0).toUpperCase()}
                    </div>
                  )}
                  <div className="ml-3">
                    <p className="text-white font-semibold text-lg">{currentVideo.creator}</p>
                    <p className="text-gray-300 text-sm">{currentVideo.categories?.[0] || 'Video'}</p>
                  </div>
                </div>
                
                <h3 className="text-white font-bold text-xl mb-2 line-clamp-2">
                  {currentVideo.title}
                </h3>
                
                {currentVideo.description && (
                  <p className="text-gray-300 text-sm line-clamp-3 mb-3">
                    {currentVideo.description}
                  </p>
                )}

                <div className="flex items-center space-x-4 text-gray-400 text-sm">
                  <span>{currentVideo.views.toLocaleString()} views</span>
                  <span>•</span>
                  <span>{currentVideo.createdAt ? new Date(currentVideo.createdAt).toLocaleDateString() : 'Recently'}</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col items-center space-y-4">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onLike(currentVideo.id)
                  }}
                  className="flex flex-col items-center group"
                  title="Like video"
                >
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-red-500/30 transition-colors">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-white text-xs mt-1">{currentVideo.likes.toLocaleString()}</span>
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onComment(currentVideo.id)
                  }}
                  className="flex flex-col items-center group"
                  title="Comment on video"
                >
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-blue-500/30 transition-colors">
                    <MessageCircle className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-white text-xs mt-1">Comments</span>
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onShare(currentVideo.id)
                  }}
                  className="flex flex-col items-center group"
                  title="Share video"
                >
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-green-500/30 transition-colors">
                    <Share2 className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-white text-xs mt-1">Share</span>
                </button>

                <button className="flex flex-col items-center group" title="More options">
                  <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-gray-500/30 transition-colors">
                    <MoreVertical className="w-6 h-6 text-white" />
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Play/Pause Indicator */}
          {!isPlaying && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="bg-black/50 rounded-full p-4">
                <div className="w-8 h-8 flex items-center justify-center">
                  <div className="w-0 h-0 border-l-[16px] border-l-white border-t-[12px] border-t-transparent border-b-[12px] border-b-transparent ml-1"></div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Preload next video */}
        {videos.length > 1 && (
          <video
            ref={(el) => { 
              const nextIndex = (activeVideoIndex + 1) % videos.length
              videoRefs.current[nextIndex] = el 
            }}
            src={videos[(activeVideoIndex + 1) % videos.length]?.videoUrl}
            className="hidden"
            preload="metadata"
          />
        )}
      </div>

      {/* Swipe Instructions */}
      <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 text-white text-center opacity-60">
        <p className="text-sm">← Swipe left to dislike • Swipe right to like →</p>
      </div>

      {/* Video Counter */}
      <div className="absolute top-16 right-4 text-white text-sm bg-black/30 px-3 py-1 rounded-full">
        {activeVideoIndex + 1} / {videos.length}
      </div>
    </div>
  )
}
