'use client'

import { useState, useRef, useCallback } from 'react'
import Image from 'next/image'
import { PlayIcon, EyeIcon, HeartIcon } from '@heroicons/react/24/solid'
import { Video } from '../../types/video'
import { useSwipeGestures } from '../../hooks/useSwipeGestures'

interface VideoCardProps {
  video: Video
  onClick?: (video: Video) => void
  onLike?: (videoId: string) => void
  onShare?: (videoId: string) => void
  className?: string
}

export default function VideoCard({
  video,
  onClick,
  onLike,
  onShare,
  className = ''
}: VideoCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null)

  const handleSwipeLeft = useCallback(() => {
    setSwipeDirection('left')
    setTimeout(() => {
      setSwipeDirection(null)
    }, 300)
    console.log('Dislike video:', video.id)
  }, [video.id])

  const handleSwipeRight = useCallback(() => {
    setSwipeDirection('right')
    setTimeout(() => {
      setSwipeDirection(null)
      if (onLike) {
        onLike(video.id)
      }
    }, 300)
  }, [video.id, onLike])

  const { elementRef, isDragging } = useSwipeGestures({
    onSwipeLeft: handleSwipeLeft,
    onSwipeRight: handleSwipeRight,
    threshold: 50
  })

  const handleClick = () => {
    if (onClick) {
      onClick(video)
    }
  }

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onLike) {
      onLike(video.id)
    }
  }

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onShare) {
      onShare(video.id)
    }
  }

  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`
    }
    return views.toString()
  }

  const formatLikes = (likes: number) => {
    if (likes >= 1000000) {
      return `${(likes / 1000000).toFixed(1)}M`
    } else if (likes >= 1000) {
      return `${(likes / 1000).toFixed(1)}K`
    }
    return likes.toString()
  }

  const getTimeAgo = (date?: Date | string | number) => {
    if (!date) return '2 days ago'
    
    // Convert string/number date to Date object if necessary
    let dateObj: Date
    if (typeof date === 'string') {
      dateObj = new Date(date)
    } else if (typeof date === 'number') {
      dateObj = new Date(date)
    } else if (date instanceof Date) {
      dateObj = date
    } else {
      return '2 days ago'
    }
    
    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return '2 days ago'
    
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)
    
    if (diffInSeconds < 60) return 'Just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}mo ago`
    return `${Math.floor(diffInSeconds / 31536000)}y ago`
  }

  return (
    <div
      ref={elementRef as React.RefObject<HTMLDivElement>}
      className={`bg-gray-900 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group relative ${
        swipeDirection === 'left' ? 'transform -translate-x-4 opacity-75' : 
        swipeDirection === 'right' ? 'transform translate-x-4 opacity-75' : ''
      } ${isDragging ? 'select-none' : ''} ${className}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Swipe Indicators */}
      {swipeDirection === 'left' && (
        <div className="absolute top-4 left-4 z-10 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
          👎 NOPE
        </div>
      )}
      {swipeDirection === 'right' && (
        <div className="absolute top-4 right-4 z-10 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
          ❤️ LIKE
        </div>
      )}
      {/* Video Thumbnail */}
      <div className="relative aspect-video bg-gray-200">
        {!imageError ? (
          <Image
            src={video.thumbnail}
            alt={video.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-300">
            <div className="text-gray-500 text-center">
              <div className="text-4xl mb-2">📹</div>
              <p className="text-sm">Thumbnail unavailable</p>
            </div>
          </div>
        )}

        {/* Duration Badge */}
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {video.duration}
        </div>

        {/* Play Button Overlay */}
        <div className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
          <div className="bg-black bg-opacity-50 rounded-full p-3">
            <PlayIcon className="w-8 h-8 text-white" />
          </div>
        </div>

        {/* IPFS Badge */}
        {video.ipfsHash && (
          <div className="absolute top-2 left-2 bg-purple-600 text-white text-xs px-2 py-1 rounded flex items-center">
            <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
            IPFS
          </div>
        )}
      </div>

      {/* Video Info */}
      <div className="p-4">
        <h3 className="font-semibold text-white mb-2 line-clamp-2 leading-tight">
          {video.title}
        </h3>
        
        <p className="text-purple-400 text-sm mb-2 hover:text-purple-300 transition-colors">
          @{video.creator}
        </p>
        
        <div className="flex items-center justify-between text-gray-400 text-xs">
          <div className="flex items-center space-x-3">
            <div className="flex items-center">
              <EyeIcon className="w-3 h-3 mr-1" />
              <span>{formatViews(video.views)}</span>
            </div>
            <div className="flex items-center">
              <HeartIcon className="w-3 h-3 mr-1 text-red-500" />
              <span>{formatLikes(video.likes)}</span>
            </div>
          </div>
          
          <span>{getTimeAgo(video.createdAt)}</span>
        </div>

        {/* Action Buttons */}
        <div className={`flex items-center justify-between mt-3 pt-3 border-t border-gray-700 transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
          <button
            onClick={handleLike}
            className="flex items-center space-x-1 text-gray-400 hover:text-red-500 transition-colors"
          >
            <HeartIcon className="w-4 h-4" />
            <span className="text-xs">Like</span>
          </button>
          
          <button
            onClick={handleShare}
            className="flex items-center space-x-1 text-gray-400 hover:text-blue-500 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            <span className="text-xs">Share</span>
          </button>
          
          <button className="flex items-center space-x-1 text-gray-400 hover:text-purple-500 transition-colors">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
            <span className="text-xs">Save</span>
          </button>
        </div>
      </div>
    </div>
  )
}