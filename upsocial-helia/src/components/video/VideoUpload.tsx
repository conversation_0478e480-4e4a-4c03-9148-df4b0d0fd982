'use client'

import React, { useState, useRef, useCallback } from 'react'
import { videoStorage } from '@/lib/ipfs/video-storage'
import { VideoMetadata } from '@/lib/ipfs/video-storage'

interface UploadProgress {
  loaded: number
  total: number
  percentage: number
  stage: 'uploading' | 'processing' | 'complete'
}

interface VideoUploadProps {
  onUploadComplete?: (metadata: VideoMetadata) => void
  onUploadError?: (error: string) => void
  maxFileSize?: number // in MB
  acceptedFormats?: string[]
}

export default function VideoUpload({
  onUploadComplete,
  onUploadError,
  maxFileSize = 500, // 500MB default
  acceptedFormats = ['video/mp4', 'video/webm', 'video/ogg', 'video/mov', 'video/avi']
}: VideoUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState<UploadProgress | null>(null)
  const [dragActive, setDragActive] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [metadata, setMetadata] = useState({
    title: '',
    description: '',
    tags: '',
    category: 'General'
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelection(e.dataTransfer.files[0])
    }
  }, [])

  const handleFileSelection = (file: File) => {
    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      onUploadError?.(`File type ${file.type} is not supported. Please use: ${acceptedFormats.join(', ')}`)
      return
    }

    // Validate file size
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxFileSize) {
      onUploadError?.(`File size (${fileSizeMB.toFixed(1)}MB) exceeds maximum limit of ${maxFileSize}MB`)
      return
    }

    setSelectedFile(file)
    
    // Auto-populate title if empty
    if (!metadata.title) {
      setMetadata(prev => ({
        ...prev,
        title: file.name.replace(/\.[^/.]+$/, '') // Remove file extension
      }))
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelection(e.target.files[0])
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    setProgress({ loaded: 0, total: selectedFile.size, percentage: 0, stage: 'uploading' })

    try {
      // Initialize Helia if not already done
      await videoStorage.initialize()

      const videoMetadata = {
        title: metadata.title || selectedFile.name,
        description: metadata.description,
        tags: metadata.tags.split(',').map(tag => tag.trim()).filter(Boolean),
        category: metadata.category,
        uploadedBy: 'current-user', // TODO: Get from auth context
        mimeType: selectedFile.type,
        originalFileName: selectedFile.name
      }

      const progressCallback = (progressData: any) => {
        setProgress({
          loaded: progressData.loaded || 0,
          total: progressData.total || selectedFile.size,
          percentage: progressData.percentage || 0,
          stage: progressData.stage || 'uploading'
        })
      }

      console.log('🎬 Starting video upload to IPFS...')
      const result = await videoStorage.uploadVideoWithMetadata(
        selectedFile,
        videoMetadata,
        progressCallback
      )

      setProgress({ 
        loaded: selectedFile.size, 
        total: selectedFile.size, 
        percentage: 100, 
        stage: 'complete' 
      })

      console.log('✅ Video upload completed:', result)
      onUploadComplete?.(result)

      // Reset form
      setSelectedFile(null)
      setMetadata({ title: '', description: '', tags: '', category: 'General' })
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

    } catch (error) {
      console.error('❌ Upload failed:', error)
      onUploadError?.(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
      setProgress(null)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-center">Upload Video to IPFS</h2>

      {/* File Drop Zone */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 bg-gray-50 hover:bg-gray-100'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.join(',')}
          onChange={handleFileInputChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          disabled={isUploading}
          title="Upload video file"
          aria-label="Upload video file"
        />
        
        <div className="space-y-4">
          <div className="text-4xl">🎬</div>
          <div>
            <p className="text-lg font-medium text-gray-700">
              Drop your video here or click to browse
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Supports: {acceptedFormats.map(format => format.split('/')[1]).join(', ')}
            </p>
            <p className="text-sm text-gray-500">
              Max size: {maxFileSize}MB
            </p>
          </div>
        </div>
      </div>

      {/* Selected File Info */}
      {selectedFile && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">Selected File:</h3>
          <div className="text-sm text-blue-700">
            <p><strong>Name:</strong> {selectedFile.name}</p>
            <p><strong>Size:</strong> {formatFileSize(selectedFile.size)}</p>
            <p><strong>Type:</strong> {selectedFile.type}</p>
          </div>
        </div>
      )}

      {/* Metadata Form */}
      {selectedFile && !isUploading && (
        <div className="mt-6 space-y-4">
          <h3 className="font-medium text-gray-800">Video Information:</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              value={metadata.title}
              onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter video title"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={metadata.description}
              onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe your video"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags (comma-separated)
            </label>
            <input
              type="text"
              value={metadata.tags}
              onChange={(e) => setMetadata(prev => ({ ...prev, tags: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="tech, tutorial, demo"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              value={metadata.category}
              onChange={(e) => setMetadata(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Select video category"
              aria-label="Select video category"
            >
              <option value="General">General</option>
              <option value="Education">Education</option>
              <option value="Entertainment">Entertainment</option>
              <option value="Technology">Technology</option>
              <option value="Music">Music</option>
              <option value="Sports">Sports</option>
              <option value="News">News</option>
              <option value="Gaming">Gaming</option>
            </select>
          </div>

          <button
            onClick={handleUpload}
            disabled={!metadata.title.trim() || isUploading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            Upload to IPFS
          </button>
        </div>
      )}

      {/* Upload Progress */}
      {progress && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>
              {progress.stage === 'uploading' && '📤 Uploading to IPFS...'}
              {progress.stage === 'processing' && '⚙️ Processing video...'}
              {progress.stage === 'complete' && '✅ Upload complete!'}
            </span>
            <span>{progress.percentage.toFixed(1)}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${progress.percentage}%`
              }}
            />
          </div>
          
          <div className="text-xs text-gray-500">
            {formatFileSize(progress.loaded)} / {formatFileSize(progress.total)}
          </div>
        </div>
      )}
    </div>
  )
}
