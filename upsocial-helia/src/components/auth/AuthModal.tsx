'use client'

import React, { useState } from 'react'
import { X } from 'lucide-react'
import type { AuthState } from '@/types'
import SignUp from './SignUp'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  onAuthSuccess?: (authState: AuthState) => void
}

export default function AuthModal({ isOpen, onClose, onAuthSuccess }: AuthModalProps) {
  const [currentPage, setCurrentPage] = useState('signup')

  if (!isOpen) return null

  const handleAuthSuccess = (authState: AuthState) => {
    onAuthSuccess?.(authState)
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="relative bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-auto">
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 right-4 z-10 text-gray-400 hover:text-gray-600 bg-white rounded-full p-1 shadow-lg"
          title="Close"
        >
          <X className="w-4 h-4" />
        </button>
        
        <div className="p-6">
          <SignUp 
            setFlag={setCurrentPage} 
            onAuthSuccess={handleAuthSuccess}
          />
        </div>
      </div>
    </div>
  )
}
