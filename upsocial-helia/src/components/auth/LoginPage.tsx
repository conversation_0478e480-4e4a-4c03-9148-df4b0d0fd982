'use client'

import { useState } from 'react'
import { useAuth } from '../../hooks/useAppOperations'

export default function LoginPage() {
  const [loginType, setLoginType] = useState<'password' | 'wallet' | 'anonymous'>('password')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [username, setUsername] = useState('')
  const [nickname, setNickname] = useState('')
  const [code, setCode] = useState('')
  const [isRegister, setIsRegister] = useState(false)
  const [isAnonymousLogin, setIsAnonymousLogin] = useState(false)

  const { register, login, connectWallet, createAnonymousSession, loginAnonymous } = useAuth()

  const handlePasswordAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isRegister) {
      await register(username, email, password)
    } else {
      await login(email, password)
    }
  }

  const handleWalletConnect = async () => {
    await connectWallet()
  }

  const handleAnonymousAuth = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (isAnonymousLogin) {
      await loginAnonymous(nickname, code)
    } else {
      await createAnonymousSession(nickname)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">UpSocial</h1>
          <p className="text-gray-600">Decentralized Social Media</p>
        </div>

        {/* Login Type Selector */}
        <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setLoginType('password')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              loginType === 'password' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600'
            }`}
          >
            Email
          </button>
          <button
            onClick={() => setLoginType('wallet')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              loginType === 'wallet' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600'
            }`}
          >
            Wallet
          </button>
          <button
            onClick={() => setLoginType('anonymous')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              loginType === 'anonymous' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600'
            }`}
          >
            Guest
          </button>
        </div>

        {/* Email/Password Login */}
        {loginType === 'password' && (
          <form onSubmit={handlePasswordAuth} className="space-y-4">
            {isRegister && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Username
                </label>
                <input
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              {isRegister ? 'Register' : 'Login'}
            </button>
            <button
              type="button"
              onClick={() => setIsRegister(!isRegister)}
              className="w-full text-blue-600 text-sm hover:underline"
            >
              {isRegister ? 'Already have an account? Login' : 'Need an account? Register'}
            </button>
          </form>
        )}

        {/* Wallet Login */}
        {loginType === 'wallet' && (
          <div className="space-y-4">
            <p className="text-gray-600 text-center mb-4">
              Connect your Web3 wallet to continue
            </p>
            <button
              onClick={handleWalletConnect}
              className="w-full bg-orange-500 text-white py-3 px-4 rounded-md hover:bg-orange-600 transition-colors flex items-center justify-center"
            >
              <span className="mr-2">🦊</span>
              Connect MetaMask
            </button>
          </div>
        )}

        {/* Anonymous Login */}
        {loginType === 'anonymous' && (
          <form onSubmit={handleAnonymousAuth} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nickname
              </label>
              <input
                type="text"
                value={nickname}
                onChange={(e) => setNickname(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            {isAnonymousLogin && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Access Code
                </label>
                <input
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            )}
            <button
              type="submit"
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
            >
              {isAnonymousLogin ? 'Continue with Code' : 'Get Access Code'}
            </button>
            <button
              type="button"
              onClick={() => setIsAnonymousLogin(!isAnonymousLogin)}
              className="w-full text-green-600 text-sm hover:underline"
            >
              {isAnonymousLogin ? 'Need a new code?' : 'Already have a code?'}
            </button>
          </form>
        )}

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Powered by IPFS & Helia</p>
          <p>Truly decentralized social media</p>
        </div>
      </div>
    </div>
  )
}
