'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AuthManager } from '@/lib/data/auth-manager'
import toast from 'react-hot-toast'

interface AnonymousProps {
  setFlag: (flag: string) => void
}

export default function Anonymous({ setFlag }: AnonymousProps) {
  const [nickname, setNickname] = useState('')
  const [loading, setLoading] = useState(false)

  const handleAnonymousLogin = async () => {
    if (!nickname.trim()) {
      toast.error('Please enter a nickname!')
      return
    }

    setLoading(true)
    try {
      const authManager = AuthManager.getInstance()
      const result = await authManager.createAnonymousSession(nickname)
      toast.success(`Welcome ${nickname}! Your session code is: ${result.code}`)
      window.location.reload()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Anonymous login failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-600 to-indigo-800 flex items-center justify-center p-4">
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Creating anonymous session...</p>
          </div>
        </div>
      )}

      <div className="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl p-8 w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <Image
            src="/assets/logos/logo.png"
            alt="UpSocial"
            width={200}
            height={50}
            className="mx-auto"
          />
        </div>

        {/* Back Button and Title */}
        <div className="flex items-center mb-6">
          <button
            onClick={() => setFlag('SignUp')}
            className="text-white mr-3"
            title="Go Back"
          >
            ←
          </button>
          <h2 className="text-white text-xl font-semibold">Anonymous Account</h2>
        </div>

        <div className="space-y-6">
          <div className="text-center text-white">
            <p className="mb-4">
              Create a temporary anonymous account to explore UpSocial.
            </p>
            <p className="text-sm text-gray-300">
              Note: Anonymous accounts are temporary and will be lost when you close the browser.
            </p>
          </div>

          <input
            type="text"
            placeholder="Enter a nickname"
            value={nickname}
            onChange={(e) => setNickname(e.target.value)}
            className="w-full px-4 py-3 bg-transparent border-b-2 border-blue-300 text-white placeholder-gray-300 focus:outline-none focus:border-white"
            maxLength={20}
          />

          <button
            onClick={handleAnonymousLogin}
            className="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 rounded-lg"
          >
            CREATE ANONYMOUS ACCOUNT
          </button>

          <div className="text-center">
            <div className="text-white mb-2">Already have an account?</div>
            <div className="space-x-4">
              <button
                onClick={() => setFlag('SignUp')}
                className="text-blue-300 underline"
              >
                Sign In
              </button>
              <span className="text-white">or</span>
              <button
                onClick={() => setFlag('SignUp')}
                className="text-blue-300 underline"
              >
                Sign Up
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
