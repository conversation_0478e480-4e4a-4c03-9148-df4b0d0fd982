'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { AuthManager } from '@/lib/data/auth-manager'
import toast from 'react-hot-toast'

interface SignUpProps {
  setFlag: (flag: string) => void
  onAuthSuccess?: (authState: any) => void
}

export default function SignUp({ setFlag, onAuthSuccess }: SignUpProps) {
  const [isLogin, setIsLogin] = useState(false)
  const [loading, setLoading] = useState(false)
  const [authReady, setAuthReady] = useState(false)
  const [initError, setInitError] = useState<string | null>(null)
  
  // Registration fields
  const [username, setUsername] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [emailValid, setEmailValid] = useState(true)
  const [passwordValid, setPasswordValid] = useState({
    length: true,
    number: true,
    capital: true
  })

  // Login fields
  const [loginEmail, setLoginEmail] = useState('')
  const [loginPassword, setLoginPassword] = useState('')

  // Initialize authentication system
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('Initializing authentication system...')
        const authManager = AuthManager.getInstance()
        await authManager.ensureInitialized()
        setAuthReady(true)
        setInitError(null)
        console.log('Authentication system ready')
      } catch (error) {
        console.error('Auth initialization failed:', error)
        setInitError(error instanceof Error ? error.message : 'Failed to initialize authentication')
        setAuthReady(false)
      }
    }

    initAuth()
  }, [])

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string) => {
    return {
      length: password.length >= 8,
      number: /[0-9]/.test(password),
      capital: /[A-Z]/.test(password)
    }
  }

  const handleEmailChange = (value: string) => {
    setEmail(value)
    setEmailValid(validateEmail(value) || value === '')
  }

  const handlePasswordChange = (value: string) => {
    setPassword(value)
    setPasswordValid(validatePassword(value))
  }

  const handleRegister = async () => {
    console.log('🔥 REGISTER BUTTON CLICKED!', { authReady, username, email, password });
    
    if (!authReady) {
      console.log('❌ Auth not ready');
      toast.error('Authentication system not ready. Please wait and try again.')
      return
    }

    if (!username.trim()) {
      console.log('❌ No username');
      toast.error('Please input username!')
      return
    }
    if (!email.trim()) {
      console.log('❌ No email');
      toast.error('Please input email!')
      return
    }
    if (!password.trim()) {
      console.log('❌ No password');
      toast.error('Please input password!')
      return
    }
    if (!emailValid) {
      console.log('❌ Invalid email');
      toast.error('Please enter a valid email!')
      return
    }
    if (!passwordValid.length || !passwordValid.number || !passwordValid.capital) {
      console.log('❌ Invalid password format');
      toast.error('Password must meet requirements!')
      return
    }

    console.log('✅ All validations passed, starting registration...');
    setLoading(true)
    try {
      console.log('Starting registration process...')
      const authManager = AuthManager.getInstance()
      const result = await authManager.registerWithPassword(username, email, password)
      console.log('✅ Registration successful:', result);
      toast.success(`Registration successful! Welcome ${result.profile.username}`)
      console.log('Registration successful, calling onAuthSuccess...')
      
      // Call onAuthSuccess if provided, otherwise reload
      if (onAuthSuccess) {
        onAuthSuccess({ isAuthenticated: true, user: result.profile })
      } else {
        setTimeout(() => window.location.reload(), 1000)
      }
    } catch (error) {
      console.error('❌ Registration error:', error)
      toast.error(error instanceof Error ? error.message : 'Registration failed')
    } finally {
      setLoading(false)
    }
  }

  const handleLogin = async () => {
    if (!authReady) {
      toast.error('Authentication system not ready. Please wait and try again.')
      return
    }

    if (!loginEmail.trim()) {
      toast.error('Please input email!')
      return
    }
    if (!loginPassword.trim()) {
      toast.error('Please input password!')
      return
    }

    setLoading(true)
    try {
      console.log('Starting login process...')
      const authManager = AuthManager.getInstance()
      
      // Add retry logic for IPFS-related failures
      let retryCount = 0
      const maxRetries = 3
      
      while (retryCount < maxRetries) {
        try {
          await authManager.loginWithPassword(loginEmail, loginPassword)
          toast.success('Login successful!')
          console.log('Login successful, calling onAuthSuccess...')
          
          // Call onAuthSuccess if provided, otherwise reload
          if (onAuthSuccess) {
            const currentAuth = authManager.getCurrentAuth()
            onAuthSuccess(currentAuth)
          } else {
            setTimeout(() => window.location.reload(), 1000)
          }
          return
        } catch (error) {
          retryCount++
          const errorMessage = error instanceof Error ? error.message : 'Login failed'
          
          // If it's a credential error, don't retry
          if (errorMessage.includes('Invalid password') || 
              errorMessage.includes('User not found') || 
              errorMessage.includes('Account is disabled')) {
            throw error
          }
          
          // If it's an IPFS/network error and we have retries left, try again
          if (retryCount < maxRetries && 
              (errorMessage.includes('retrieve') || 
               errorMessage.includes('network') || 
               errorMessage.includes('timeout'))) {
            console.log(`Login attempt ${retryCount} failed, retrying...`)
            toast.error(`Connection issue. Retrying... (${retryCount}/${maxRetries})`)
            await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds before retry
            continue
          }
          
          throw error
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Login failed'
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleWalletConnect = async () => {
    if (!authReady) {
      toast.error('Authentication system not ready. Please wait and try again.')
      return
    }

    setLoading(true)
    try {
      console.log('Starting wallet connection...')
      const authManager = AuthManager.getInstance()
      await authManager.connectWallet()
      toast.success('Wallet connected successfully!')
      console.log('Wallet connection successful, calling onAuthSuccess...')
      
      // Call onAuthSuccess if provided, otherwise reload
      if (onAuthSuccess) {
        const currentAuth = authManager.getCurrentAuth()
        onAuthSuccess(currentAuth)
      } else {
        setTimeout(() => window.location.reload(), 1000)
      }
    } catch (error) {
      console.error('Wallet connection error:', error)
      toast.error(error instanceof Error ? error.message : 'Wallet connection failed')
    } finally {
      setLoading(false)
    }
  }

  const handleRetryInit = async () => {
    setInitError(null)
    setAuthReady(false)
    
    try {
      console.log('Retrying authentication initialization...')
      const authManager = AuthManager.getInstance()
      await authManager.ensureInitialized()
      setAuthReady(true)
      toast.success('Authentication system initialized successfully')
    } catch (error) {
      console.error('Retry initialization failed:', error)
      setInitError(error instanceof Error ? error.message : 'Failed to initialize authentication')
    }
  }

  const handleTestAPI = async () => {
    try {
      console.log('Testing API fallback...')
      const response = await fetch('/api/test-ipfs')
      const result = await response.json()
      console.log('API test result:', result)
      toast.success('API test completed - check console')
    } catch (error) {
      console.error('API test failed:', error)
      toast.error('API test failed')
    }
  }

  return (
    <div className="bg-white rounded-lg p-6">
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Processing...</p>
          </div>
        </div>
      )}

      {/* Logo */}
      <div className="text-center mb-8">
        <div className="text-3xl font-bold text-purple-600 mb-2">UpSocial</div>
      </div>

        {/* Initialization Status */}
        {!authReady && !initError && (
          <div className="text-center mb-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto mb-2"></div>
            <p className="text-gray-600 text-sm">Initializing authentication system...</p>
          </div>
        )}

        {initError && (
          <div className="mb-6 p-4 bg-red-50 rounded-lg border border-red-200">
            <p className="text-red-600 text-sm mb-2">{initError}</p>
            <div className="space-x-2">
              <button
                onClick={handleRetryInit}
                className="bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-1 rounded"
              >
                Retry
              </button>
              <button
                onClick={handleTestAPI}
                className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1 rounded"
              >
                Test API
              </button>
            </div>
          </div>
        )}

        {authReady && !isLogin ? (
          // Registration Form
          <form className="space-y-4" onSubmit={(e) => { e.preventDefault(); handleRegister(); }}>
            {/* Social Login Buttons */}
            <div className="flex justify-center space-x-4 mb-6">
              <button type="button" className="bg-blue-600 p-3 rounded-full text-white">f</button>
              <button type="button" className="bg-blue-400 p-3 rounded-full text-white">t</button>
              <button type="button" className="bg-pink-500 p-3 rounded-full text-white">d</button>
            </div>
            
            <p className="text-white text-center mb-6">or be classical</p>

            <input
              type="text"
              placeholder="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-4 py-3 border-b-2 border-gray-200 text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent"
              required
            />

            <input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => handleEmailChange(e.target.value)}
              className={`w-full px-4 py-3 border-b-2 ${
                emailValid ? 'border-gray-200' : 'border-red-500'
              } text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent`}
              required
            />
            {!emailValid && <p className="text-red-500 text-sm">Invalid Email Address!</p>}

            <input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => handlePasswordChange(e.target.value)}
              autoComplete="new-password"
              className={`w-full px-4 py-3 border-b-2 ${
                passwordValid.length && passwordValid.number && passwordValid.capital 
                  ? 'border-gray-200' 
                  : 'border-red-500'
              } text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent`}
              required
            />
            
            {password && (
              <div className="space-y-1">
                {!passwordValid.length && <p className="text-red-500 text-sm">Password must have more than 8 characters!</p>}
                {!passwordValid.number && <p className="text-red-500 text-sm">Password must have a number!</p>}
                {!passwordValid.capital && <p className="text-red-500 text-sm">Password must have a capital letter!</p>}
              </div>
            )}

            <button
              type="submit"
              disabled={!authReady || loading}
              className={`w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-3 rounded-lg mt-6 ${authReady && !loading ? 'cursor-pointer' : 'cursor-not-allowed'}`}
            >
              {loading ? 'PROCESSING...' : authReady ? 'SIGN UP' : 'PLEASE WAIT...'}
            </button>

            <div className="text-center mt-4">
              <span className="text-gray-600">Already have an account? </span>
              <button
                type="button"
                onClick={() => setIsLogin(true)}
                className="text-purple-600 underline"
              >
                Sign In
              </button>
            </div>
          </form>
        ) : authReady ? (
          // Login Form
          <form className="space-y-4" onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
            <input
              type="email"
              placeholder="Email"
              value={loginEmail}
              onChange={(e) => setLoginEmail(e.target.value)}
              autoComplete="email"
              className="w-full px-4 py-3 border-b-2 border-gray-200 text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent"
              required
            />

            <input
              type="password"
              placeholder="Password"
              value={loginPassword}
              onChange={(e) => setLoginPassword(e.target.value)}
              autoComplete="current-password"
              className="w-full px-4 py-3 border-b-2 border-gray-200 text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent"
              required
            />

            <button
              type="submit"
              disabled={!authReady || loading}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-3 rounded-lg mt-6"
            >
              {loading ? 'SIGNING IN...' : 'SIGN IN'}
            </button>

            <div className="text-center mt-4">
              <span className="text-gray-600">Don&apos;t have account? </span>
              <button
                type="button"
                onClick={() => setIsLogin(false)}
                className="text-purple-600 underline"
              >
                SIGN UP
              </button>
            </div>
          </form>
        ) : null}
      </div>
    </div>
  )
          </form>
        ) : authReady ? (
          // Login Form
          <form className="space-y-4" onSubmit={(e) => { e.preventDefault(); handleLogin(); }}>
            <input
              type="email"
              placeholder="Email"
              value={loginEmail}
              onChange={(e) => setLoginEmail(e.target.value)}
              autoComplete="email"
              className="w-full px-4 py-3 bg-transparent border-b-2 border-blue-300 text-white placeholder-gray-300 focus:outline-none focus:border-white"
              required
            />

            <input
              type="password"
              placeholder="Password"
              value={loginPassword}
              onChange={(e) => setLoginPassword(e.target.value)}
              autoComplete="current-password"
              className="w-full px-4 py-3 bg-transparent border-b-2 border-blue-300 text-white placeholder-gray-300 focus:outline-none focus:border-white"
              required
            />

            <div className="text-right">
              <button type="button" className="text-blue-300 underline text-sm">
                Forgot Password?
              </button>
            </div>

            <button
              type="submit"
              disabled={!authReady || loading}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white font-bold py-3 rounded-lg mt-6"
            >
              {loading ? 'SIGNING IN...' : 'SIGN IN'}
            </button>

            <button
              type="button"
              onClick={handleWalletConnect}
              disabled={!authReady || loading}
              className="w-full bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 text-white font-bold py-3 rounded-lg mt-4"
            >
              🔗 Connect Wallet
            </button>

            <button
              type="button"
              onClick={() => setFlag('Anonymous')}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 rounded-lg mt-4"
            >
              Anonymous account
            </button>

            <div className="text-center mt-4">
              <span className="text-white">Don&apos;t have account? </span>
              <button
                type="button"
                onClick={() => setIsLogin(false)}
                className="text-blue-300 underline"
              >
                SIGN UP
              </button>
            </div>

            {/* Debug Test Button */}
            <button
              onClick={handleTestAPI}
              type="button"
              className="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 rounded-lg mt-4 text-sm"
            >
              🔧 Test API Gateway
            </button>
          </form>
        ) : null}
      </div>
    </div>
  )
}
