'use client'

import React, { useState } from 'react'
import { X } from 'lucide-react'
import type { AuthState } from '@/types'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  onAuthSuccess?: (authState: AuthState) => void
}

export default function AuthModal({ isOpen, onClose, onAuthSuccess }: AuthModalProps) {
  const [isLogin, setIsLogin] = useState(false)
  const [username, setUsername] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    
    try {
      // Mock authentication - replace with real auth logic
      const mockUser = {
        id: '1',
        username: username || email.split('@')[0],
        email: email
      }
      
      const authState: AuthState = {
        isAuthenticated: true,
        profile: {
          id: '1',
          username: username || email.split('@')[0],
          email: email,
          status: true,
          handle: username || email.split('@')[0],
          description: '',
          location: '',
          following: [],
          followers: [],
          liked: [],
          disliked: [],
          history: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      
      if (onAuthSuccess) {
        onAuthSuccess(authState)
      }
      
      onClose()
    } catch (error) {
      console.error('Auth error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="relative bg-white rounded-lg max-w-md w-full p-6">
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          title="Close"
        >
          <X className="w-4 h-4" />
        </button>
        
        {/* Logo */}
        <div className="text-center mb-8">
          <div className="text-3xl font-bold text-purple-600 mb-2">UpSocial</div>
        </div>

        {/* Social Login Buttons */}
        <div className="flex justify-center space-x-4 mb-6">
          <button type="button" className="bg-blue-600 p-3 rounded-full text-white w-12 h-12 flex items-center justify-center">
            f
          </button>
          <button type="button" className="bg-blue-400 p-3 rounded-full text-white w-12 h-12 flex items-center justify-center">
            t
          </button>
          <button type="button" className="bg-pink-500 p-3 rounded-full text-white w-12 h-12 flex items-center justify-center">
            d
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {!isLogin && (
            <input
              type="text"
              placeholder="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="w-full px-4 py-3 border-b-2 border-gray-200 text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent"
              required={!isLogin}
            />
          )}
          
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 border-b-2 border-gray-200 text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent"
            required
          />

          <input
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-4 py-3 border-b-2 border-gray-200 text-gray-700 placeholder-gray-400 focus:outline-none focus:border-purple-600 bg-transparent"
            required
          />

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-bold py-3 rounded-lg mt-6"
          >
            {loading ? 'PROCESSING...' : isLogin ? 'SIGN IN' : 'SIGN UP'}
          </button>

          <div className="text-center mt-4">
            <span className="text-gray-600">
              {isLogin ? "Don't have an account? " : "Already have an account? "}
            </span>
            <button
              type="button"
              onClick={() => setIsLogin(!isLogin)}
              className="text-purple-600 underline"
            >
              {isLogin ? 'Sign Up' : 'Sign In'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
