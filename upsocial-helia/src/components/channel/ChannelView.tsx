'use client'

import { useState, useEffect } from 'react'
import { ArrowLeftIcon, UserPlusIcon, UserMinusIcon, PlayIcon, EyeIcon, HeartIcon, ShareIcon } from '@heroicons/react/24/outline'

interface Channel {
  id: string
  channelName: string
  description: string
  photo: string
  followers: string[]
  contents: VideoContent[]
  createdDate: string
  email: string
  isPersonalProfile?: boolean
}

interface VideoContent {
  ID: string
  title: string
  description: string
  ipfsUrl: string
  thumbnail: string
  postDate: string
  liked: number
  watched: number
  shared: number
  duration: string
  category: string
}

interface ChannelViewProps {
  channel: Channel
  currentUserEmail: string
  onBack: () => void
  onVideoPlay?: (video: VideoContent) => void
  className?: string
}

export default function ChannelView({
  channel,
  currentUserEmail,
  onBack,
  onVideoPlay,
  className = ''
}: ChannelViewProps) {
  const [isFollowing, setIsFollowing] = useState(false)
  const [followerCount, setFollowerCount] = useState(channel.followers.length)
  const [videos, setVideos] = useState<VideoContent[]>(channel.contents || [])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setIsFollowing(channel.followers.includes(currentUserEmail))
    loadChannelVideos()
  }, [channel.id, currentUserEmail])

  const loadChannelVideos = async () => {
    setLoading(true)
    try {
      // Mock video data - replace with actual data from Helia/IPFS
      const mockVideos: VideoContent[] = [
        {
          ID: '1',
          title: 'Introduction to Web3 Development',
          description: 'Learn the basics of decentralized web development',
          ipfsUrl: 'https://g.upsocial.com/ipfs/QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
          thumbnail: '/assets/thumb-placeholder.jpg',
          postDate: new Date().toISOString(),
          liked: 245,
          watched: 1234,
          shared: 56,
          duration: '12:34',
          category: 'Education'
        },
        {
          ID: '2',
          title: 'Building on IPFS',
          description: 'How to store and retrieve data from IPFS',
          ipfsUrl: 'https://g.upsocial.com/ipfs/Qmd9jWF4ajEop3AyJirP4q2N8nFzL5GyeoB75pTqRPSAUr',
          thumbnail: '/assets/thumb-placeholder.jpg',
          postDate: new Date(Date.now() - 86400000).toISOString(),
          liked: 189,
          watched: 876,
          shared: 43,
          duration: '8:45',
          category: 'Science & Technology'
        }
      ]
      
      setVideos(mockVideos)
    } catch (error) {
      console.error('Failed to load channel videos:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFollowToggle = async () => {
    try {
      if (isFollowing) {
        setIsFollowing(false)
        setFollowerCount(prev => prev - 1)
        // TODO: Unfollow channel in Helia/IPFS
      } else {
        setIsFollowing(true)
        setFollowerCount(prev => prev + 1)
        // TODO: Follow channel in Helia/IPFS
      }
    } catch (error) {
      console.error('Failed to toggle follow:', error)
      // Revert on error
      setIsFollowing(prev => !prev)
      setFollowerCount(prev => isFollowing ? prev + 1 : prev - 1)
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatViews = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="relative">
        {/* Cover Image */}
        <div className="h-48 bg-gradient-to-r from-purple-600 to-pink-600"></div>
        
        {/* Back Button */}
        <button
          onClick={onBack}
          className="absolute top-4 left-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-opacity"
        >
          <ArrowLeftIcon className="w-5 h-5" />
        </button>

        {/* Channel Info */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-6">
          <div className="flex items-end space-x-4">
            {/* Channel Avatar */}
            <div className="w-24 h-24 bg-white rounded-full overflow-hidden border-4 border-white shadow-lg flex-shrink-0">
              <img
                src={channel.photo}
                alt={channel.channelName}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.src = '/assets/logos/logo.png'
                }}
              />
            </div>
            
            {/* Channel Details */}
            <div className="flex-1 text-white min-w-0">
              <h1 className="text-3xl font-bold mb-2 truncate">
                {channel.channelName}
                {channel.isPersonalProfile && (
                  <span className="ml-2 text-sm bg-blue-500 px-2 py-1 rounded-full">
                    Personal
                  </span>
                )}
              </h1>
              <p className="text-gray-300 mb-2 line-clamp-2">
                {channel.description || 'No description available'}
              </p>
              <div className="flex items-center space-x-4 text-sm text-gray-300">
                <span>{followerCount} followers</span>
                <span>{videos.length} videos</span>
                <span>Joined {formatDate(channel.createdDate)}</span>
              </div>
            </div>
            
            {/* Follow/Unfollow Button */}
            {channel.email !== currentUserEmail && (
              <button
                onClick={handleFollowToggle}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
                  isFollowing
                    ? 'bg-gray-600 text-white hover:bg-gray-700'
                    : 'bg-purple-600 text-white hover:bg-purple-700'
                }`}
              >
                {isFollowing ? (
                  <>
                    <UserMinusIcon className="w-5 h-5" />
                    <span>Unfollow</span>
                  </>
                ) : (
                  <>
                    <UserPlusIcon className="w-5 h-5" />
                    <span>Follow</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Videos Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Videos</h2>
            <span className="text-gray-600">{videos.length} videos</span>
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : videos.length === 0 ? (
            <div className="text-center py-12">
              <PlayIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No videos yet</h3>
              <p className="text-gray-600">This channel hasn't uploaded any videos yet.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {videos.map((video) => (
                <div
                  key={video.ID}
                  className="bg-gray-50 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200 cursor-pointer group"
                  onClick={() => onVideoPlay?.(video)}
                >
                  {/* Video Thumbnail */}
                  <div className="relative aspect-video bg-gray-200">
                    <img
                      src={video.thumbnail}
                      alt={video.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.src = '/assets/thumb-placeholder.jpg'
                      }}
                    />
                    
                    {/* Play Button Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity flex items-center justify-center">
                      <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <PlayIcon className="w-6 h-6 text-gray-900 ml-1" />
                      </div>
                    </div>

                    {/* Duration */}
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                  </div>

                  {/* Video Info */}
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {video.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {video.description}
                    </p>

                    {/* Video Stats */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-1">
                          <EyeIcon className="w-3 h-3" />
                          <span>{formatViews(video.watched)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <HeartIcon className="w-3 h-3" />
                          <span>{formatViews(video.liked)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <ShareIcon className="w-3 h-3" />
                          <span>{formatViews(video.shared)}</span>
                        </div>
                      </div>
                      <span>{formatDate(video.postDate)}</span>
                    </div>

                    {/* Category */}
                    <div className="mt-2">
                      <span className="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                        {video.category}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}