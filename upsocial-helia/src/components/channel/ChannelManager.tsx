'use client'

import { useState, useEffect } from 'react'
import { PlusIcon, TrashIcon, PencilIcon, UserGroupIcon, EyeIcon } from '@heroicons/react/24/outline'

interface Channel {
  id: string
  channelName: string
  description: string
  photo: string
  followers: string[]
  contents: any[]
  createdDate: string
  email: string
  isPersonalProfile: boolean
}

interface ChannelManagerProps {
  userEmail: string
  onChannelSelect?: (channel: Channel) => void
  className?: string
}

export default function ChannelManager({ 
  userEmail, 
  onChannelSelect,
  className = '' 
}: ChannelManagerProps) {
  const [channels, setChannels] = useState<Channel[]>([])
  const [isCreating, setIsCreating] = useState(false)
  const [editingChannel, setEditingChannel] = useState<Channel | null>(null)
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    channelName: '',
    description: '',
    photo: ''
  })

  useEffect(() => {
    loadChannels()
  }, [userEmail])

  const loadChannels = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual Helia/IPFS data
      const mockChannels: Channel[] = [
        {
          id: '1',
          channelName: 'Personal Profile',
          description: 'My personal channel for sharing daily content',
          photo: '/assets/profile.png',
          followers: ['<EMAIL>', '<EMAIL>'],
          contents: [],
          createdDate: new Date().toISOString(),
          email: userEmail,
          isPersonalProfile: true
        },
        {
          id: '2',
          channelName: 'Tech Tutorials',
          description: 'Educational content about web3 and blockchain technology',
          photo: '/assets/logos/logo.png',
          followers: ['<EMAIL>', '<EMAIL>'],
          contents: [],
          createdDate: new Date().toISOString(),
          email: userEmail,
          isPersonalProfile: false
        }
      ]
      
      setChannels(mockChannels)
    } catch (error) {
      console.error('Failed to load channels:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateChannel = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.channelName.trim()) return

    try {
      const newChannel: Channel = {
        id: Date.now().toString(),
        channelName: formData.channelName,
        description: formData.description,
        photo: formData.photo || '/assets/logos/logo.png',
        followers: [],
        contents: [],
        createdDate: new Date().toISOString(),
        email: userEmail,
        isPersonalProfile: false
      }

      setChannels(prev => [...prev, newChannel])
      setFormData({ channelName: '', description: '', photo: '' })
      setIsCreating(false)
      
      // TODO: Save to Helia/IPFS
    } catch (error) {
      console.error('Failed to create channel:', error)
    }
  }

  const handleUpdateChannel = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!editingChannel || !formData.channelName.trim()) return

    try {
      const updatedChannel = {
        ...editingChannel,
        channelName: formData.channelName,
        description: formData.description,
        photo: formData.photo || editingChannel.photo
      }

      setChannels(prev => prev.map(channel => 
        channel.id === editingChannel.id ? updatedChannel : channel
      ))
      
      setEditingChannel(null)
      setFormData({ channelName: '', description: '', photo: '' })
      
      // TODO: Update in Helia/IPFS
    } catch (error) {
      console.error('Failed to update channel:', error)
    }
  }

  const handleDeleteChannel = async (channelId: string) => {
    if (!confirm('Are you sure you want to delete this channel? This action cannot be undone.')) {
      return
    }

    try {
      setChannels(prev => prev.filter(channel => channel.id !== channelId))
      // TODO: Delete from Helia/IPFS
    } catch (error) {
      console.error('Failed to delete channel:', error)
    }
  }

  const startEditing = (channel: Channel) => {
    setEditingChannel(channel)
    setFormData({
      channelName: channel.channelName,
      description: channel.description,
      photo: channel.photo
    })
  }

  const cancelEditing = () => {
    setEditingChannel(null)
    setIsCreating(false)
    setFormData({ channelName: '', description: '', photo: '' })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">My Channels</h2>
            <p className="text-gray-600 mt-1">Manage your content channels</p>
          </div>
          
          {!isCreating && !editingChannel && (
            <button
              onClick={() => setIsCreating(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
              <span>New Channel</span>
            </button>
          )}
        </div>
      </div>

      {/* Create/Edit Form */}
      {(isCreating || editingChannel) && (
        <div className="p-6 border-b border-gray-200 bg-gray-50">
          <form onSubmit={editingChannel ? handleUpdateChannel : handleCreateChannel}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Channel Name *
                </label>
                <input
                  type="text"
                  value={formData.channelName}
                  onChange={(e) => setFormData(prev => ({ ...prev, channelName: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Enter channel name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Channel Image URL
                </label>
                <input
                  type="url"
                  value={formData.photo}
                  onChange={(e) => setFormData(prev => ({ ...prev, photo: e.target.value }))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="https://example.com/image.jpg"
                />
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 resize-vertical"
                placeholder="Describe your channel..."
              />
            </div>
            
            <div className="flex space-x-3">
              <button
                type="submit"
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                {editingChannel ? 'Update Channel' : 'Create Channel'}
              </button>
              
              <button
                type="button"
                onClick={cancelEditing}
                className="px-6 py-2 text-gray-600 hover:text-gray-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Channels List */}
      <div className="p-6">
        {channels.length === 0 ? (
          <div className="text-center py-12">
            <UserGroupIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No channels yet</h3>
            <p className="text-gray-600 mb-4">Create your first channel to start organizing your content</p>
            <button
              onClick={() => setIsCreating(true)}
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Create Channel
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {channels.map((channel) => (
              <div
                key={channel.id}
                className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-all duration-200 border-2 border-transparent hover:border-purple-200"
              >
                {/* Channel Image */}
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-16 h-16 bg-purple-100 rounded-full overflow-hidden flex-shrink-0">
                    <img
                      src={channel.photo}
                      alt={channel.channelName}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.src = '/assets/logos/logo.png'
                      }}
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {channel.channelName}
                      {channel.isPersonalProfile && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          Personal
                        </span>
                      )}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {channel.description || 'No description'}
                    </p>
                  </div>
                </div>

                {/* Channel Stats */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <UserGroupIcon className="w-4 h-4" />
                      <span>{channel.followers.length} followers</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <EyeIcon className="w-4 h-4" />
                      <span>{channel.contents.length} videos</span>
                    </div>
                  </div>
                </div>

                {/* Channel Actions */}
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => onChannelSelect?.(channel)}
                    className="px-4 py-2 text-purple-600 hover:text-purple-700 font-medium transition-colors"
                  >
                    View Channel
                  </button>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => startEditing(channel)}
                      className="p-2 text-gray-600 hover:text-gray-700 transition-colors"
                      title="Edit channel"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    
                    {!channel.isPersonalProfile && (
                      <button
                        onClick={() => handleDeleteChannel(channel.id)}
                        className="p-2 text-red-600 hover:text-red-700 transition-colors"
                        title="Delete channel"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}