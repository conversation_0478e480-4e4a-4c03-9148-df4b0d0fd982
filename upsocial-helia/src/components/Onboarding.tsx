'use client'

import { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import Image from 'next/image'
import styles from '../styles/Onboarding.module.css'
import { useTouchHandler } from '../hooks/useTouchHandler'

interface OnboardingProps {
  setFlag: (flag: string) => void
}

export default function Onboarding({ setFlag }: OnboardingProps) {
  const [count, setCount] = useState(0)
  const [searchFlag, setSearchFlag] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [isTransitioning, setIsTransitioning] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const lastScrollTime = useRef<number>(0)

  const videoDatas = useMemo(() => [
    // Primary IPFS sources
    'https://g.upsocial.com/ipfs/QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
    'https://g.upsocial.com/ipfs/Qmd9jWF4ajEop3AyJirP4q2N8nFzL5GyeoB75pTqRPSAUr',
    'https://g.upsocial.com/ipfs/QmUYUkvJFCpdt3dKqhGkAX9cpi3PydC2hvVHSTv1RYQQUS',
    'https://g.upsocial.com/ipfs/QmaeDhrZPgxQ3qypFdzqFos1gu7QnotfghYCPYjP2pVMNJ',
    'https://g.upsocial.com/ipfs/QmaR5ovcPp1s7urguih9ZNfd1Dt97tqEaMSHsQfLyhiCX4',
    'https://g.upsocial.com/ipfs/QmfPLvtyGxgiNvw4Kp17tyuyX8ykptNbcFZKc6Mj7HkVGi',
    'https://g.upsocial.com/ipfs/QmPLkLnSNUD5AzqbYsPKrmwETRcshuy3Y1Yvv5WAPhfi6x'
  ], [])

  const fallbackVideos = useMemo(() => [
    // Fallback test videos (using Big Buck Bunny sample videos)
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
    'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4'
  ], [])

  const nextVideo = useCallback(() => {
    if (isTransitioning) return
    setIsTransitioning(true)
    setCount(prev => (prev + 1) % videoDatas.length)
    setTimeout(() => setIsTransitioning(false), 500)
  }, [isTransitioning, videoDatas.length])

  const prevVideo = useCallback(() => {
    if (isTransitioning) return
    setIsTransitioning(true)
    setCount(prev => (prev - 1 + videoDatas.length) % videoDatas.length)
    setTimeout(() => setIsTransitioning(false), 500)
  }, [isTransitioning, videoDatas.length])

  // Use the enhanced touch handler
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useTouchHandler(nextVideo, prevVideo)

  const handleScroll = useCallback((e: React.WheelEvent) => {
    e.preventDefault()
    const now = Date.now()
    if (now - lastScrollTime.current < 800) return // Throttle scrolling
    lastScrollTime.current = now

    if (e.deltaY > 0) {
      // Scroll down - next video
      nextVideo()
    } else if (e.deltaY < 0) {
      // Scroll up - previous video
      prevVideo()
    }
  }, [nextVideo, prevVideo])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault()
      prevVideo()
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      nextVideo()
    } else if (e.key === ' ') {
      e.preventDefault()
      nextVideo()
    }
  }, [nextVideo, prevVideo])

  useEffect(() => {
    if (videoRef.current) {
      const video = videoRef.current
      const primarySource = videoDatas[count]
      const fallbackSource = fallbackVideos[count]
      
      video.src = primarySource
      
      const playVideo = async () => {
        try {
          await video.play()
        } catch (error) {
          console.error('Primary video play failed, trying fallback:', error)
          // Try fallback video
          try {
            video.src = fallbackSource
            await video.play()
          } catch (fallbackError) {
            console.error('Fallback video play failed:', fallbackError)
          }
        }
      }
      
      // Handle video error event
      const handleVideoError = () => {
        console.log('Video error, switching to fallback')
        video.src = fallbackSource
        video.play().catch(err => console.error('Fallback video failed:', err))
      }
      
      video.addEventListener('error', handleVideoError)
      playVideo()
      
      return () => {
        video.removeEventListener('error', handleVideoError)
      }
    }
  }, [count, videoDatas, fallbackVideos])

  useEffect(() => {
    // Focus the container for keyboard events
    if (containerRef.current) {
      containerRef.current.focus()
    }
  }, [])

  return (
    <div 
      ref={containerRef}
      className={`relative h-screen bg-gradient-to-br from-blue-400 via-purple-600 to-indigo-800 overflow-hidden select-none ${styles.container}`}
      onWheel={handleScroll}
      onKeyDown={handleKeyDown}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      tabIndex={0}
    >
      {/* Top Bar */}
      <div className="absolute top-0 left-0 w-full bg-black bg-opacity-20 z-20 p-4">
        <div className="flex justify-between items-center">
          <Image
            src="/assets/logos/logo.png"
            alt="UpSocial Logo"
            width={120}
            height={30}
            className="h-8 w-auto"
            style={{ width: 'auto', height: '32px' }}
          />
          <button
            onClick={() => setSearchFlag(!searchFlag)}
            className="text-white p-2 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
            title="Search"
          >
            🔍
          </button>
        </div>
        
        {searchFlag && (
          <div className={`mt-4 ${styles.fadeIn}`}>
            <input
              type="text"
              placeholder="search by title & Tags"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="w-full px-4 py-2 rounded-full bg-white bg-opacity-20 text-white placeholder-white border-2 border-blue-300 focus:outline-none focus:border-white transition-colors"
            />
          </div>
        )}
      </div>

      {/* Video Background */}
      <div className={`absolute inset-0 transition-opacity duration-500 ${isTransitioning ? 'opacity-70' : 'opacity-100'}`}>
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          autoPlay
          loop
          muted
          playsInline
          preload="metadata"
        >
          <source src={videoDatas[count]} type="video/mp4" />
        </video>
      </div>

      {/* Video Progress Indicators */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 space-y-2">
        {videoDatas.map((_: string, index: number) => (
          <div
            key={index}
            className={`w-1 h-8 rounded-full transition-all duration-300 ${
              index === count ? 'bg-white' : 'bg-white bg-opacity-40'
            }`}
          />
        ))}
      </div>

      {/* Navigation Hints */}
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 text-white text-opacity-60">
        <div className="flex flex-col items-center space-y-4">
          <button 
            onClick={prevVideo}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
            disabled={isTransitioning}
          >
            ↑
          </button>
          <div className="text-xs text-center">
            <div>Scroll</div>
            <div>or</div>
            <div>Swipe</div>
          </div>
          <button 
            onClick={nextVideo}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
            disabled={isTransitioning}
          >
            ↓
          </button>
        </div>
      </div>

      {/* Overlay Content */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        {/* Scroll Indicator (show first few videos) */}
        {count <= 3 && (
          <div className={`flex items-center justify-center h-full ${styles.scrollHint}`}>
            <div className="text-center text-white">
              <div className="mb-4">
                <Image
                  src="/assets/ScrollTop.gif"
                  alt="Scroll"
                  width={120}
                  height={150}
                  className="mx-auto transform rotate-180"
                  priority
                  style={{ width: 'auto', height: '150px' }}
                />
              </div>
              <div className="text-2xl font-bold uppercase">Scroll or Swipe</div>
            </div>
          </div>
        )}

        {/* Get Started Button (show after 3 videos) */}
        {count > 3 && (
          <div className="absolute bottom-20 left-0 w-full flex justify-center pointer-events-auto">
            <button
              onClick={() => setFlag('SignUp')}
              className={`bg-purple-600 hover:bg-purple-700 text-white font-bold py-4 px-12 rounded-lg text-2xl uppercase opacity-90 hover:opacity-100 shadow-lg ${styles.getStartedButton}`}
            >
              Get Started
            </button>
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {isTransitioning && (
        <div className="absolute inset-0 z-30 flex items-center justify-center bg-black bg-opacity-20">
          <div className={`w-8 h-8 border-2 border-white border-t-transparent rounded-full ${styles.spinner}`}></div>
        </div>
      )}
    </div>
  )
}
