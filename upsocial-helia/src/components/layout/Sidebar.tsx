'use client'

import Image from 'next/image'
import { AuthManager } from '@/lib/data/auth-manager'

interface SidebarProps {
  currentPage: string
  setCurrentPage: (page: string) => void
  username: string
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}

export default function Sidebar({ currentPage, setCurrentPage, username, isOpen, setIsOpen }: SidebarProps) {
  const menuItems = [
    { name: 'Home', icon: '🏠' },
    { name: 'My Videos', icon: '📹' },
    { name: 'Profile', icon: '👤' },
    { name: 'Add a Video', icon: '➕' },
    { name: 'Settings', icon: '⚙️' }
  ]

  const handleLogout = () => {
    const authManager = AuthManager.getInstance()
    authManager.logout()
    window.location.reload()
  }

  const sidebarClasses = `
    fixed lg:static inset-y-0 left-0 z-50 w-64 bg-purple-800 text-white transform transition-transform duration-200 ease-in-out
    ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
  `

  return (
    <div className={sidebarClasses}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-6 border-b border-purple-700">
          <div className="flex items-center justify-between">
            <Image
              src="/assets/logos/logo.png"
              alt="UpSocial"
              width={120}
              height={30}
              className="h-8 w-auto"
            />
            <button
              onClick={() => setIsOpen(false)}
              className="lg:hidden text-white"
              title="Close Menu"
            >
              ✕
            </button>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3">
                {username.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="font-semibold">{username}</div>
                <div className="text-sm text-purple-300">User</div>
              </div>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.name}>
                <button
                  onClick={() => {
                    setCurrentPage(item.name)
                    setIsOpen(false)
                  }}
                  className={`
                    w-full flex items-center px-4 py-3 rounded-lg text-left transition-colors
                    ${currentPage === item.name 
                      ? 'bg-purple-700 text-white' 
                      : 'text-purple-200 hover:bg-purple-700 hover:text-white'
                    }
                  `}
                >
                  <span className="text-xl mr-3">{item.icon}</span>
                  {item.name}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-purple-700">
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-4 py-3 rounded-lg text-purple-200 hover:bg-purple-700 hover:text-white transition-colors"
          >
            <span className="text-xl mr-3">🚪</span>
            Logout
          </button>
        </div>
      </div>
    </div>
  )
}
