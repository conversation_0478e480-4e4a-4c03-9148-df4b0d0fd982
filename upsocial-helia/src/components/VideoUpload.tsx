'use client';

import React, { useState, useRef } from 'react';
import { videoStorage, VideoMetadata, StoredVideo } from '@/lib/ipfs/video-storage-v2';

interface VideoUploadProps {
  onUploadComplete?: (video: StoredVideo) => void;
  onUploadError?: (error: string) => void;
}

export function VideoUpload({ onUploadComplete, onUploadError }: VideoUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [metadata, setMetadata] = useState<Partial<VideoMetadata>>({
    title: '',
    description: '',
    category: 'Entertainment',
    tags: [],
    author: 'Anonymous'
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      // Auto-generate title from filename if not set
      if (!metadata.title) {
        setMetadata(prev => ({
          ...prev,
          title: file.name.replace(/\.[^/.]+$/, '')
        }));
      }
    }
  };

  const handleMetadataChange = (field: keyof VideoMetadata, value: any) => {
    setMetadata(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setMetadata(prev => ({
      ...prev,
      tags
    }));
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      onUploadError?.('Please select a video file');
      return;
    }

    if (!metadata.title) {
      onUploadError?.('Please enter a video title');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Initialize video storage service
      await videoStorage.initialize();

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Prepare complete metadata
      const completeMetadata: VideoMetadata = {
        title: metadata.title!,
        description: metadata.description || '',
        duration: 0, // Would be extracted from video in production
        category: metadata.category || 'Entertainment',
        tags: metadata.tags || [],
        createdAt: new Date().toISOString(),
        author: metadata.author || 'Anonymous'
      };

      // Upload video
      const uploadedVideo = await videoStorage.uploadVideo(selectedFile, completeMetadata);

      // Complete progress
      clearInterval(progressInterval);
      setUploadProgress(100);

      // Success callback
      onUploadComplete?.(uploadedVideo);

      // Reset form
      setSelectedFile(null);
      setMetadata({
        title: '',
        description: '',
        category: 'Entertainment',
        tags: [],
        author: 'Anonymous'
      });
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      console.error('Upload failed:', error);
      onUploadError?.(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
        Upload Video to IPFS
      </h2>

      {/* File Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Select Video File
        </label>
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileSelect}
          disabled={isUploading}
          title="Select video file to upload"
          aria-label="Select video file to upload"
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-300"
        />
        {selectedFile && (
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </p>
        )}
      </div>

      {/* Metadata Form */}
      <div className="space-y-4 mb-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Title *
          </label>
          <input
            type="text"
            value={metadata.title || ''}
            onChange={(e) => handleMetadataChange('title', e.target.value)}
            disabled={isUploading}
            placeholder="Enter video title"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description
          </label>
          <textarea
            value={metadata.description || ''}
            onChange={(e) => handleMetadataChange('description', e.target.value)}
            disabled={isUploading}
            placeholder="Enter video description"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Category
          </label>
          <select
            value={metadata.category || 'Entertainment'}
            onChange={(e) => handleMetadataChange('category', e.target.value)}
            disabled={isUploading}
            title="Select video category"
            aria-label="Select video category"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          >
            <option value="Entertainment">Entertainment</option>
            <option value="Education">Education</option>
            <option value="Gaming">Gaming</option>
            <option value="Music">Music</option>
            <option value="Sports">Sports</option>
            <option value="Technology">Technology</option>
            <option value="Travel">Travel</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Tags (comma-separated)
          </label>
          <input
            type="text"
            value={metadata.tags?.join(', ') || ''}
            onChange={(e) => handleTagsChange(e.target.value)}
            disabled={isUploading}
            placeholder="e.g. funny, tutorial, gaming"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>

        {/* Author */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Author
          </label>
          <input
            type="text"
            value={metadata.author || ''}
            onChange={(e) => handleMetadataChange('author', e.target.value)}
            disabled={isUploading}
            placeholder="Your name or channel"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
            <span>Uploading to IPFS...</span>
            <span>{uploadProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Upload Button */}
      <button
        onClick={handleUpload}
        disabled={!selectedFile || !metadata.title || isUploading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-4 rounded-md transition duration-200"
      >
        {isUploading ? 'Uploading...' : 'Upload to IPFS'}
      </button>

      {/* Help Text */}
      <p className="mt-4 text-sm text-gray-500 dark:text-gray-400 text-center">
        Supported formats: MP4, WebM, OGG • Max size: 100MB
      </p>
    </div>
  );
}
