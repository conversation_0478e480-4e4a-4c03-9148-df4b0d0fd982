'use client'

import { useState, useRef, useEffect } from 'react'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

const CATEGORIES = [
  'NEWEST',
  'FOR ME',
  'SUBSCRIPTIONS',
  'Animation',
  'Autos & Vehicles',
  'Beauty & Fashion',
  'Comedy',
  'Cooking & Food',
  'DIY & Crafts',
  'Documentary',
  'Education',
  'Entertainment',
  'Film & Animation',
  'Gaming',
  'Health & Fitness',
  'How-to & Style',
  'Kids & Family',
  'Music',
  'News & Politics',
  'Nonprofits & Activism',
  'People & Blogs',
  'Pets & Animals',
  'Science & Technology',
  'Sports',
  'Travel & Events',
  'Unboxing & Reviews',
  'Blogs'
]

interface CategoryTabsProps {
  selectedCategory: string
  onCategoryChange: (category: string) => void
  className?: string
}

export default function CategoryTabs({
  selectedCategory,
  onCategoryChange,
  className = ''
}: CategoryTabsProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  const checkScrollability = () => {
    const container = scrollContainerRef.current
    if (!container) return

    setCanScrollLeft(container.scrollLeft > 0)
    setCanScrollRight(
      container.scrollLeft < container.scrollWidth - container.clientWidth
    )
  }

  useEffect(() => {
    checkScrollability()
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollability)
      return () => container.removeEventListener('scroll', checkScrollability)
    }
  }, [])

  const scrollLeft = () => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollBy({ left: -200, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollBy({ left: 200, behavior: 'smooth' })
    }
  }

  const getCategoryIcon = (category: string) => {
    const iconMap: Record<string, string> = {
      'NEWEST': '🆕',
      'FOR ME': '🎯',
      'SUBSCRIPTIONS': '📺',
      'Animation': '🎬',
      'Autos & Vehicles': '🚗',
      'Beauty & Fashion': '💄',
      'Comedy': '😂',
      'Cooking & Food': '🍳',
      'DIY & Crafts': '🔨',
      'Documentary': '📚',
      'Education': '🎓',
      'Entertainment': '🎭',
      'Film & Animation': '🎥',
      'Gaming': '🎮',
      'Health & Fitness': '💪',
      'How-to & Style': '✨',
      'Kids & Family': '👨‍👩‍👧‍👦',
      'Music': '🎵',
      'News & Politics': '📰',
      'Nonprofits & Activism': '🤝',
      'People & Blogs': '👥',
      'Pets & Animals': '🐾',
      'Science & Technology': '🔬',
      'Sports': '⚽',
      'Travel & Events': '✈️',
      'Unboxing & Reviews': '📦',
      'Blogs': '📝'
    }
    return iconMap[category] || '📹'
  }

  const isSpecialCategory = (category: string) => {
    return ['NEWEST', 'FOR ME', 'SUBSCRIPTIONS'].includes(category)
  }

  return (
    <div className={`relative bg-black border-b border-gray-800 ${className}`}>
      <div className="flex items-center">
        {/* Left scroll button */}
        {canScrollLeft && (
          <button
            onClick={scrollLeft}
            className="absolute left-0 z-10 bg-gray-900 bg-opacity-90 hover:bg-opacity-100 p-2 rounded-r-lg shadow-md transition-all duration-200"
          >
            <ChevronLeftIcon className="w-5 h-5 text-gray-300" />
          </button>
        )}

        {/* Scrollable categories */}
        <div
          ref={scrollContainerRef}
          className="flex overflow-x-auto scrollbar-hide space-x-2 px-4 py-3"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {CATEGORIES.map((category) => {
            const isSelected = category === selectedCategory
            const isSpecial = isSpecialCategory(category)
            
            return (
              <button
                key={category}
                onClick={() => onCategoryChange(category)}
                className={`
                  flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all duration-200 transform hover:scale-105
                  ${isSelected
                    ? isSpecial
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'
                      : 'bg-purple-600 text-white shadow-lg'
                    : isSpecial
                      ? 'bg-gradient-to-r from-gray-800 to-gray-700 text-gray-300 hover:from-purple-600 hover:to-pink-600 hover:text-white'
                      : 'bg-gray-800 text-gray-300 hover:bg-purple-600 hover:text-white'
                  }
                `}
              >
                <span className="text-base">{getCategoryIcon(category)}</span>
                <span>{category}</span>
                {isSelected && (
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                )}
              </button>
            )
          })}
        </div>

        {/* Right scroll button */}
        {canScrollRight && (
          <button
            onClick={scrollRight}
            className="absolute right-0 z-10 bg-gray-900 bg-opacity-90 hover:bg-opacity-100 p-2 rounded-l-lg shadow-md transition-all duration-200"
          >
            <ChevronRightIcon className="w-5 h-5 text-gray-300" />
          </button>
        )}
      </div>

      {/* Category count indicator */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex space-x-1 pb-1">
        {Array.from({ length: Math.ceil(CATEGORIES.length / 8) }).map((_, index) => (
          <div
            key={index}
            className="w-1 h-1 bg-gray-600 rounded-full"
          />
        ))}
      </div>

      {/* Custom styles for hiding scrollbar */}
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  )
}