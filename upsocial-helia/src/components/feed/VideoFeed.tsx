'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import VideoCard from '../video/VideoCard'
import SwipeInterface from '../video/SwipeInterface'
import { Video } from '../../types/video'

interface VideoFeedProps {
  videos: Video[]
  loading?: boolean
  hasMore?: boolean
  selectedCategory?: string
  onLoadMore?: () => void
  onVideoLike?: (videoId: string) => void
  onVideoShare?: (videoId: string) => void
  onVideoSelect?: (videoIndex: number) => void
  className?: string
}

export default function VideoFeed({
  videos,
  loading = false,
  hasMore = true,
  selectedCategory,
  onLoadMore,
  onVideoLike,
  onVideoShare,
  onVideoSelect,
  className = ''
}: VideoFeedProps) {
  const [selectedVideoIndex, setSelectedVideoIndex] = useState<number | null>(null)
  const [visibleVideos, setVisibleVideos] = useState<Set<string>>(new Set())
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadingRef = useRef<HTMLDivElement>(null)

  // Filter videos based on selected category
  const filteredVideos = selectedCategory && selectedCategory !== 'NEWEST'
    ? videos.filter(video => {
        if (selectedCategory === 'FOR ME') {
          // TODO: Implement personalized algorithm
          return true
        }
        if (selectedCategory === 'SUBSCRIPTIONS') {
          // TODO: Filter by subscribed channels
          return true
        }
        return video.categories?.includes(selectedCategory) || 
               video.title.toLowerCase().includes(selectedCategory.toLowerCase())
      })
    : videos

  // Set up intersection observer for infinite scroll
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect()
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const target = entries[0]
        if (target.isIntersecting && hasMore && !loading && onLoadMore) {
          onLoadMore()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px'
      }
    )

    if (loadingRef.current) {
      observerRef.current.observe(loadingRef.current)
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [hasMore, loading, onLoadMore])

  // Set up intersection observer for video visibility tracking
  useEffect(() => {
    const videoObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const videoId = entry.target.getAttribute('data-video-id')
          if (videoId) {
            if (entry.isIntersecting) {
              setVisibleVideos(prev => new Set([...prev, videoId]))
            } else {
              setVisibleVideos(prev => {
                const newSet = new Set(prev)
                newSet.delete(videoId)
                return newSet
              })
            }
          }
        })
      },
      {
        threshold: 0.5
      }
    )

    // Observe all video cards
    const videoCards = document.querySelectorAll('[data-video-id]')
    videoCards.forEach(card => videoObserver.observe(card))

    return () => videoObserver.disconnect()
  }, [filteredVideos])

  const handleVideoClick = useCallback((video: Video) => {
    const index = filteredVideos.findIndex(v => v.id === video.id)
    setSelectedVideoIndex(index)
    // Call the onVideoSelect prop if provided
    if (onVideoSelect) {
      onVideoSelect(index)
    }
  }, [filteredVideos, onVideoSelect])

  const handleSwipeInterfaceClose = useCallback(() => {
    setSelectedVideoIndex(null)
  }, [])

  const handleVideoChange = useCallback((index: number) => {
    setSelectedVideoIndex(index)
  }, [])

  const LoadingSpinner = () => (
    <div className="flex items-center justify-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
    </div>
  )

  const EmptyState = () => (
    <div className="text-center py-16">
      <div className="text-gray-400 text-6xl mb-4">📹</div>
      <h3 className="text-xl font-semibold text-gray-700 mb-2">
        No videos found
      </h3>
      <p className="text-gray-500 max-w-md mx-auto">
        {selectedCategory && selectedCategory !== 'NEWEST'
          ? `No videos found in the "${selectedCategory}" category. Try selecting a different category or check back later.`
          : 'No videos available at the moment. Check back later for new content.'
        }
      </p>
    </div>
  )

  const VideoGrid = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
      {filteredVideos.map((video) => (
        <div
          key={video.id}
          data-video-id={video.id}
          className={`transition-transform duration-200 ${
            visibleVideos.has(video.id) ? 'animate-fade-in' : 'opacity-0'
          }`}
        >
          <VideoCard
            video={video}
            onClick={handleVideoClick}
            onLike={onVideoLike}
            onShare={onVideoShare}
            className="h-full"
          />
        </div>
      ))}
    </div>
  )

  return (
    <div className={`min-h-screen ${className}`}>
      {/* Video Grid */}
      {filteredVideos.length > 0 ? (
        <>
          <VideoGrid />
          
          {/* Loading indicator for infinite scroll */}
          {hasMore && (
            <div ref={loadingRef} className="py-8">
              {loading ? (
                <LoadingSpinner />
              ) : (
                <div className="text-center text-gray-500">
                  <p>Scroll down to load more videos</p>
                </div>
              )}
            </div>
          )}
          
          {/* End of content indicator */}
          {!hasMore && !loading && (
            <div className="text-center py-8 text-gray-500">
              <p>You&apos;ve reached the end! 🎉</p>
              <p className="text-sm mt-2">Check back later for more amazing content</p>
            </div>
          )}
        </>
      ) : loading ? (
        <LoadingSpinner />
      ) : (
        <EmptyState />
      )}

      {/* Swipe Interface Modal */}
      {selectedVideoIndex !== null && (
        <SwipeInterface
          videos={filteredVideos}
          initialIndex={selectedVideoIndex}
          onVideoChange={handleVideoChange}
          onLike={onVideoLike}
          onShare={onVideoShare}
          onClose={handleSwipeInterfaceClose}
        />
      )}

      {/* Custom animations */}
      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-out forwards;
        }
      `}</style>
    </div>
  )
}