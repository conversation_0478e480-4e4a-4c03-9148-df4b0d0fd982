'use client'

import { useState, useRef, useEffect } from 'react'
import { MagnifyingGlassIcon, XMarkIcon, ClockIcon, HashtagIcon } from '@heroicons/react/24/outline'

interface SearchSuggestion {
  id: string
  text: string
  type: 'recent' | 'trending' | 'hashtag' | 'creator'
  icon?: string
}

interface SearchBarProps {
  onSearch: (query: string) => void
  onFilterChange?: (filters: SearchFilters) => void
  placeholder?: string
  className?: string
}

interface SearchFilters {
  duration?: 'short' | 'medium' | 'long'
  uploadDate?: 'hour' | 'day' | 'week' | 'month' | 'year'
  sortBy?: 'relevance' | 'date' | 'views' | 'rating'
}

// Mock data - replace with real search suggestions
const mockSuggestions: SearchSuggestion[] = [
  { id: '1', text: 'web3 tutorial', type: 'recent' },
  { id: '2', text: 'blockchain basics', type: 'recent' },
  { id: '3', text: '#decentralized', type: 'hashtag' },
  { id: '4', text: '#ipfs', type: 'hashtag' },
  { id: '5', text: 'crypto news', type: 'trending', icon: '🔥' },
  { id: '6', text: 'nft explained', type: 'trending', icon: '🔥' },
  { id: '7', text: '@techguru', type: 'creator', icon: '👤' },
  { id: '8', text: '@cryptoexplorer', type: 'creator', icon: '👤' }
]

export default function SearchBar({
  onSearch,
  onFilterChange,
  placeholder = 'Search videos, creators, hashtags...',
  className = ''
}: SearchBarProps) {
  const [query, setQuery] = useState('')
  const [isActive, setIsActive] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [filters, setFilters] = useState<SearchFilters>({})
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('upsocial-recent-searches')
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved))
      } catch (error) {
        console.error('Failed to parse recent searches:', error)
      }
    }
  }, [])

  // Update suggestions based on query
  useEffect(() => {
    if (!query.trim()) {
      setSuggestions([])
      return
    }

    const filtered = mockSuggestions.filter(suggestion =>
      suggestion.text.toLowerCase().includes(query.toLowerCase())
    )
    setSuggestions(filtered.slice(0, 6))
  }, [query])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsActive(false)
        setShowFilters(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query
    if (!finalQuery.trim()) return

    // Add to recent searches
    const updatedRecent = [finalQuery, ...recentSearches.filter(s => s !== finalQuery)].slice(0, 5)
    setRecentSearches(updatedRecent)
    localStorage.setItem('upsocial-recent-searches', JSON.stringify(updatedRecent))

    onSearch(finalQuery)
    setIsActive(false)
    inputRef.current?.blur()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    } else if (e.key === 'Escape') {
      setIsActive(false)
      inputRef.current?.blur()
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    handleSearch(suggestion.text)
  }

  const clearQuery = () => {
    setQuery('')
    inputRef.current?.focus()
  }

  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('upsocial-recent-searches')
  }

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    if (onFilterChange) {
      onFilterChange(updatedFilters)
    }
  }

  const getSuggestionIcon = (suggestion: SearchSuggestion) => {
    switch (suggestion.type) {
      case 'recent':
        return <ClockIcon className="w-4 h-4 text-gray-400" />
      case 'hashtag':
        return <HashtagIcon className="w-4 h-4 text-blue-500" />
      case 'trending':
        return <span className="text-sm">{suggestion.icon || '🔥'}</span>
      case 'creator':
        return <span className="text-sm">{suggestion.icon || '👤'}</span>
      default:
        return <MagnifyingGlassIcon className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className={`relative flex items-center bg-white border rounded-full transition-all duration-200 ${
        isActive ? 'border-purple-500 shadow-lg ring-2 ring-purple-100' : 'border-gray-300 hover:border-gray-400'
      }`}>
        <MagnifyingGlassIcon className="w-5 h-5 text-gray-400 ml-4" />
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsActive(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="flex-1 px-4 py-3 bg-transparent border-none outline-none text-gray-900 placeholder-gray-500"
        />

        {query && (
          <button
            onClick={clearQuery}
            className="mr-2 p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <XMarkIcon className="w-4 h-4 text-gray-400" />
          </button>
        )}

        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`mr-4 p-2 rounded-full transition-colors ${
            showFilters ? 'bg-purple-100 text-purple-600' : 'hover:bg-gray-100 text-gray-600'
          }`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
          </svg>
        </button>
      </div>

      {/* Search Dropdown */}
      {isActive && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto">
          {/* Recent Searches */}
          {recentSearches.length > 0 && !query && (
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-700">Recent searches</h4>
                <button
                  onClick={clearRecentSearches}
                  className="text-xs text-purple-600 hover:text-purple-700"
                >
                  Clear all
                </button>
              </div>
              <div className="space-y-1">
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearch(search)}
                    className="w-full flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg text-left"
                  >
                    <ClockIcon className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-700">{search}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Suggestions */}
          {suggestions.length > 0 && (
            <div className="p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Suggestions</h4>
              <div className="space-y-1">
                {suggestions.map((suggestion) => (
                  <button
                    key={suggestion.id}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg text-left"
                  >
                    {getSuggestionIcon(suggestion)}
                    <span className="text-gray-700">{suggestion.text}</span>
                    {suggestion.type === 'trending' && (
                      <span className="ml-auto text-xs text-orange-500 font-medium">Trending</span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* No results */}
          {query && suggestions.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              <p>No suggestions found</p>
              <button
                onClick={() => handleSearch()}
                className="mt-2 text-purple-600 hover:text-purple-700 text-sm"
              >
                Search for &ldquo;{query}&rdquo;
              </button>
            </div>
          )}
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 p-6">
          <h4 className="text-lg font-semibold text-gray-800 mb-4">Search Filters</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Duration Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Duration</label>
              <select
                value={filters.duration || ''}
                onChange={(e) => updateFilters({ duration: e.target.value as 'short' | 'medium' | 'long' | undefined })}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">Any duration</option>
                <option value="short">Under 4 minutes</option>
                <option value="medium">4-20 minutes</option>
                <option value="long">Over 20 minutes</option>
              </select>
            </div>

            {/* Upload Date Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Upload date</label>
              <select
                value={filters.uploadDate || ''}
                onChange={(e) => updateFilters({ uploadDate: e.target.value as 'hour' | 'day' | 'week' | 'month' | 'year' | undefined })}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">Any time</option>
                <option value="hour">Last hour</option>
                <option value="day">Today</option>
                <option value="week">This week</option>
                <option value="month">This month</option>
                <option value="year">This year</option>
              </select>
            </div>

            {/* Sort By Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Sort by</label>
              <select
                value={filters.sortBy || 'relevance'}
                onChange={(e) => updateFilters({ sortBy: e.target.value as 'relevance' | 'date' | 'views' | 'rating' })}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="relevance">Relevance</option>
                <option value="date">Upload date</option>
                <option value="views">View count</option>
                <option value="rating">Rating</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => {
                setFilters({})
                if (onFilterChange) {
                  onFilterChange({})
                }
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-700 transition-colors"
            >
              Reset
            </button>
            <button
              onClick={() => setShowFilters(false)}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}
    </div>
  )
}