'use client'

import { useState } from 'react'
import { toast } from 'react-hot-toast'
import { AuthManager } from '@/lib/data/auth-manager'

interface SettingsSection {
  id: string
  name: string
  icon: string
}

interface NotificationSettings {
  emailNotifications: boolean
  pushNotifications: boolean
  videoUploaded: boolean
  newFollower: boolean
  videoLiked: boolean
}

interface PrivacySettings {
  profileVisibility: 'public' | 'private'
  showEmail: boolean
  showSocialLinks: boolean
  allowComments: boolean
}

export default function Settings() {
  const [activeSection, setActiveSection] = useState('notifications')
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: false,
    videoUploaded: true,
    newFollower: true,
    videoLiked: false
  })
  
  const [privacy, setPrivacy] = useState<PrivacySettings>({
    profileVisibility: 'public',
    showEmail: false,
    showSocialLinks: true,
    allowComments: true
  })

  const sections: SettingsSection[] = [
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'privacy', name: 'Privacy', icon: '🔒' },
    { id: 'storage', name: 'Storage', icon: '💾' },
    { id: 'account', name: 'Account', icon: '👤' },
    { id: 'about', name: 'About', icon: 'ℹ️' }
  ]

  const handleNotificationChange = (key: keyof NotificationSettings) => {
    setNotifications(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const handlePrivacyChange = (key: keyof PrivacySettings, value: string | boolean) => {
    setPrivacy(prev => ({ ...prev, [key]: value }))
  }

  const handleSaveSettings = () => {
    // TODO: Save to IPFS/Helia
    toast.success('Settings saved successfully!')
  }

  const handleExportData = () => {
    toast.success('Data export started. You will receive an email when ready.')
  }

  const handleDeleteAccount = () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      const authManager = AuthManager.getInstance()
      authManager.logout()
      toast.success('Account deletion initiated.')
      window.location.reload()
    }
  }

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Email & Push Notifications</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Email Notifications</div>
              <div className="text-sm text-gray-500">Receive notifications via email</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.emailNotifications}
                onChange={() => handleNotificationChange('emailNotifications')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Push Notifications</div>
              <div className="text-sm text-gray-500">Receive browser notifications</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.pushNotifications}
                onChange={() => handleNotificationChange('pushNotifications')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Activity Notifications</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Video Uploaded</div>
              <div className="text-sm text-gray-500">When your video is processed</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.videoUploaded}
                onChange={() => handleNotificationChange('videoUploaded')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">New Follower</div>
              <div className="text-sm text-gray-500">When someone follows you</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.newFollower}
                onChange={() => handleNotificationChange('newFollower')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Video Liked</div>
              <div className="text-sm text-gray-500">When someone likes your video</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.videoLiked}
                onChange={() => handleNotificationChange('videoLiked')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  const renderPrivacySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Profile Privacy</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Profile Visibility</label>
            <select
              value={privacy.profileVisibility}
              onChange={(e) => handlePrivacyChange('profileVisibility', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg"
            >
              <option value="public">Public - Anyone can see your profile</option>
              <option value="private">Private - Only followers can see your content</option>
            </select>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Show Email Address</div>
              <div className="text-sm text-gray-500">Display email on your profile</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={privacy.showEmail}
                onChange={() => handlePrivacyChange('showEmail', !privacy.showEmail)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Show Social Links</div>
              <div className="text-sm text-gray-500">Display social media links</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={privacy.showSocialLinks}
                onChange={() => handlePrivacyChange('showSocialLinks', !privacy.showSocialLinks)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-700">Allow Comments</div>
              <div className="text-sm text-gray-500">Let others comment on your videos</div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={privacy.allowComments}
                onChange={() => handlePrivacyChange('allowComments', !privacy.allowComments)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  )

  const renderStorageSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">IPFS Storage</h3>
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">Storage Used</span>
            <span className="text-sm font-medium">2.4 GB / 10 GB</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-purple-600 h-2 rounded-full w-1/4"></div>
          </div>
        </div>
        <p className="text-sm text-gray-600">
          Your content is stored on the IPFS network, ensuring decentralization and permanence.
        </p>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Data Management</h3>
        <div className="space-y-3">
          <button
            onClick={handleExportData}
            className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            <div className="font-medium text-gray-700">Export My Data</div>
            <div className="text-sm text-gray-500">Download all your videos and profile data</div>
          </button>
          
          <button className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
            <div className="font-medium text-gray-700">Clear Cache</div>
            <div className="text-sm text-gray-500">Clear locally cached content</div>
          </button>
        </div>
      </div>
    </div>
  )

  const renderAccountSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Account Actions</h3>
        <div className="space-y-3">
          <button className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
            <div className="font-medium text-gray-700">Change Password</div>
            <div className="text-sm text-gray-500">Update your account password</div>
          </button>
          
          <button className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
            <div className="font-medium text-gray-700">Two-Factor Authentication</div>
            <div className="text-sm text-gray-500">Add extra security to your account</div>
          </button>
          
          <button
            onClick={handleDeleteAccount}
            className="w-full text-left p-4 border border-red-300 rounded-lg hover:bg-red-50"
          >
            <div className="font-medium text-red-700">Delete Account</div>
            <div className="text-sm text-red-500">Permanently delete your account and data</div>
          </button>
        </div>
      </div>
    </div>
  )

  const renderAboutSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">About UpSocial</h3>
        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="font-medium text-gray-700 mb-2">Version</div>
            <div className="text-sm text-gray-600">1.0.0 (Beta)</div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="font-medium text-gray-700 mb-2">Built With</div>
            <div className="text-sm text-gray-600">
              Next.js, TypeScript, Tailwind CSS, Helia (IPFS)
            </div>
          </div>
          
          <div className="space-y-2">
            <button className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
              <div className="font-medium text-gray-700">Privacy Policy</div>
            </button>
            
            <button className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
              <div className="font-medium text-gray-700">Terms of Service</div>
            </button>
            
            <button className="w-full text-left p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
              <div className="font-medium text-gray-700">Contact Support</div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'notifications':
        return renderNotificationSettings()
      case 'privacy':
        return renderPrivacySettings()
      case 'storage':
        return renderStorageSettings()
      case 'account':
        return renderAccountSettings()
      case 'about':
        return renderAboutSettings()
      default:
        return renderNotificationSettings()
    }
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">Settings</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Settings Menu */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-4">
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`
                    w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors
                    ${activeSection === section.id
                      ? 'bg-purple-100 text-purple-700 border-r-2 border-purple-700'
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  <span className="text-lg mr-3">{section.icon}</span>
                  {section.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md p-6">
            {renderActiveSection()}
            
            {/* Save Button */}
            {(activeSection === 'notifications' || activeSection === 'privacy') && (
              <div className="mt-8 pt-6 border-t border-gray-200">
                <button
                  onClick={handleSaveSettings}
                  className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
