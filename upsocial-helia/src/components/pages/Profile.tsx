'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { AuthManager } from '@/lib/data/auth-manager'

interface UserProfile {
  username: string
  email: string
  bio: string
  avatar: string
  followers: number
  following: number
  totalViews: number
  joinDate: string
  socialLinks: {
    twitter?: string
    youtube?: string
    website?: string
  }
}

export default function Profile() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState<Partial<UserProfile>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load user profile - replace with actual data from Helia
    const authManager = AuthManager.getInstance()
    const currentUser = authManager.getCurrentUser()
    
    if (currentUser) {
      const mockProfile: UserProfile = {
        username: currentUser.username,
        email: currentUser.email,
        bio: 'Content creator passionate about decentralized technologies and Web3.',
        avatar: '/assets/profile.png',
        followers: 1234,
        following: 567,
        totalViews: 45678,
        joinDate: '2024-01-01',
        socialLinks: {
          twitter: '',
          youtube: '',
          website: ''
        }
      }
      
      setProfile(mockProfile)
      setEditForm(mockProfile)
    }
    
    setLoading(false)
  }, [])

  const handleSaveProfile = () => {
    if (profile && editForm) {
      setProfile({ ...profile, ...editForm })
      setIsEditing(false)
      // TODO: Save to Helia/IPFS
    }
  }

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setEditForm(prev => ({ ...prev, [field]: value }))
  }

  const handleSocialLinkChange = (platform: string, value: string) => {
    setEditForm(prev => ({
      ...prev,
      socialLinks: { ...prev.socialLinks, [platform]: value }
    }))
  }

  if (loading || !profile) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Profile Header */}
      <div className="bg-white rounded-lg shadow-md p-8 mb-8">
        <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
          {/* Avatar */}
          <div className="relative">
            <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-purple-200">
              <Image
                src={profile.avatar}
                alt={profile.username}
                width={128}
                height={128}
                className="object-cover"
              />
            </div>
            <button className="absolute bottom-0 right-0 bg-purple-600 text-white p-2 rounded-full hover:bg-purple-700 transition-colors">
              📷
            </button>
          </div>

          {/* Profile Info */}
          <div className="flex-1 text-center md:text-left">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
              <h1 className="text-3xl font-bold text-gray-800">{profile.username}</h1>
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="mt-2 md:mt-0 bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                {isEditing ? 'Cancel' : 'Edit Profile'}
              </button>
            </div>
            
            <p className="text-gray-600 mb-4">{profile.email}</p>
            
            {isEditing ? (
              <textarea
                value={editForm.bio || ''}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg resize-none"
                rows={3}
                placeholder="Tell us about yourself..."
              />
            ) : (
              <p className="text-gray-700 mb-6">{profile.bio}</p>
            )}

            {/* Stats */}
            <div className="flex justify-center md:justify-start space-x-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">{profile.followers.toLocaleString()}</div>
                <div className="text-gray-600">Followers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">{profile.following.toLocaleString()}</div>
                <div className="text-gray-600">Following</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-800">{profile.totalViews.toLocaleString()}</div>
                <div className="text-gray-600">Total Views</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Profile Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Account Information */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Account Information</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
              {isEditing ? (
                <input
                  type="text"
                  value={editForm.username || ''}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">{profile.username}</div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              {isEditing ? (
                <input
                  type="email"
                  value={editForm.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">{profile.email}</div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Member Since</label>
              <div className="p-3 bg-gray-50 rounded-lg">
                {new Date(profile.joinDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Social Links */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Social Links</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Twitter</label>
              {isEditing ? (
                <input
                  type="url"
                  value={editForm.socialLinks?.twitter || ''}
                  onChange={(e) => handleSocialLinkChange('twitter', e.target.value)}
                  placeholder="https://twitter.com/username"
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">
                  {profile.socialLinks.twitter || 'Not connected'}
                </div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">YouTube</label>
              {isEditing ? (
                <input
                  type="url"
                  value={editForm.socialLinks?.youtube || ''}
                  onChange={(e) => handleSocialLinkChange('youtube', e.target.value)}
                  placeholder="https://youtube.com/channel/..."
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">
                  {profile.socialLinks.youtube || 'Not connected'}
                </div>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
              {isEditing ? (
                <input
                  type="url"
                  value={editForm.socialLinks?.website || ''}
                  onChange={(e) => handleSocialLinkChange('website', e.target.value)}
                  placeholder="https://yourwebsite.com"
                  className="w-full p-3 border border-gray-300 rounded-lg"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-lg">
                  {profile.socialLinks.website || 'Not connected'}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      {isEditing && (
        <div className="mt-8 flex justify-center">
          <button
            onClick={handleSaveProfile}
            className="bg-purple-600 text-white px-8 py-3 rounded-lg hover:bg-purple-700 transition-colors"
          >
            Save Changes
          </button>
        </div>
      )}
    </div>
  )
}
