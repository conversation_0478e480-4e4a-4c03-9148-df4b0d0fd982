'use client'

import { useState, useRef } from 'react'
import { toast } from 'react-hot-toast'
import VideoUpload from '../upload/VideoUpload'
import VideoPreview from '../upload/VideoPreview'
import MetadataForm from '../upload/MetadataForm'
import ThumbnailGenerator from '../upload/ThumbnailGenerator'
import ProgressTracker, { UploadStep } from '../upload/ProgressTracker'
import ContentService from '../../lib/data/content-service'
import HybridDataLayer from '../../lib/data/hybrid-data-layer'

type UploadState = 'select' | 'preview' | 'metadata' | 'thumbnail' | 'uploading' | 'complete'

interface VideoMetadata {
  title: string
  description: string
  categories: string[]
  tags: string[]
}

export default function Upload() {
  const [currentState, setCurrentState] = useState<UploadState>('select')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [metadata, setMetadata] = useState<VideoMetadata>({
    title: '',
    description: '',
    categories: [],
    tags: []
  })
  const [selectedThumbnail, setSelectedThumbnail] = useState<HTMLCanvasElement | null>(null)
  const [uploadSteps, setUploadSteps] = useState<UploadStep[]>([])
  
  const hybridDataLayer = HybridDataLayer.getInstance()

  // Step handlers
  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    setCurrentState('preview')
    
    // Auto-generate title from filename if empty
    const fileName = file.name.split('.')[0].replace(/[_-]/g, ' ')
    setMetadata(prev => ({
      ...prev,
      title: prev.title || fileName
    }))
  }

  const handlePreviewAccept = () => {
    setCurrentState('metadata')
  }

  const handlePreviewReject = () => {
    setSelectedFile(null)
    setCurrentState('select')
  }

  const handleMetadataSubmit = (formData: VideoMetadata) => {
    setMetadata(formData)
    setCurrentState('thumbnail')
  }

  const handleMetadataCancel = () => {
    setCurrentState('preview')
  }

  const handleThumbnailSelect = (thumbnail: HTMLCanvasElement) => {
    setSelectedThumbnail(thumbnail)
  }

  const handleThumbnailComplete = () => {
    setCurrentState('uploading')
    startUpload()
  }

  const handleThumbnailSkip = () => {
    setSelectedThumbnail(null)
    setCurrentState('uploading')
    startUpload()
  }

  const initializeUploadSteps = (): UploadStep[] => [
    {
      id: 'validate',
      label: 'Validating video file',
      status: 'pending'
    },
    {
      id: 'thumbnail',
      label: 'Processing thumbnail',
      status: 'pending'
    },
    {
      id: 'ipfs-video',
      label: 'Uploading video to IPFS',
      status: 'pending'
    },
    {
      id: 'ipfs-thumbnail',
      label: 'Uploading thumbnail to IPFS',
      status: 'pending'
    },
    {
      id: 'metadata',
      label: 'Storing metadata',
      status: 'pending'
    },
    {
      id: 'indexing',
      label: 'Indexing content',
      status: 'pending'
    }
  ]

  const updateStepStatus = (stepId: string, status: UploadStep['status'], progress?: number, error?: string) => {
    setUploadSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status, progress, error }
        : step
    ))
  }

  const startUpload = async () => {
    if (!selectedFile || !metadata.title) {
      toast.error('Missing required data for upload')
      return
    }

    const steps = initializeUploadSteps()
    setUploadSteps(steps)

    try {
      // Step 1: Validate file
      updateStepStatus('validate', 'processing')
      await new Promise(resolve => setTimeout(resolve, 1000))
      updateStepStatus('validate', 'completed')

      // Step 2: Process thumbnail
      updateStepStatus('thumbnail', 'processing')
      let thumbnailFile: File | undefined
      
      if (selectedThumbnail) {
        // Convert canvas to blob then to file
        const blob = await new Promise<Blob>((resolve) => {
          selectedThumbnail.toBlob((blob) => {
            resolve(blob!)
          }, 'image/jpeg', 0.8)
        })
        thumbnailFile = new File([blob], 'thumbnail.jpg', { type: 'image/jpeg' })
      }
      updateStepStatus('thumbnail', 'completed')

      // Step 3-6: Upload using HybridDataLayer
      updateStepStatus('ipfs-video', 'processing')
      
      const result = await hybridDataLayer.storeVideo(selectedFile, {
        title: metadata.title,
        description: metadata.description,
        categories: metadata.categories,
        tags: metadata.tags,
        thumbnail: thumbnailFile
      })

      if (result) {
        updateStepStatus('ipfs-video', 'completed')
        updateStepStatus('ipfs-thumbnail', 'completed')
        updateStepStatus('metadata', 'completed')
        updateStepStatus('indexing', 'completed')
        
        setCurrentState('complete')
        toast.success('Video uploaded successfully! 🎉')
      } else {
        throw new Error('Upload failed')
      }

    } catch (error) {
      console.error('Upload error:', error)
      
      // Mark current step as failed
      const currentStep = uploadSteps.find(step => step.status === 'processing')
      if (currentStep) {
        updateStepStatus(currentStep.id, 'error', undefined, error instanceof Error ? error.message : 'Upload failed')
      }
      
      toast.error('Upload failed. Please try again.')
    }
  }

  const handleUploadCancel = () => {
    setCurrentState('select')
    setSelectedFile(null)
    setMetadata({
      title: '',
      description: '',
      categories: [],
      tags: []
    })
    setSelectedThumbnail(null)
    setUploadSteps([])
  }

  const handleRetryStep = (stepId: string) => {
    // Reset failed step and retry upload
    updateStepStatus(stepId, 'pending')
    startUpload()
  }

  const resetUpload = () => {
    setCurrentState('select')
    setSelectedFile(null)
    setMetadata({
      title: '',
      description: '',
      categories: [],
      tags: []
    })
    setSelectedThumbnail(null)
    setUploadSteps([])
  }

  // Render appropriate component based on current state
  const renderCurrentStep = () => {
    switch (currentState) {
      case 'select':
        return (
          <VideoUpload
            onFileSelect={handleFileSelect}
            maxSize={500}
            acceptedFormats={['mp4', 'webm', 'mov', 'avi']}
            className="max-w-3xl mx-auto"
          />
        )

      case 'preview':
        return selectedFile ? (
          <VideoPreview
            file={selectedFile}
            onAccept={handlePreviewAccept}
            onReject={handlePreviewReject}
            className="max-w-4xl mx-auto"
          />
        ) : null

      case 'metadata':
        return (
          <MetadataForm
            onSubmit={handleMetadataSubmit}
            onCancel={handleMetadataCancel}
            initialData={metadata}
            className="max-w-3xl mx-auto"
          />
        )

      case 'thumbnail':
        return selectedFile ? (
          <div className="max-w-4xl mx-auto space-y-6">
            <ThumbnailGenerator
              videoFile={selectedFile}
              onThumbnailSelect={handleThumbnailSelect}
              className="mb-6"
            />
            
            <div className="flex items-center justify-between p-6 bg-white rounded-xl shadow-lg">
              <button
                onClick={handleThumbnailSkip}
                className="px-6 py-3 text-gray-600 hover:text-gray-700 font-medium transition-colors"
              >
                Skip Thumbnail
              </button>
              
              <button
                onClick={handleThumbnailComplete}
                disabled={!selectedThumbnail}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium transition-colors shadow-lg hover:shadow-xl transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none"
              >
                Continue to Upload
              </button>
            </div>
          </div>
        ) : null

      case 'uploading':
        return (
          <ProgressTracker
            steps={uploadSteps}
            onCancel={handleUploadCancel}
            onRetry={handleRetryStep}
            className="max-w-3xl mx-auto"
          />
        )

      case 'complete':
        return (
          <div className="max-w-3xl mx-auto">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6 text-center">
                <div className="text-green-500 text-6xl mb-4">🎉</div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Upload Complete!
                </h2>
                <p className="text-gray-600 mb-6">
                  Your video has been successfully uploaded to the decentralized network and is now live!
                </p>
                
                <div className="flex items-center justify-center space-x-4">
                  <button
                    onClick={() => window.location.href = '/'}
                    className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium transition-colors shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    View in Feed
                  </button>
                  
                  <button
                    onClick={resetUpload}
                    className="px-6 py-3 text-gray-600 hover:text-gray-700 font-medium transition-colors"
                  >
                    Upload Another Video
                  </button>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Upload Video</h1>
                <p className="text-gray-600 mt-1">Share your content with the decentralized web</p>
              </div>
              
              {/* Progress indicator */}
              <div className="hidden md:flex items-center space-x-2">
                {['select', 'preview', 'metadata', 'thumbnail', 'uploading', 'complete'].map((step, index) => (
                  <div
                    key={step}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      currentState === step
                        ? 'bg-purple-600 text-white'
                        : ['select', 'preview', 'metadata', 'thumbnail', 'uploading', 'complete'].indexOf(currentState) > index
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {['select', 'preview', 'metadata', 'thumbnail', 'uploading', 'complete'].indexOf(currentState) > index ? '✓' : index + 1}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        {renderCurrentStep()}
      </div>
    </div>
  )
}
