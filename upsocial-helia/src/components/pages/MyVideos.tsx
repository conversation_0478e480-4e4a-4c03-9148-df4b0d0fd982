'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { AuthManager } from '@/lib/data/auth-manager'
import ChannelManager from '../channel/ChannelManager'
import ChannelView from '../channel/ChannelView'

interface UserVideo {
  id: string
  title: string
  thumbnail: string
  uploadDate: string
  views: number
  likes: number
  duration: string
  status: 'published' | 'processing' | 'draft'
  ipfsHash?: string
}

interface Channel {
  id: string
  channelName: string
  description: string
  photo: string
  followers: string[]
  contents: any[]
  createdDate: string
  email: string
  isPersonalProfile?: boolean
}

export default function MyVideos() {
  const [videos, setVideos] = useState<UserVideo[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedVideo, setSelectedVideo] = useState<UserVideo | null>(null)
  const [currentView, setCurrentView] = useState<'videos' | 'channels' | 'channelDetail'>('videos')
  const [selectedChannel, setSelectedChannel] = useState<Channel | null>(null)
  const [userEmail, setUserEmail] = useState('')

  // Mock user videos - replace with actual IPFS/Helia data
  const mockUserVideos: UserVideo[] = [
    {
      id: '1',
      title: 'My First Decentralized Video',
      thumbnail: '/assets/default.png',
      uploadDate: '2024-01-15',
      views: 234,
      likes: 12,
      duration: '10:30',
      status: 'published'
    },
    {
      id: '2',
      title: 'Web3 Development Tips',
      thumbnail: '/assets/default.png',
      uploadDate: '2024-01-10',
      views: 567,
      likes: 34,
      duration: '15:45',
      status: 'published'
    },
    {
      id: '3',
      title: 'IPFS Tutorial Draft',
      thumbnail: '/assets/default.png',
      uploadDate: '2024-01-20',
      views: 0,
      likes: 0,
      duration: '8:20',
      status: 'draft'
    }
  ]

  useEffect(() => {
    // Get current user email
    const authManager = AuthManager.getInstance()
    const currentUser = authManager.getCurrentUser()
    if (currentUser) {
      setUserEmail(currentUser.email)
    }

    // Simulate loading user videos
    const timer = setTimeout(() => {
      setVideos(mockUserVideos)
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800'
      case 'processing':
        return 'bg-yellow-100 text-yellow-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleDeleteVideo = (videoId: string) => {
    if (confirm('Are you sure you want to delete this video?')) {
      setVideos(videos.filter(video => video.id !== videoId))
    }
  }

  const handleChannelSelect = (channel: Channel) => {
    setSelectedChannel(channel)
    setCurrentView('channelDetail')
  }

  const handleBackToChannels = () => {
    setSelectedChannel(null)
    setCurrentView('channels')
  }

  const renderTabButton = (view: string, label: string, icon: string) => (
    <button
      onClick={() => setCurrentView(view as any)}
      className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
        currentView === view
          ? 'bg-purple-600 text-white'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      <span>{icon}</span>
      <span>{label}</span>
    </button>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600"></div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              {currentView === 'videos' ? 'My Videos' : currentView === 'channels' ? 'My Channels' : selectedChannel?.channelName}
            </h1>
            <p className="text-gray-600">
              {currentView === 'videos' ? 'Manage your uploaded content' : currentView === 'channels' ? 'Manage your content channels' : 'Channel overview and management'}
            </p>
          </div>
          {currentView === 'videos' && (
            <button className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
              Upload New Video
            </button>
          )}
        </div>

        {/* Tab Navigation */}
        {currentView !== 'channelDetail' && (
          <div className="flex space-x-4 border-b border-gray-200 pb-4">
            {renderTabButton('videos', 'My Videos', '📹')}
            {renderTabButton('channels', 'My Channels', '📺')}
          </div>
        )}
      </div>

      {/* Content Based on Current View */}
      {currentView === 'videos' && (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 p-3 rounded-full">
                  <span className="text-purple-600 text-xl">📹</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-800">{videos.length}</div>
                  <div className="text-gray-600">Total Videos</div>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 p-3 rounded-full">
                  <span className="text-blue-600 text-xl">👁️</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-800">
                    {videos.reduce((sum, video) => sum + video.views, 0)}
                  </div>
                  <div className="text-gray-600">Total Views</div>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="bg-green-100 p-3 rounded-full">
                  <span className="text-green-600 text-xl">❤️</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-800">
                    {videos.reduce((sum, video) => sum + video.likes, 0)}
                  </div>
                  <div className="text-gray-600">Total Likes</div>
                </div>
              </div>
            </div>
          </div>

          {/* Videos List */}
          {videos.length > 0 ? (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-800">Your Videos</h2>
              </div>
              
              <div className="divide-y divide-gray-200">
                {videos.map((video) => (
                  <div key={video.id} className="p-6 hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      {/* Video Thumbnail */}
                      <div className="relative w-32 h-20 flex-shrink-0">
                        <Image
                          src={video.thumbnail}
                          alt={video.title}
                          fill
                          className="object-cover rounded"
                        />
                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
                          {video.duration}
                        </div>
                      </div>

                      {/* Video Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-medium text-gray-900 truncate">
                          {video.title}
                        </h3>
                        <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                          <span>{video.views} views</span>
                          <span>{video.likes} likes</span>
                          <span>Uploaded {new Date(video.uploadDate).toLocaleDateString()}</span>
                        </div>
                        <div className="mt-2">
                          <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(video.status)}`}>
                            {video.status.charAt(0).toUpperCase() + video.status.slice(1)}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                          <span title="Edit">✏️</span>
                        </button>
                        <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                          <span title="Share">🔗</span>
                        </button>
                        <button 
                          onClick={() => handleDeleteVideo(video.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                        >
                          <span title="Delete">🗑️</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            /* Empty State */
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">📹</div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                No videos yet
              </h3>
              <p className="text-gray-500 mb-6">
                Start sharing your content with the decentralized world!
              </p>
              <button className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                Upload Your First Video
              </button>
            </div>
          )}
        </>
      )}

      {/* Channels View */}
      {currentView === 'channels' && (
        <ChannelManager
          userEmail={userEmail}
          onChannelSelect={handleChannelSelect}
          className="mt-6"
        />
      )}

      {/* Channel Detail View */}
      {currentView === 'channelDetail' && selectedChannel && (
        <ChannelView
          channel={selectedChannel}
          currentUserEmail={userEmail}
          onBack={handleBackToChannels}
          className="mt-6"
        />
      )}
    </div>
  )
}
