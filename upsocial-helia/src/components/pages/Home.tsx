'use client'

import { useState, useEffect } from 'react'
import CategoryTabs from '../feed/CategoryTabs'
import VideoFeed from '../feed/VideoFeed'
import SearchBar from '../feed/SearchBar'
import SwipeVideoModal from '../video/SwipeVideoModal'
import ContentService, { Video, SearchFilters } from '../../lib/data/content-service'

export default function Home() {
  const [videos, setVideos] = useState<Video[]>([])
  const [loading, setLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('NEWEST')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({})
  const [currentPage, setCurrentPage] = useState(1)
  
  // Swipe video modal state
  const [isSwipeModalOpen, setIsSwipeModalOpen] = useState(false)
  const [swipeModalVideoIndex, setSwipeModalVideoIndex] = useState(0)
  
  const contentService = ContentService.getInstance()

  // Load initial videos
  useEffect(() => {
    loadVideos(true)
  }, [selectedCategory, searchQuery, searchFilters])

  const loadVideos = async (reset: boolean = false) => {
    if (loading && !reset) return

    setLoading(true)
    
    try {
      let result
      const page = reset ? 1 : currentPage
      
      if (searchQuery) {
        result = await contentService.searchVideos(
          searchQuery,
          { ...searchFilters, category: selectedCategory !== 'NEWEST' ? selectedCategory : undefined },
          { page, limit: 20 }
        )
      } else {
        result = await contentService.getVideosByCategory(selectedCategory, { page, limit: 20 })
      }

      if (reset) {
        setVideos(result.videos)
        setCurrentPage(2)
      } else {
        setVideos(prev => [...prev, ...result.videos])
        setCurrentPage(prev => prev + 1)
      }
      
      setHasMore(result.hasMore)
    } catch (error) {
      console.error('Failed to load videos:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category)
    setCurrentPage(1)
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handleFilterChange = (filters: SearchFilters) => {
    setSearchFilters(filters)
    setCurrentPage(1)
  }

  const handleLoadMore = () => {
    loadVideos(false)
  }

  const handleVideoLike = async (videoId: string) => {
    try {
      await contentService.likeVideo(videoId)
      // Update local state
      setVideos(prev => prev.map(video => 
        video.id === videoId 
          ? { ...video, likes: video.likes + 1 }
          : video
      ))
    } catch (error) {
      console.error('Failed to like video:', error)
    }
  }

  const handleVideoShare = async (videoId: string) => {
    try {
      const shareUrl = await contentService.shareVideo(videoId)
      if (shareUrl) {
        // Copy to clipboard
        await navigator.clipboard.writeText(shareUrl)
        // TODO: Show toast notification
        console.log('Share URL copied to clipboard:', shareUrl)
      }
    } catch (error) {
      console.error('Failed to share video:', error)
    }
  }

  // New handlers for swipe modal
  const handleVideoSelect = (videoIndex: number) => {
    setSwipeModalVideoIndex(videoIndex)
    setIsSwipeModalOpen(true)
  }

  const handleSwipeModalClose = () => {
    setIsSwipeModalOpen(false)
  }

  const handleVideoSwipe = async (direction: 'left' | 'right', video: Video) => {
    try {
      if (direction === 'right') {
        // Like the video
        await contentService.likeVideo(video.id)
        setVideos(prev => prev.map(v => 
          v.id === video.id 
            ? { ...v, likes: v.likes + 1 }
            : v
        ))
      } else {
        // Dislike the video - for now just log it
        // TODO: Implement dislike functionality in ContentService
        console.log('Disliked video:', video.id)
      }
    } catch (error) {
      console.error('Failed to process swipe action:', error)
    }
  }

  const handleVideoComment = async (videoId: string) => {
    // TODO: Implement comment functionality
    console.log('Comment on video:', videoId)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Discover</h1>
                  <p className="text-gray-600 mt-1">Explore decentralized videos from creators worldwide</p>
                </div>
                <div className="hidden md:block">
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <span className="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                      IPFS Connected
                    </span>
                    <span>{videos.length} videos loaded</span>
                  </div>
                </div>
              </div>
              
              {/* Search Bar */}
              <SearchBar
                onSearch={handleSearch}
                onFilterChange={handleFilterChange}
                className="max-w-2xl"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Category Tabs */}
      <CategoryTabs
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
        className="sticky top-[180px] z-30 bg-white shadow-sm"
      />

      {/* Video Feed */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <VideoFeed
          videos={videos}
          loading={loading}
          hasMore={hasMore}
          selectedCategory={selectedCategory}
          onLoadMore={handleLoadMore}
          onVideoLike={handleVideoLike}
          onVideoShare={handleVideoShare}
          onVideoSelect={handleVideoSelect}
        />
      </div>

      {/* Swipe Video Modal */}
      <SwipeVideoModal
        isOpen={isSwipeModalOpen}
        videos={videos}
        currentIndex={swipeModalVideoIndex}
        onClose={handleSwipeModalClose}
        onSwipe={handleVideoSwipe}
        onLike={handleVideoLike}
        onShare={handleVideoShare}
        onComment={handleVideoComment}
      />
    </div>
  )
}
