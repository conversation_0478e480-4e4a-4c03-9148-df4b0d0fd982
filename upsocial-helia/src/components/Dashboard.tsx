'use client'

import { useState } from 'react'
import Onboarding from './Onboarding'
import SignUp from './auth/SignUp'
import Anonymous from './auth/Anonymous'

export default function Dashboard() {
  const [routeFlag, setRouteFlag] = useState('onboarding')

  const setFlag = (flag: string) => {
    setRouteFlag(flag)
  }

  return (
    <>
      {routeFlag === 'onboarding' && <Onboarding setFlag={setFlag} />}
      {routeFlag === 'SignUp' && <SignUp setFlag={setFlag} />}
      {routeFlag === 'Anonymous' && <Anonymous setFlag={setFlag} />}
    </>
  )
}
