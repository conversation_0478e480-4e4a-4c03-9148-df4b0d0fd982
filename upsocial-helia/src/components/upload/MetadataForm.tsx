'use client'

import { useState, useEffect } from 'react'
import { XMarkIcon, PlusIcon } from '@heroicons/react/24/outline'

const CATEGORIES = [
  'Animation', 'Autos & Vehicles', 'Beauty & Fashion', 'Comedy',
  'Cooking & Food', 'DIY & Crafts', 'Documentary', 'Education',
  'Entertainment', 'Film & Animation', 'Gaming', 'Health & Fitness',
  'How-to & Style', 'Kids & Family', 'Music', 'News & Politics',
  'Nonprofits & Activism', 'People & Blogs', 'Pets & Animals',
  'Science & Technology', 'Sports', 'Travel & Events',
  'Unboxing & Reviews', 'Blogs'
]

interface VideoMetadata {
  title: string
  description: string
  categories: string[]
  tags: string[]
}

interface MetadataFormProps {
  onSubmit: (metadata: VideoMetadata) => void
  onCancel: () => void
  initialData?: Partial<VideoMetadata>
  className?: string
}

export default function MetadataForm({
  onSubmit,
  onCancel,
  initialData,
  className = ''
}: MetadataFormProps) {
  const [formData, setFormData] = useState<VideoMetadata>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    categories: initialData?.categories || [],
    tags: initialData?.tags || []
  })

  const [newTag, setNewTag] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters'
    }

    if (formData.description.length > 5000) {
      newErrors.description = 'Description must be less than 5000 characters'
    }

    if (formData.categories.length === 0) {
      newErrors.categories = 'At least one category is required'
    } else if (formData.categories.length > 3) {
      newErrors.categories = 'Maximum 3 categories allowed'
    }

    if (formData.tags.length > 10) {
      newErrors.tags = 'Maximum 10 tags allowed'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCategoryToggle = (category: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : prev.categories.length < 3
          ? [...prev.categories, category]
          : prev.categories
    }))
  }

  const handleAddTag = () => {
    const tag = newTag.trim().toLowerCase()
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
      setNewTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Video Details</h3>
        <p className="text-gray-600">Add information about your video to help others discover it</p>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <input
            id="title"
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${
              errors.title 
                ? 'border-red-300 focus:ring-red-500' 
                : 'border-gray-300 focus:ring-purple-500 focus:border-purple-500'
            }`}
            placeholder="Enter a descriptive title for your video"
            maxLength={100}
          />
          <div className="flex justify-between mt-1">
            {errors.title && <p className="text-red-600 text-sm">{errors.title}</p>}
            <p className="text-gray-500 text-sm ml-auto">{formData.title.length}/100</p>
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={4}
            className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 transition-colors resize-vertical ${
              errors.description 
                ? 'border-red-300 focus:ring-red-500' 
                : 'border-gray-300 focus:ring-purple-500 focus:border-purple-500'
            }`}
            placeholder="Tell viewers about your video (optional)"
            maxLength={5000}
          />
          <div className="flex justify-between mt-1">
            {errors.description && <p className="text-red-600 text-sm">{errors.description}</p>}
            <p className="text-gray-500 text-sm ml-auto">{formData.description.length}/5000</p>
          </div>
        </div>

        {/* Categories */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Categories * (Select up to 3)
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-48 overflow-y-auto border rounded-lg p-4">
            {CATEGORIES.map(category => (
              <button
                key={category}
                type="button"
                onClick={() => handleCategoryToggle(category)}
                className={`
                  px-3 py-2 text-sm rounded-lg text-left transition-all duration-200 transform hover:scale-105
                  ${formData.categories.includes(category)
                    ? 'bg-purple-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-purple-100 hover:text-purple-700'
                  }
                  ${formData.categories.length >= 3 && !formData.categories.includes(category)
                    ? 'opacity-50 cursor-not-allowed'
                    : 'cursor-pointer'
                  }
                `}
                disabled={formData.categories.length >= 3 && !formData.categories.includes(category)}
              >
                {category}
              </button>
            ))}
          </div>
          <div className="flex justify-between mt-1">
            {errors.categories && <p className="text-red-600 text-sm">{errors.categories}</p>}
            <p className="text-gray-500 text-sm ml-auto">{formData.categories.length}/3 selected</p>
          </div>
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tags (Up to 10)
          </label>
          
          {/* Tag Input */}
          <div className="flex space-x-2 mb-3">
            <input
              type="text"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="Add a tag (press Enter to add)"
              maxLength={20}
              disabled={formData.tags.length >= 10}
            />
            <button
              type="button"
              onClick={handleAddTag}
              disabled={!newTag.trim() || formData.tags.length >= 10}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              <PlusIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Tag List */}
          <div className="flex flex-wrap gap-2 min-h-[40px] p-3 border border-gray-200 rounded-lg bg-gray-50">
            {formData.tags.length === 0 ? (
              <p className="text-gray-500 text-sm">No tags added yet</p>
            ) : (
              formData.tags.map(tag => (
                <span
                  key={tag}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800"
                >
                  #{tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-2 text-purple-600 hover:text-purple-800"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </span>
              ))
            )}
          </div>
          <div className="flex justify-between mt-1">
            {errors.tags && <p className="text-red-600 text-sm">{errors.tags}</p>}
            <p className="text-gray-500 text-sm ml-auto">{formData.tags.length}/10 tags</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 text-gray-600 hover:text-gray-700 font-medium transition-colors"
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium transition-colors shadow-lg hover:shadow-xl transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isSubmitting ? 'Processing...' : 'Continue to Upload'}
          </button>
        </div>
      </form>
    </div>
  )
}