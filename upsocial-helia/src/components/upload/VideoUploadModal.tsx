'use client'

import { useState, useRef, useEffect } from 'react'
import { X, Upload, Film } from 'lucide-react'
import styles from './VideoUploadModal.module.css'
import { VideoStorageService, VideoMetadata } from '@/lib/ipfs/video-storage'

interface VideoUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUploadComplete?: (videoData: any) => void
}

export default function VideoUploadModal({ isOpen, onClose, onUploadComplete }: VideoUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [category, setCategory] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)

  // Update progress bar width
  useEffect(() => {
    if (progressBarRef.current) {
      progressBarRef.current.style.width = `${uploadProgress}%`
    }
  }, [uploadProgress])

  if (!isOpen) return null

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type.startsWith('video/')) {
      setSelectedFile(file)
    }
  }

  const handleSubmit = async () => {
    if (!selectedFile || !title.trim()) {
      alert('Please select a video file and enter a title')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      console.log('Starting video upload to IPFS...')
      
      // Initialize IPFS storage service
      const storageService = VideoStorageService.getInstance()
      await storageService.initialize()
      
      // Update progress
      setUploadProgress(20)
      
      // Upload video to IPFS
      console.log('Uploading video file...')
      const videoCID = await storageService.uploadVideo(selectedFile)
      setUploadProgress(50)
      
      // Generate and upload thumbnail
      console.log('Generating thumbnail...')
      const thumbnailDataUrl = await storageService.generateThumbnail(selectedFile)
      const thumbnailCID = await storageService.uploadThumbnail(thumbnailDataUrl)
      setUploadProgress(70)
      
      // Get video duration
      const duration = await storageService.getVideoDuration(selectedFile)
      setUploadProgress(80)
      
      // Create metadata
      const metadata: VideoMetadata = {
        title,
        description,
        category,
        duration,
        size: selectedFile.size,
        createdAt: new Date().toISOString(),
        videoCID,
        thumbnailCID,
        creator: 'Anonymous' // TODO: Get from auth context
      }
      
      // Upload metadata to IPFS
      console.log('Uploading metadata...')
      const metadataCID = await storageService.uploadMetadata(metadata)
      setUploadProgress(90)
      
      // Create video object for local storage
      const videoData = {
        id: metadataCID,
        title,
        description,
        creator: 'Anonymous',
        thumbnail: `https://ipfs.io/ipfs/${thumbnailCID}`,
        videoUrl: `https://ipfs.io/ipfs/${videoCID}`,
        ipfsHash: videoCID,
        views: 0,
        likes: 0,
        duration: `${Math.floor(duration / 60)}:${Math.floor(duration % 60).toString().padStart(2, '0')}`,
        createdAt: new Date(),
        categories: [category],
        metadata: {
          resolution: '720p', // TODO: Detect actual resolution
          bitrate: 1000, // TODO: Calculate actual bitrate
          codec: 'h264',
          fps: 30,
          size: selectedFile.size
        }
      }
      
      // Store in localStorage for now (TODO: Replace with proper database)
      const existingVideos = JSON.parse(localStorage.getItem('videos') || '[]')
      existingVideos.push(videoData)
      localStorage.setItem('videos', JSON.stringify(existingVideos))
      
      setUploadProgress(100)
      
      // Call the upload complete callback
      onUploadComplete?.(videoData)
      
      console.log('Video uploaded successfully!', {
        videoCID,
        thumbnailCID,
        metadataCID,
        title
      })
      
      // Reset form
      setSelectedFile(null)
      setTitle('')
      setDescription('')
      setCategory('')
      setUploadProgress(0)
      
      alert('Video uploaded successfully to IPFS!')
      // onClose() will be called by the parent via handleUploadComplete
      
    } catch (error) {
      console.error('Upload failed:', error)
      alert(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsUploading(false)
    }
  }

  const categories = [
    'Entertainment', 'Gaming', 'Music', 'Technology', 'Education',
    'Comedy', 'Sports', 'News', 'Fashion', 'Food', 'Travel', 'Art'
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="relative bg-gray-900 rounded-lg max-w-md w-full max-h-[90vh] overflow-auto">
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white z-10"
          title="Close"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="p-6">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <Film className="w-6 h-6 mr-2" />
            Upload Video
          </h2>

          {/* File Upload Area */}
          <div 
            className={`border-2 border-dashed ${selectedFile ? 'border-green-500' : 'border-gray-600'} rounded-lg p-6 mb-4 text-center cursor-pointer hover:border-purple-500 transition-colors`}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleFileSelect}
              className="hidden"
              aria-label="Select video file"
            />
            
            {selectedFile ? (
              <div className="text-green-400">
                <Film className="w-12 h-12 mx-auto mb-2" />
                <p className="font-semibold">{selectedFile.name}</p>
                <p className="text-sm text-gray-400">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
              </div>
            ) : (
              <div className="text-gray-400">
                <Upload className="w-12 h-12 mx-auto mb-2" />
                <p>Click to select a video file</p>
                <p className="text-sm">MP4, MOV, AVI supported</p>
              </div>
            )}
          </div>

          {/* Form Fields */}
          <div className="space-y-4">
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Title *
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter video title"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                required
              />
            </div>

            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Description
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe your video..."
                rows={3}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
              />
            </div>

            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Category
              </label>
                            <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                title="Select video category"
              >
                {categories.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Upload Progress */}
          {isUploading && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-400 mb-1">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <div className={styles.progressBarContainer}>
                <div 
                  ref={progressBarRef}
                  className={styles.progressBar}
                  role="progressbar"
                  aria-label={`Upload progress: ${uploadProgress}%`}
                />
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3 mt-6">
            <button
              onClick={onClose}
              disabled={isUploading}
              className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 text-white rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={!selectedFile || !title.trim() || isUploading}
              className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white rounded-lg transition-colors"
            >
              {isUploading ? 'Uploading...' : 'Upload'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
