'use client'

import { useState, useEffect } from 'react'
import { CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/solid'

export interface UploadStep {
  id: string
  label: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress?: number
  error?: string
}

interface ProgressTrackerProps {
  steps: UploadStep[]
  onCancel?: () => void
  onRetry?: (stepId: string) => void
  className?: string
}

export default function ProgressTracker({
  steps,
  onCancel,
  onRetry,
  className = ''
}: ProgressTrackerProps) {
  const [overallProgress, setOverallProgress] = useState(0)

  useEffect(() => {
    const completedSteps = steps.filter(step => step.status === 'completed').length
    const progress = (completedSteps / steps.length) * 100
    setOverallProgress(progress)
  }, [steps])

  const getStepIcon = (step: UploadStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />
      case 'error':
        return <XCircleIcon className="w-6 h-6 text-red-500" />
      case 'processing':
        return (
          <div className="w-6 h-6 border-2 border-purple-500 border-t-transparent rounded-full animate-spin" />
        )
      default:
        return <ClockIcon className="w-6 h-6 text-gray-400" />
    }
  }

  const getStepStatusText = (step: UploadStep) => {
    switch (step.status) {
      case 'completed':
        return 'Completed'
      case 'error':
        return 'Failed'
      case 'processing':
        return step.progress !== undefined ? `${Math.round(step.progress)}%` : 'Processing...'
      default:
        return 'Waiting...'
    }
  }

  const hasErrors = steps.some(step => step.status === 'error')
  const isCompleted = steps.every(step => step.status === 'completed')
  const isProcessing = steps.some(step => step.status === 'processing')

  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {isCompleted ? 'Upload Complete!' : 'Uploading Video...'}
            </h3>
            <p className="text-gray-600">
              {isCompleted 
                ? 'Your video has been successfully uploaded and is now available!'
                : 'Please wait while we process and upload your video to IPFS'
              }
            </p>
          </div>
          
          {!isCompleted && onCancel && (
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-700 font-medium transition-colors"
            >
              Cancel
            </button>
          )}
        </div>

        {/* Overall Progress Bar */}
        <div className="mt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-600">{Math.round(overallProgress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                hasErrors ? 'bg-red-500' : isCompleted ? 'bg-green-500' : 'bg-purple-500'
              }`}
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Steps */}
      <div className="p-6 space-y-4">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-200 ${
              step.status === 'processing'
                ? 'bg-purple-50 border border-purple-200'
                : step.status === 'completed'
                ? 'bg-green-50 border border-green-200'
                : step.status === 'error'
                ? 'bg-red-50 border border-red-200'
                : 'bg-gray-50 border border-gray-200'
            }`}
          >
            {/* Step Number */}
            <div className={`
              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
              ${step.status === 'completed'
                ? 'bg-green-500 text-white'
                : step.status === 'error'
                ? 'bg-red-500 text-white'
                : step.status === 'processing'
                ? 'bg-purple-500 text-white'
                : 'bg-gray-300 text-gray-600'
              }
            `}>
              {index + 1}
            </div>

            {/* Step Icon */}
            <div className="flex-shrink-0">
              {getStepIcon(step)}
            </div>

            {/* Step Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {step.label}
                </h4>
                <span className={`text-sm font-medium ${
                  step.status === 'completed'
                    ? 'text-green-600'
                    : step.status === 'error'
                    ? 'text-red-600'
                    : step.status === 'processing'
                    ? 'text-purple-600'
                    : 'text-gray-500'
                }`}>
                  {getStepStatusText(step)}
                </span>
              </div>

              {/* Individual Progress Bar */}
              {step.status === 'processing' && step.progress !== undefined && (
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-1">
                    <div
                      className="h-1 bg-purple-500 rounded-full transition-all duration-300"
                      style={{ width: `${step.progress}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Error Message */}
              {step.status === 'error' && step.error && (
                <p className="mt-1 text-sm text-red-600">{step.error}</p>
              )}
            </div>

            {/* Retry Button */}
            {step.status === 'error' && onRetry && (
              <button
                onClick={() => onRetry(step.id)}
                className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
              >
                Retry
              </button>
            )}
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-6 border-t border-gray-200 bg-gray-50">
        {isCompleted ? (
          <div className="text-center">
            <div className="text-green-500 text-4xl mb-2">🎉</div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Your video is live!
            </h4>
            <p className="text-gray-600 mb-4">
              Your video has been successfully uploaded to IPFS and is now available for viewing.
            </p>
            <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium transition-colors shadow-lg hover:shadow-xl transform hover:scale-105">
              View Video
            </button>
          </div>
        ) : hasErrors ? (
          <div className="text-center">
            <div className="text-red-500 text-4xl mb-2">❌</div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Upload Failed
            </h4>
            <p className="text-gray-600 mb-4">
              Some steps failed during the upload process. Please check the errors above and retry.
            </p>
            <div className="flex items-center justify-center space-x-3">
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="px-4 py-2 text-gray-600 hover:text-gray-700 font-medium transition-colors"
                >
                  Cancel Upload
                </button>
              )}
              <button className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 font-medium transition-colors">
                Retry All Failed Steps
              </button>
            </div>
          </div>
        ) : isProcessing ? (
          <div className="text-center">
            <div className="animate-pulse text-purple-500 text-4xl mb-2">⏳</div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Processing Your Video
            </h4>
            <p className="text-gray-600">
              Please keep this page open while we upload your video to the decentralized network.
            </p>
          </div>
        ) : (
          <div className="text-center">
            <div className="text-gray-400 text-4xl mb-2">📤</div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Ready to Upload
            </h4>
            <p className="text-gray-600">
              Click start to begin uploading your video to IPFS.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}