'use client'

import { useState, useRef, useEffect } from 'react'
import { CheckIcon, ArrowPathIcon } from '@heroicons/react/24/outline'

interface ThumbnailOption {
  id: string
  canvas: HTMLCanvasElement
  timeIndex: number
  selected: boolean
}

interface ThumbnailGeneratorProps {
  videoFile: File
  onThumbnailSelect: (canvas: HTMLCanvasElement) => void
  className?: string
}

export default function ThumbnailGenerator({
  videoFile,
  onThumbnailSelect,
  className = ''
}: ThumbnailGeneratorProps) {
  const [thumbnails, setThumbnails] = useState<ThumbnailOption[]>([])
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    generateThumbnails()
  }, [videoFile])

  const generateThumbnails = async () => {
    setIsGenerating(true)
    setError(null)
    
    try {
      const video = videoRef.current
      if (!video) return

      const videoUrl = URL.createObjectURL(videoFile)
      video.src = videoUrl

      await new Promise((resolve, reject) => {
        video.onloadedmetadata = resolve
        video.onerror = reject
      })

      const duration = video.duration
      const timePoints = [
        duration * 0.1,  // 10% into video
        duration * 0.5,  // 50% into video
        duration * 0.8   // 80% into video
      ]

      const newThumbnails: ThumbnailOption[] = []

      for (let i = 0; i < timePoints.length; i++) {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) continue

        // Set canvas size to match video aspect ratio
        const aspectRatio = video.videoWidth / video.videoHeight
        canvas.width = 320
        canvas.height = 320 / aspectRatio

        // Seek to time point
        video.currentTime = timePoints[i]
        
        await new Promise((resolve) => {
          video.onseeked = resolve
        })

        // Draw video frame to canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

        newThumbnails.push({
          id: `thumbnail-${i}`,
          canvas,
          timeIndex: i,
          selected: i === 0 // Select first thumbnail by default
        })
      }

      setThumbnails(newThumbnails)
      
      // Auto-select first thumbnail
      if (newThumbnails.length > 0) {
        setSelectedThumbnail(newThumbnails[0].id)
        onThumbnailSelect(newThumbnails[0].canvas)
      }

      URL.revokeObjectURL(videoUrl)
    } catch (error) {
      console.error('Error generating thumbnails:', error)
      setError('Failed to generate thumbnails. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleThumbnailSelect = (thumbnailId: string) => {
    const thumbnail = thumbnails.find(t => t.id === thumbnailId)
    if (!thumbnail) return

    setSelectedThumbnail(thumbnailId)
    onThumbnailSelect(thumbnail.canvas)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden ${className}`}>
      {/* Hidden video element for thumbnail generation */}
      <video
        ref={videoRef}
        style={{ display: 'none' }}
        preload="metadata"
      />

      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Choose Thumbnail</h3>
        <p className="text-gray-600">
          Select the thumbnail that best represents your video content
        </p>
      </div>

      <div className="p-6">
        {isGenerating ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mb-4"></div>
            <p className="text-gray-600">Generating thumbnails...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <p className="text-red-600 mb-4">{error}</p>
            <button
              onClick={generateThumbnails}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors inline-flex items-center space-x-2"
            >
              <ArrowPathIcon className="w-4 h-4" />
              <span>Try Again</span>
            </button>
          </div>
        ) : (
          <>
            {/* Thumbnail Options */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {thumbnails.map((thumbnail, index) => (
                <div
                  key={thumbnail.id}
                  className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 transform hover:scale-105 ${
                    selectedThumbnail === thumbnail.id
                      ? 'border-purple-500 shadow-lg'
                      : 'border-gray-200 hover:border-purple-300'
                  }`}
                  onClick={() => handleThumbnailSelect(thumbnail.id)}
                >
                  {/* Thumbnail Image */}
                  <div className="aspect-video bg-gray-100">
                    <canvas
                      ref={(canvasRef) => {
                        if (canvasRef && thumbnail.canvas) {
                          const ctx = canvasRef.getContext('2d')
                          if (ctx) {
                            canvasRef.width = thumbnail.canvas.width
                            canvasRef.height = thumbnail.canvas.height
                            ctx.drawImage(thumbnail.canvas, 0, 0)
                          }
                        }
                      }}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Selection Indicator */}
                  {selectedThumbnail === thumbnail.id && (
                    <div className="absolute top-2 right-2 bg-purple-600 text-white rounded-full p-1">
                      <CheckIcon className="w-4 h-4" />
                    </div>
                  )}

                  {/* Time Label */}
                  <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                    {thumbnail.timeIndex === 0 && 'Beginning'}
                    {thumbnail.timeIndex === 1 && 'Middle'}
                    {thumbnail.timeIndex === 2 && 'End'}
                  </div>
                </div>
              ))}
            </div>

            {/* Selected Thumbnail Preview */}
            {selectedThumbnail && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Selected Thumbnail</h4>
                <div className="flex items-center space-x-4">
                  <div className="w-20 h-12 bg-gray-200 rounded overflow-hidden">
                    {(() => {
                      const selected = thumbnails.find(t => t.id === selectedThumbnail)
                      return selected ? (
                        <canvas
                          ref={(canvasRef) => {
                            if (canvasRef && selected.canvas) {
                              const ctx = canvasRef.getContext('2d')
                              if (ctx) {
                                canvasRef.width = selected.canvas.width
                                canvasRef.height = selected.canvas.height
                                ctx.drawImage(selected.canvas, 0, 0)
                              }
                            }
                          }}
                          className="w-full h-full object-cover"
                        />
                      ) : null
                    })()}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600">
                      This thumbnail will be displayed when users browse your video
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Tips */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Thumbnail Tips</h4>
              <ul className="text-xs text-blue-800 space-y-1">
                <li>• Choose a clear, engaging image that represents your content</li>
                <li>• Avoid blurry or dark frames</li>
                <li>• Consider which thumbnail would make you want to click</li>
                <li>• The thumbnail will be automatically optimized for different screen sizes</li>
              </ul>
            </div>
          </>
        )}
      </div>
    </div>
  )
}