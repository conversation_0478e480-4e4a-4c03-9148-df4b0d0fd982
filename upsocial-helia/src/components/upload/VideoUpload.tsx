'use client'

import { useState, useRef, useCallback } from 'react'
import { CloudArrowUpIcon, FilmIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface VideoUploadProps {
  onFileSelect: (file: File) => void
  onCancel?: () => void
  maxSize?: number // in MB
  acceptedFormats?: string[]
  className?: string
}

export default function VideoUpload({
  onFileSelect,
  onCancel,
  maxSize = 500,
  acceptedFormats = ['mp4', 'webm', 'mov', 'avi'],
  className = ''
}: VideoUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file size
    const fileSizeMB = file.size / (1024 * 1024)
    if (fileSizeMB > maxSize) {
      return `File size must be less than ${maxSize}MB`
    }

    // Check file format
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!fileExtension || !acceptedFormats.includes(fileExtension)) {
      return `Please select a valid video file (${acceptedFormats.join(', ')})`
    }

    // Check if it's actually a video file
    if (!file.type.startsWith('video/')) {
      return 'Please select a valid video file'
    }

    return null
  }

  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    setError(null)
    onFileSelect(file)
  }, [onFileSelect, maxSize, acceptedFormats])

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  return (
    <div className={`w-full ${className}`}>
      {/* Upload Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300 cursor-pointer
          ${isDragOver
            ? 'border-purple-500 bg-purple-50'
            : error
            ? 'border-red-300 bg-red-50'
            : 'border-gray-300 bg-white hover:border-purple-400 hover:bg-purple-50'
          }
        `}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        {/* Cancel Button */}
        {onCancel && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onCancel()
            }}
            className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        )}

        {/* Upload Icon */}
        <div className="mb-6">
          {isDragOver ? (
            <CloudArrowUpIcon className="w-16 h-16 text-purple-500 mx-auto animate-bounce" />
          ) : (
            <FilmIcon className="w-16 h-16 text-gray-400 mx-auto" />
          )}
        </div>

        {/* Upload Text */}
        <div className="mb-6">
          <h3 className={`text-xl font-semibold mb-2 ${error ? 'text-red-700' : 'text-gray-900'}`}>
            {isDragOver
              ? 'Drop your video here'
              : error
              ? 'Upload Error'
              : 'Upload Your Video'
            }
          </h3>
          
          {error ? (
            <p className="text-red-600">{error}</p>
          ) : (
            <div className="text-gray-600">
              <p className="mb-2">
                Drag and drop your video file here, or{' '}
                <span className="text-purple-600 font-medium">browse</span>
              </p>
              <div className="text-sm">
                <p>Supported formats: {acceptedFormats.join(', ').toUpperCase()}</p>
                <p>Maximum file size: {maxSize}MB</p>
              </div>
            </div>
          )}
        </div>

        {/* Upload Button */}
        <button
          type="button"
          className={`
            px-6 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105
            ${error
              ? 'bg-red-100 text-red-700 hover:bg-red-200'
              : 'bg-purple-600 text-white hover:bg-purple-700 shadow-lg hover:shadow-xl'
            }
          `}
          onClick={(e) => {
            e.stopPropagation()
            if (error) {
              setError(null)
            } else {
              openFileDialog()
            }
          }}
        >
          {error ? 'Try Again' : 'Select Video File'}
        </button>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptedFormats.map(format => `.${format}`).join(',')}
          onChange={handleInputChange}
          className="hidden"
        />
      </div>

      {/* File Format Help */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Supported Video Formats</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600">
          {acceptedFormats.map(format => (
            <div key={format} className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="uppercase font-medium">{format}</span>
            </div>
          ))}
        </div>
        <p className="text-xs text-gray-500 mt-3">
          For best quality and compatibility, we recommend MP4 format with H.264 encoding.
        </p>
      </div>

      {/* Upload Tips */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Upload Tips</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Higher resolution videos may take longer to process</li>
          <li>• Videos are stored securely on IPFS for decentralized access</li>
          <li>• Processing time depends on video length and quality</li>
        </ul>
      </div>
    </div>
  )
}