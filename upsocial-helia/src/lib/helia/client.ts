import { createHelia } from 'helia'
import { unixfs } from '@helia/unixfs'
import { ipns } from '@helia/ipns'
import { strings } from '@helia/strings'
import type { Helia } from '@helia/interface'
import type { UnixFS } from '@helia/unixfs'
import type { IPNS } from '@helia/ipns'
import type { Strings } from '@helia/strings'

export interface HeliaInstance {
  helia: Helia
  fs: UnixFS
  ipns: IPNS
  strings: Strings
}

let heliaInstance: HeliaInstance | null = null

export async function createHeliaInstance(): Promise<HeliaInstance> {
  try {
    console.log('Creating Helia instance...')
    
    // Create Helia instance with simpler configuration for better reliability
    const helia = await createHelia({
      // Use default configuration but with custom settings
      blockstore: undefined, // Use default blockstore
      datastore: undefined,   // Use default datastore
    })

    console.log('Helia instance created, initializing subsystems...')

    // Initialize IPFS subsystems
    const fs = unixfs(helia)
    const ipnsInstance = ipns(helia)
    const stringInstance = strings(helia)

    heliaInstance = {
      helia,
      fs,
      ipns: ipnsInstance,
      strings: stringInstance
    }

    console.log('Helia initialized successfully')
    console.log('Peer ID:', helia.libp2p.peerId.toString())

    // Test the connection by doing a simple operation
    try {
      const testCID = await stringInstance.add('test')
      console.log('Helia test successful, test CID:', testCID.toString())
    } catch (testError) {
      console.warn('Helia test operation failed, but instance is ready:', testError)
    }

    return heliaInstance
  } catch (error) {
    console.error('Failed to initialize Helia:', error)
    // Clear the instance on failure
    heliaInstance = null
    throw new Error(`Helia initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export async function getHeliaInstance(): Promise<HeliaInstance> {
  if (!heliaInstance) {
    console.log('No existing Helia instance, creating new one...')
    return await createHeliaInstance()
  }
  
  // Check if the instance is still valid
  try {
    if (heliaInstance.helia.libp2p.status === 'stopped') {
      console.log('Helia instance was stopped, creating new one...')
      heliaInstance = null
      return await createHeliaInstance()
    }
  } catch (error) {
    console.warn('Error checking Helia status, creating new instance:', error)
    heliaInstance = null
    return await createHeliaInstance()
  }
  
  return heliaInstance
}

export async function stopHelia(): Promise<void> {
  if (heliaInstance) {
    await heliaInstance.helia.stop()
    heliaInstance = null
    console.log('Helia stopped')
  }
}
