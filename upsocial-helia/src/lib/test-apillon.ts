// Simple test to verify Apillon integration
async function testApillonIntegration() {
  console.log('🧪 Testing Apillon Integration...')
  
  const testData = {
    type: 'test',
    data: { 
      message: 'Hello Apillon!', 
      timestamp: Date.now(),
      user: 'test-user'
    },
    timestamp: Date.now()
  }
  
  const testCid = 'test-' + Date.now()
  
  try {
    // Test upload
    console.log('1️⃣ Testing upload endpoint...')
    const uploadResponse = await fetch('/api/apillon/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        cid: testCid,
        data: JSON.stringify(testData)
      })
    })
    
    const uploadResult = await uploadResponse.json()
    console.log('Upload response:', uploadResult)
    
    if (uploadResponse.ok) {
      console.log('✅ Upload successful')
      
      // Wait a moment for processing
      console.log('⏳ Waiting for file to be processed...')
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Test retrieve
      console.log('2️⃣ Testing retrieve endpoint...')
      const retrieveResponse = await fetch(`/api/apillon/retrieve?cid=${testCid}`)
      
      if (retrieveResponse.ok) {
        const retrieveResult = await retrieveResponse.text()
        console.log('Retrieve response:', retrieveResult)
        
        const parsedData = JSON.parse(retrieveResult)
        if (parsedData.data.message === testData.data.message) {
          console.log('✅ Round-trip test successful!')
          console.log('🎉 Apillon integration is working correctly!')
        } else {
          console.log('❌ Data integrity check failed')
        }
      } else {
        const retrieveError = await retrieveResponse.json()
        console.log('❌ Retrieve failed:', retrieveError)
        console.log('ℹ️ This might be expected if the file hasn\'t propagated yet')
      }
    } else {
      console.log('❌ Upload failed:', uploadResult)
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error)
  }
}

// Run test if in browser
if (typeof window !== 'undefined') {
  window.testApillonIntegration = testApillonIntegration
  console.log('Test function available: testApillonIntegration()')
}

export { testApillonIntegration }
