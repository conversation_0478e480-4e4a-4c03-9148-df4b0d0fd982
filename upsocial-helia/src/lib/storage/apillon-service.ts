// Apillon Web3 Storage Service for file and metadata management
export interface ApillonUploadResult {
  success: boolean;
  cid?: string;
  uuid?: string;
  url?: string;
  sessionUuid?: string;
  error?: string;
}

export interface ApillonFileStatus {
  fileName: string;
  status: string;
  CID?: string;
  fileUuid?: string;
}

export class ApillonStorageService {
  private apiKey: string;
  private apiSecret: string;
  private bucketUuid: string;
  private ipnsUuid: string;
  private baseUrl = 'https://api.apillon.io';
  private configured: boolean;

  constructor() {
    // Enhanced environment variable checking with detailed logging
    const apiKey = process.env.NEXT_PUBLIC_APILLON_API_KEY;
    const apiSecret = process.env.NEXT_PUBLIC_APILLON_API_SECRET;
    const bucketUuid = process.env.NEXT_PUBLIC_APILLON_BUCKET_UUID;
    const ipnsUuid = process.env.NEXT_PUBLIC_APILLON_IPNS_UUID;

    // Log detailed environment variable status
    console.log('🔍 Apillon Environment Variable Check:');
    console.log(`  NEXT_PUBLIC_APILLON_API_KEY: ${apiKey ? '✅ Set (' + apiKey.substring(0, 8) + '...)' : '❌ Missing'}`);
    console.log(`  NEXT_PUBLIC_APILLON_API_SECRET: ${apiSecret ? '✅ Set (' + apiSecret.substring(0, 8) + '...)' : '❌ Missing'}`);
    console.log(`  NEXT_PUBLIC_APILLON_BUCKET_UUID: ${bucketUuid ? '✅ Set (' + bucketUuid + ')' : '❌ Missing'}`);
    console.log(`  NEXT_PUBLIC_APILLON_IPNS_UUID: ${ipnsUuid ? '✅ Set (' + ipnsUuid + ')' : '❌ Missing'}`);

    // Check for old variable names (without NEXT_PUBLIC_ prefix)
    const oldApiKey = process.env.APILLON_API_KEY;
    const oldApiSecret = process.env.APILLON_API_SECRET;
    const oldBucketUuid = process.env.APILLON_BUCKET_UUID;
    const oldIpnsUuid = process.env.APILLON_IPNS_UUID;

    if (oldApiKey || oldApiSecret || oldBucketUuid || oldIpnsUuid) {
      console.warn('⚠️ Found old Apillon environment variables without NEXT_PUBLIC_ prefix:');
      if (oldApiKey) console.warn(`  APILLON_API_KEY found but needs to be NEXT_PUBLIC_APILLON_API_KEY`);
      if (oldApiSecret) console.warn(`  APILLON_API_SECRET found but needs to be NEXT_PUBLIC_APILLON_API_SECRET`);
      if (oldBucketUuid) console.warn(`  APILLON_BUCKET_UUID found but needs to be NEXT_PUBLIC_APILLON_BUCKET_UUID`);
      if (oldIpnsUuid) console.warn(`  APILLON_IPNS_UUID found but needs to be NEXT_PUBLIC_APILLON_IPNS_UUID`);
    }

    const missingVars = [];
    if (!apiKey) missingVars.push('NEXT_PUBLIC_APILLON_API_KEY');
    if (!apiSecret) missingVars.push('NEXT_PUBLIC_APILLON_API_SECRET');
    if (!bucketUuid) missingVars.push('NEXT_PUBLIC_APILLON_BUCKET_UUID');
    // IPNS is optional for basic storage functionality
    if (!ipnsUuid) {
      console.warn('⚠️ NEXT_PUBLIC_APILLON_IPNS_UUID is missing - IPNS features will be disabled');
    }

    if (missingVars.length > 0) {
      console.warn(`❌ Apillon configuration incomplete. Missing: ${missingVars.join(', ')}`);
      console.warn('   Please set these environment variables in your Vercel dashboard.');
      console.warn('   Apillon features will be disabled.');
      this.configured = false;
      // Set empty values to prevent TypeScript errors
      this.apiKey = '';
      this.apiSecret = '';
      this.bucketUuid = '';
      this.ipnsUuid = '';
      return;
    }

    console.log('✅ Core Apillon environment variables are configured correctly!');
    if (ipnsUuid) {
      console.log('✅ IPNS functionality is also available!');
    }
    
    this.apiKey = apiKey!;
    this.apiSecret = apiSecret!;
    this.bucketUuid = bucketUuid!;
    this.ipnsUuid = ipnsUuid || ''; // IPNS is optional
    this.configured = true;
  }

  private getAuthHeaders(): Record<string, string> {
    const auth = btoa(`${this.apiKey}:${this.apiSecret}`);
    return {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/json',
    };
  }

  // Upload file to Apillon storage bucket
  async uploadFile(file: File, fileName?: string): Promise<ApillonUploadResult> {
    if (!this.configured) {
      return {
        success: false,
        error: 'Apillon service not configured'
      };
    }

    try {
      console.log(`🚀 Starting Apillon upload for: ${fileName || file.name}`);

      // Step 1: Get upload URL from Apillon
      const uploadUrlResponse = await fetch(`${this.baseUrl}/storage/buckets/${this.bucketUuid}/upload`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          files: [{
            fileName: fileName || file.name,
            contentType: file.type
          }]
        })
      });

      if (!uploadUrlResponse.ok) {
        const error = await uploadUrlResponse.text();
        throw new Error(`Failed to get upload URL: ${error}`);
      }

      const uploadData = await uploadUrlResponse.json();
      const sessionUuid = uploadData.data.sessionUuid;
      const uploadUrl = uploadData.data.files[0].url;

      console.log(`📡 Got upload URL for session: ${sessionUuid}`);

      // Step 2: Upload file to the provided URL
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await fetch(uploadUrl, {
        method: 'POST',
        body: formData
      });

      if (!uploadResponse.ok) {
        throw new Error(`Upload failed: ${uploadResponse.statusText}`);
      }

      console.log(`✅ File uploaded successfully to Apillon`);

      // Step 3: End upload session to finalize
      const endSessionResponse = await fetch(`${this.baseUrl}/storage/buckets/${this.bucketUuid}/upload/${sessionUuid}/end`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!endSessionResponse.ok) {
        console.warn(`⚠️ Failed to end upload session, but file may still be uploaded`);
      }

      // Step 4: Wait for processing and get CID
      const status = await this.waitForFileProcessing(sessionUuid, fileName || file.name);

      return {
        success: true,
        sessionUuid,
        cid: status.CID,
        uuid: status.fileUuid,
        url: status.CID ? `https://ipfs.apillon.io/ipfs/${status.CID}` : undefined
      };

    } catch (error) {
      console.error('❌ Apillon upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Upload JSON metadata to Apillon
  async uploadJSON(data: any, fileName: string = 'metadata.json'): Promise<ApillonUploadResult> {
    if (!this.configured) {
      return {
        success: false,
        error: 'Apillon service not configured'
      };
    }

    try {
      const jsonBlob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const file = new File([jsonBlob], fileName, { type: 'application/json' });
      
      return await this.uploadFile(file, fileName);
    } catch (error) {
      console.error('❌ Apillon JSON upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Wait for file processing to complete and get CID
  private async waitForFileProcessing(sessionUuid: string, fileName: string, maxAttempts: number = 30): Promise<ApillonFileStatus> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/storage/buckets/${this.bucketUuid}/files`, {
          headers: this.getAuthHeaders()
        });

        if (response.ok) {
          const data = await response.json();
          const file = data.data.items.find((f: any) => f.name === fileName);
          
          if (file && file.CID) {
            console.log(`✅ File processed successfully: CID ${file.CID}`);
            return {
              fileName,
              status: 'processed',
              CID: file.CID,
              fileUuid: file.uuid
            };
          }
        }

        console.log(`⏳ Waiting for file processing... (${attempt}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds

      } catch (error) {
        console.warn(`⚠️ Error checking file status (attempt ${attempt}):`, error);
      }
    }

    throw new Error(`File processing timeout after ${maxAttempts} attempts`);
  }

  // List files in the bucket
  async listFiles(): Promise<any[]> {
    if (!this.configured) {
      console.warn('⚠️ Apillon service not configured, returning empty list');
      return [];
    }

    try {
      const response = await fetch(`${this.baseUrl}/storage/buckets/${this.bucketUuid}/files`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to list files: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data.items || [];
    } catch (error) {
      console.error('❌ Failed to list Apillon files:', error);
      return [];
    }
  }

  // Publish to IPNS (if configured)
  async publishToIPNS(cid: string): Promise<boolean> {
    if (!this.configured) {
      console.warn('⚠️ Apillon service not configured, skipping IPNS publishing');
      return false;
    }

    try {
      if (!this.ipnsUuid) {
        console.warn('⚠️ IPNS UUID not configured, skipping IPNS publishing');
        return false;
      }

      const response = await fetch(`${this.baseUrl}/storage/ipns/${this.ipnsUuid}/publish`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          cid: cid
        })
      });

      if (!response.ok) {
        throw new Error(`IPNS publish failed: ${response.statusText}`);
      }

      console.log(`✅ Successfully published to IPNS: ${cid}`);
      return true;
    } catch (error) {
      console.error('❌ IPNS publish failed:', error);
      return false;
    }
  }

  // Get file details by CID
  async getFileDetails(cid: string): Promise<any | null> {
    try {
      const files = await this.listFiles();
      return files.find(f => f.CID === cid) || null;
    } catch (error) {
      console.error('❌ Failed to get file details:', error);
      return null;
    }
  }

  // Check if service is properly configured
  isConfigured(): boolean {
    return this.configured;
  }

  // Get storage statistics
  async getStorageStats(): Promise<any> {
    if (!this.configured) {
      console.warn('⚠️ Apillon service not configured');
      return null;
    }

    try {
      const response = await fetch(`${this.baseUrl}/storage/buckets/${this.bucketUuid}`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to get storage stats: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return null;
    }
  }
}

// Export singleton instance
export const apillonStorage = new ApillonStorageService();
