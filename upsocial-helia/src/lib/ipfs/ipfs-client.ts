// IPFS Client - Browser-compatible implementation
// This uses IPFS HTTP API instead of Helia for better browser compatibility

export interface IPFSUploadResult {
  cid: string;
  size: number;
  url: string;
}

export interface IPFSClientConfig {
  gatewayUrl?: string;
  apiUrl?: string;
  timeout?: number;
}

export class IPFSClient {
  private config: Required<IPFSClientConfig>;
  private static instance: IPFSClient | null = null;

  constructor(config: IPFSClientConfig = {}) {
    this.config = {
      gatewayUrl: config.gatewayUrl || 'https://ipfs.io',
      apiUrl: config.apiUrl || 'https://api.pinata.cloud',
      timeout: config.timeout || 30000,
    };
  }

  static getInstance(config?: IPFSClientConfig): IPFSClient {
    if (!IPFSClient.instance) {
      IPFSClient.instance = new IPFSClient(config);
    }
    return IPFSClient.instance;
  }

  /**
   * Upload a file to IPFS using Pinata service
   */
  async uploadFile(file: File): Promise<IPFSUploadResult> {
    try {
      console.log('Starting IPFS upload via Pinata...');
      
      // For now, simulate upload and return mock data
      // In production, this would use Pinata API with proper API keys
      const mockCid = this.generateMockCID();
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const result: IPFSUploadResult = {
        cid: mockCid,
        size: file.size,
        url: `${this.config.gatewayUrl}/ipfs/${mockCid}`
      };

      console.log('IPFS upload completed:', result);
      return result;
    } catch (error) {
      console.error('IPFS upload failed:', error);
      throw new Error(`IPFS upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload file using Web3.Storage as alternative
   */
  async uploadViaWeb3Storage(file: File): Promise<IPFSUploadResult> {
    try {
      console.log('Attempting upload via Web3.Storage...');
      
      // Mock implementation for now
      const mockCid = this.generateMockCID();
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      return {
        cid: mockCid,
        size: file.size,
        url: `https://w3s.link/ipfs/${mockCid}`
      };
    } catch (error) {
      console.error('Web3.Storage upload failed:', error);
      throw new Error(`Web3.Storage upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get content from IPFS using gateway
   */
  async getContent(cid: string): Promise<Response> {
    const url = `${this.config.gatewayUrl}/ipfs/${cid}`;
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
        },
        signal: AbortSignal.timeout(this.config.timeout)
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch content: ${response.status} ${response.statusText}`);
      }

      return response;
    } catch (error) {
      console.error('Failed to get IPFS content:', error);
      throw new Error(`Failed to get IPFS content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a CID is valid
   */
  isValidCID(cid: string): boolean {
    try {
      // Basic CID validation - starts with Qm (v0) or b (v1) or f (v1)
      return /^(Qm[1-9A-HJ-NP-Za-km-z]{44}|b[A-Za-z2-7]{58}|f[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})/.test(cid);
    } catch {
      return false;
    }
  }

  /**
   * Get gateway URL for a CID
   */
  getGatewayUrl(cid: string, gateway?: string): string {
    const baseUrl = gateway || this.config.gatewayUrl;
    return `${baseUrl}/ipfs/${cid}`;
  }

  /**
   * Generate a mock CID for testing purposes
   */
  private generateMockCID(): string {
    const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstvwxyz123456789';
    let result = 'Qm';
    for (let i = 0; i < 44; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Test connectivity to IPFS gateways
   */
  async testConnectivity(): Promise<{ gateway: boolean; api: boolean }> {
    const results = { gateway: false, api: false };

    try {
      // Test gateway with a known hash
      const testResponse = await fetch(`${this.config.gatewayUrl}/ipfs/QmYwAPJzv5CZsnA625s3Xf2nemtYgPpHdWEz79ojWnPbdG/readme`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      results.gateway = testResponse.ok;
    } catch {
      results.gateway = false;
    }

    // API test would go here in production
    results.api = true; // Mock as working

    return results;
  }
}

// Export singleton instance
export const ipfsClient = IPFSClient.getInstance();
