import { heliaClient, HeliaUpload<PERSON><PERSON>ult } from './helia-client'
import { apillonStorage, ApillonUploadResult } from '../storage/apillon-service'

export interface VideoMetadata {
  title: string
  description: string
  category: string
  duration: number
  size: number
  createdAt: string
  videoCID: string
  thumbnailCID: string
  creator: string
  tags?: string[]
  mimeType?: string
  // Legacy fields for compatibility
  cid?: string
  uploadedAt?: string
  uploadedBy?: string
  thumbnail?: string
  // Enhanced fields for Apillon integration
  apillonPinned?: boolean
  apillonUrl?: string
  gatewayUrls?: string[]
}

export interface VideoUploadProgress {
  loaded: number
  total: number
  percentage: number
  stage: 'preparing' | 'uploading' | 'processing' | 'complete' | 'error'
  message?: string
}

export class VideoStorageService {
  private isInitialized = false
  private static instance: VideoStorageService | null = null

  private constructor() {}

  static getInstance(): VideoStorageService {
    if (!VideoStorageService.instance) {
      VideoStorageService.instance = new VideoStorageService()
    }
    return VideoStorageService.instance
  }

  async initialize() {
    if (this.isInitialized) return

    try {
      console.log('Initializing VideoStorageService with Helia...')
      await heliaClient.initialize()
      this.isInitialized = true
      console.log('VideoStorageService initialized successfully')
    } catch (error) {
      console.error('Failed to initialize VideoStorageService:', error)
      throw new Error('Failed to initialize video storage')
    }
  }

  async uploadVideo(file: File, onProgress?: (progress: VideoUploadProgress) => void): Promise<HeliaUploadResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      onProgress?.({
        loaded: 0,
        total: file.size,
        percentage: 0,
        stage: 'preparing',
        message: 'Preparing video for upload...'
      })

      console.log('Uploading video to IPFS:', file.name)
      
      onProgress?.({
        loaded: 0,
        total: file.size,
        percentage: 10,
        stage: 'uploading',
        message: 'Uploading to IPFS network...'
      })

      // Upload to IPFS using Helia
      const ipfsResult = await heliaClient.uploadFile(file)
      
      onProgress?.({
        loaded: file.size * 0.7,
        total: file.size,
        percentage: 70,
        stage: 'uploading',
        message: 'IPFS upload complete, uploading to Apillon for faster access...'
      })

      // Parallel upload to Apillon for better availability
      let apillonResult: ApillonUploadResult | null = null
      try {
        if (apillonStorage.isConfigured()) {
          console.log('🚀 Starting parallel upload to Apillon...')
          apillonResult = await apillonStorage.uploadFile(file, `video_${Date.now()}_${file.name}`)
          
          if (apillonResult.success) {
            console.log('✅ Successfully uploaded to Apillon:', apillonResult.cid)
          } else {
            console.warn('⚠️ Apillon upload failed:', apillonResult.error)
          }
        } else {
          console.log('ℹ️ Apillon not configured, skipping Apillon upload')
        }
      } catch (apillonError) {
        console.warn('⚠️ Apillon upload failed:', apillonError)
      }
      
      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 90,
        stage: 'processing',
        message: apillonResult?.success ? 'Uploaded to both IPFS and Apillon!' : 'Processing complete...'
      })

      // Enhance result with Apillon data
      const enhancedResult: HeliaUploadResult = {
        ...ipfsResult,
        apillonPinned: apillonResult?.success || false,
        apillonUrl: apillonResult?.url || undefined
      }

      // Verify content availability after upload
      setTimeout(async () => {
        try {
          const availability = await heliaClient.verifyContentAvailability(enhancedResult.cid)
          console.log('📊 Video availability:', availability)
          if (availability.networkRetrievable) {
            console.log('✅ Video available via public gateways')
          } else {
            console.warn('⚠️ Video not yet available via public gateways - may need more time to propagate')
          }
        } catch (verifyError) {
          console.warn('⚠️ Could not verify content availability:', verifyError)
        }
      }, 2000)
      
      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 100,
        stage: 'complete',
        message: enhancedResult.apillonPinned ? 'Upload completed with Apillon pinning!' : 'Upload completed successfully!'
      })

      return enhancedResult
    } catch (error) {
      onProgress?.({
        loaded: 0,
        total: file.size,
        percentage: 0,
        stage: 'error',
        message: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  async uploadVideoWithMetadata(
    file: File, 
    metadata: Omit<VideoMetadata, 'cid' | 'size' | 'uploadedAt'>,
    onProgress?: (progress: VideoUploadProgress) => void
  ): Promise<VideoMetadata> {
    try {
      // Upload the video file
      const videoResult = await this.uploadVideo(file, onProgress)
      
      // Prepare gateway URLs for enhanced availability
      const gatewayUrls = [
        videoResult.url,
        heliaClient.getGatewayUrl(videoResult.cid, 'https://cf-ipfs.com'),
        heliaClient.getGatewayUrl(videoResult.cid, 'https://dweb.link'),
        heliaClient.getGatewayUrl(videoResult.cid, 'https://ipfs.apillon.io')
      ];

      if (videoResult.apillonUrl) {
        gatewayUrls.unshift(videoResult.apillonUrl); // Put Apillon URL first for fastest access
      }
      
      // Create complete metadata with enhanced fields
      const completeMetadata: VideoMetadata = {
        ...metadata,
        cid: videoResult.cid,
        videoCID: videoResult.cid,
        size: file.size,
        uploadedAt: new Date().toISOString(),
        apillonPinned: videoResult.apillonPinned,
        apillonUrl: videoResult.apillonUrl,
        gatewayUrls: [...new Set(gatewayUrls)] // Remove duplicates
      }

      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 90,
        stage: 'processing',
        message: 'Saving metadata...'
      })

      // Upload metadata to IPFS
      const metadataResult = await heliaClient.uploadJSON(completeMetadata)
      console.log('Metadata uploaded with CID:', metadataResult.cid)

      // Verify availability after a short delay
      setTimeout(async () => {
        try {
          const availability = await heliaClient.verifyContentAvailability(videoResult.cid)
          console.log('📊 Content availability:', availability)
          if (!availability.networkRetrievable) {
            console.warn('⚠️ Content not yet available via public gateways - may need more time to propagate')
          }
        } catch (verifyError) {
          console.warn('⚠️ Could not verify content availability:', verifyError)
        }
      }, 2000)

      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 100,
        stage: 'complete',
        message: videoResult.apillonPinned ? 
          'Video uploaded and pinned to Apillon for instant access!' : 
          'Video uploaded and announced to network!'
      })

      return completeMetadata
    } catch (error) {
      console.error('Failed to upload video with metadata:', error)
      throw error
    }
  }

  async uploadMetadata(metadata: VideoMetadata): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      console.log('Uploading metadata to IPFS...')
      const ipfsResult = await heliaClient.uploadJSON(metadata)
      console.log('Metadata uploaded to IPFS with CID:', ipfsResult.cid)
      
      // Also upload metadata to Apillon for better availability
      try {
        if (apillonStorage.isConfigured()) {
          const apillonResult = await apillonStorage.uploadJSON(metadata, `metadata_${Date.now()}.json`)
          if (apillonResult.success) {
            console.log('✅ Metadata also uploaded to Apillon:', apillonResult.cid)
          }
        }
      } catch (apillonError) {
        console.warn('⚠️ Apillon metadata upload failed:', apillonError)
      }
      
      return ipfsResult.cid
    } catch (error) {
      console.error('Failed to upload metadata:', error)
      throw error
    }
  }

  async getVideo(cid: string): Promise<Uint8Array> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      console.log('Retrieving video from IPFS:', cid)
      return await heliaClient.getFile(cid)
    } catch (error) {
      console.error('Failed to retrieve video:', error)
      throw new Error('Failed to retrieve video from IPFS')
    }
  }

  async getVideoMetadata(cid: string): Promise<VideoMetadata> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      console.log('Retrieving video metadata from IPFS:', cid)
      return await heliaClient.getJSON(cid)
    } catch (error) {
      console.error('Failed to retrieve video metadata:', error)
      throw new Error('Failed to retrieve video metadata from IPFS')
    }
  }

  async generateThumbnail(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        reject(new Error('Canvas context not available'))
        return
      }

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        
        video.currentTime = Math.min(video.duration * 0.1, 5) // 10% through or 5 seconds
      })

      video.addEventListener('seeked', () => {
        ctx.drawImage(video, 0, 0)
        const thumbnail = canvas.toDataURL('image/jpeg', 0.8)
        resolve(thumbnail)
      })

      video.addEventListener('error', () => {
        reject(new Error('Failed to generate thumbnail'))
      })

      video.src = URL.createObjectURL(file)
      video.load()
    })
  }

  async uploadThumbnail(thumbnailDataUrl: string): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      // Convert data URL to blob
      const response = await fetch(thumbnailDataUrl)
      const blob = await response.blob()
      
      // Create a File object from the blob
      const thumbnailFile = new File([blob], 'thumbnail.jpg', { type: 'image/jpeg' })
      
      // Upload to IPFS
      const ipfsResult = await heliaClient.uploadFile(thumbnailFile)
      console.log('Thumbnail uploaded to IPFS with CID:', ipfsResult.cid)
      
      // Also upload to Apillon for better availability
      try {
        if (apillonStorage.isConfigured()) {
          const apillonResult = await apillonStorage.uploadFile(thumbnailFile, `thumbnail_${Date.now()}.jpg`)
          if (apillonResult.success) {
            console.log('✅ Thumbnail also uploaded to Apillon:', apillonResult.cid)
          }
        }
      } catch (apillonError) {
        console.warn('⚠️ Apillon thumbnail upload failed:', apillonError)
      }
      
      return ipfsResult.cid
    } catch (error) {
      console.error('Failed to upload thumbnail:', error)
      throw error
    }
  }

  async getVideoDuration(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      
      video.addEventListener('loadedmetadata', () => {
        resolve(video.duration)
      })

      video.addEventListener('error', () => {
        reject(new Error('Failed to get video duration'))
      })

      video.src = URL.createObjectURL(file)
      video.load()
    })
  }

  async isValidCID(cidString: string): Promise<boolean> {
    return await heliaClient.isValidCID(cidString)
  }

  getGatewayUrl(cid: string, gateway?: string): string {
    return heliaClient.getGatewayUrl(cid, gateway)
  }

  async getNodeStats() {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      return await heliaClient.getNodeStats()
    } catch (error) {
      console.error('Failed to get node stats:', error)
      throw error
    }
  }

  async stop(): Promise<void> {
    if (this.isInitialized) {
      await heliaClient.stop()
      this.isInitialized = false
    }
  }

  // Helper method to create a blob URL for video playback
  createVideoURL(videoData: Uint8Array, mimeType: string = 'video/mp4'): string {
    const blob = new Blob([new Uint8Array(videoData)], { type: mimeType })
    return URL.createObjectURL(blob)
  }

  // Helper method to create blob URL directly from CID
  async createBlobUrl(cid: string, mimeType: string = 'video/mp4'): Promise<string> {
    try {
      console.log('Creating blob URL for CID:', cid)
      const videoData = await this.getVideo(cid)
      const blob = new Blob([new Uint8Array(videoData)], { type: mimeType })
      const url = URL.createObjectURL(blob)
      console.log('Blob URL created successfully')
      return url
    } catch (error) {
      console.error('Failed to create blob URL:', error)
      throw new Error('Failed to create video URL from IPFS')
    }
  }

  // Helper method to revoke blob URL
  revokeVideoURL(url: string): void {
    URL.revokeObjectURL(url)
  }

  // Helper method to get file info
  getFileInfo(file: File) {
    return {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString(),
      sizeFormatted: this.formatFileSize(file.size)
    }
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// Export singleton instance
export const videoStorage = VideoStorageService.getInstance()
