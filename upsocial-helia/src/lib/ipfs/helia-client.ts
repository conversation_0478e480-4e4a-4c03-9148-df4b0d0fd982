// Helia IPFS Client - Full decentralized implementation with Apillon pinning
import { create<PERSON><PERSON><PERSON>, Helia } from 'helia'
import { unixfs, UnixFS } from '@helia/unixfs'
import { ipns, IPNS } from '@helia/ipns'
import { generateKeyPair } from '@libp2p/crypto/keys'
import { createLibp2p } from 'libp2p'
import { noise } from '@libp2p/noise'
import { mplex } from '@libp2p/mplex'
import { webSockets } from '@libp2p/websockets'
import { webRTC, webRTCDirect } from '@libp2p/webrtc'
import { bootstrap } from '@libp2p/bootstrap'
import { kadDHT } from '@libp2p/kad-dht'
import { mdns } from '@libp2p/mdns'
import { identify } from '@libp2p/identify'
import { ping } from '@libp2p/ping'
import { dcutr } from '@libp2p/dcutr'
import { circuitRelayTransport } from '@libp2p/circuit-relay-v2'
import { CID } from 'multiformats/cid'
import type { PrivateKey } from '@libp2p/interface'

export interface HeliaUploadResult {
  cid: string;
  size: number;
  url: string;
  apillonPinned?: boolean;
  apillonUrl?: string;
}

export interface HeliaClientConfig {
  enableWebRTC?: boolean;
  enableMDNS?: boolean;
  bootstrapPeers?: string[];
  gatewayUrl?: string;
  enableApillonPinning?: boolean;
}

export interface ApillonPinResult {
  success: boolean;
  cid?: string;
  uuid?: string;
  url?: string;
  error?: string;
}

export class HeliaClient {
  private helia: Helia | null = null
  private fs: UnixFS | null = null
  private ipnsService: IPNS | null = null
  private ipnsPrivateKey: PrivateKey | null = null
  private isInitialized = false
  private initPromise: Promise<void> | null = null;
  private config: HeliaClientConfig;
  private static instance: HeliaClient | null = null;

  constructor(config: HeliaClientConfig = {}) {
    this.config = {
      enableWebRTC: config.enableWebRTC ?? false,
      enableMDNS: config.enableMDNS ?? true,
      bootstrapPeers: config.bootstrapPeers || [
        // Official IPFS bootstrap nodes
        '/dnsaddr/bootstrap.libp2p.io/p2p/QmNnooDu7bfjPFoTZYxMNLWUQJyrVwtbZg5gBMjTezGAJN',
        '/dnsaddr/bootstrap.libp2p.io/p2p/QmQCU2EcMqAqQPR2i9bChDtGNJchTbq5TbXJJ16u19uLTa',
        '/dnsaddr/bootstrap.libp2p.io/p2p/QmbLHAnMoJPWSCR5Zp9T2XeZb3fKtk7pZCv5jW3qUgPSzr',
        '/dnsaddr/bootstrap.libp2p.io/p2p/QmcZf59bWwK5XFi76CZX8cbJ4BhTzzA3gU1ZjYZcYW3dwt',
        // Preload nodes for better content discovery
        '/dns4/node0.preload.ipfs.io/tcp/443/wss/p2p/QmZMxNdpMkewiVZLMRxaNxUeZpDUb34pWjZ1kZvsd16Zic',
        '/dns4/node1.preload.ipfs.io/tcp/443/wss/p2p/Qmbut9Ywz9YEDrz8ySBSgWyJk41Uvm2QJPhwDJzJyGFsD6',
        '/dns4/node2.preload.ipfs.io/tcp/443/wss/p2p/QmV7gnbW5VTcJ3oyM2Xk1rdFBJ3kTkvxc87UFGsun29STS',
        '/dns4/node3.preload.ipfs.io/tcp/443/wss/p2p/QmY7JB6MQXhxHvq7dBDh4HpbH29v4yE9JRadAVpndvzySN',
        // Additional DHT bootstrap nodes
        '/ip4/**************/tcp/4001/p2p/QmaCpDMGvV2BGHeYERUEnRQAwe3N8SzbUtfsmvsqQLuvuJ',
        '/ip4/***************/tcp/4001/p2p/QmSoLPppuBtQSGwKDZT2M73ULpjvfd3aZ6ha4oFGL1KrGM'
      ],
      gatewayUrl: config.gatewayUrl || 'https://ipfs.io',
      enableApillonPinning: config.enableApillonPinning ?? false,
    };
  }

  static getInstance(config?: HeliaClientConfig): HeliaClient {
    if (!HeliaClient.instance) {
      HeliaClient.instance = new HeliaClient(config);
    }
    return HeliaClient.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    if (this.initPromise) return this.initPromise;

    this.initPromise = this._initialize();
    await this.initPromise;
  }

  private async _initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing Helia IPFS node...');

      // Create libp2p configuration for browser
      const libp2pConfig = {
        addresses: {
          listen: ['/p2p-circuit']
        },
        transports: [
          webSockets(),
          circuitRelayTransport(),
          ...(this.config.enableWebRTC ? [webRTC(), webRTCDirect()] : [])
        ],
        connectionEncryption: [noise()],
        streamMuxers: [mplex()],
        services: {
          identify: identify(),
          ping: ping(),
          dht: kadDHT({
            clientMode: false, // Enable server mode for better providing
            validators: {},
            selectors: {},
            kBucketSize: 20
          }),
          dcutr: dcutr()
        },
        peerDiscovery: [
          bootstrap({
            list: this.config.bootstrapPeers!
          })
        ],
        connectionManager: {
          maxConnections: 150,
          minConnections: 10,
          autoDialInterval: 10000,
          maxParallelDials: 5,
          maxDialsPerPeer: 2
        }
      };

      // Create Helia instance
      this.helia = await createHelia({
        libp2p: await createLibp2p(libp2pConfig),
        start: true
      });

      // Create UnixFS interface
      this.fs = unixfs(this.helia);
      
      // Create IPNS interface
      this.ipnsService = ipns(this.helia);
      
      // Generate IPNS keypair for publishing
      this.ipnsPrivateKey = await generateKeyPair('Ed25519')
      
      this.isInitialized = true;
      
      console.log('✅ Helia IPFS node initialized successfully');
      console.log('📡 Peer ID:', this.helia.libp2p.peerId.toString());
      
      // Set up event listeners
      this.setupEventListeners();
      
    } catch (error) {
      console.error('❌ Failed to initialize Helia:', error);
      this.isInitialized = false;
      throw new Error(`Failed to initialize IPFS: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private setupEventListeners(): void {
    if (!this.helia) return;

    this.helia.libp2p.addEventListener('peer:connect', (event) => {
      console.log('🤝 Connected to peer:', event.detail.toString());
    });

    this.helia.libp2p.addEventListener('peer:disconnect', (event) => {
      console.log('👋 Disconnected from peer:', event.detail.toString());
    });
  }

  async uploadFile(file: File): Promise<HeliaUploadResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.fs) {
      throw new Error('UnixFS not initialized');
    }

    try {
      console.log('📤 Starting file upload to IPFS...');
      console.log('📁 File:', file.name, 'Size:', file.size, 'bytes');

      // Convert file to Uint8Array
      const arrayBuffer = await file.arrayBuffer();
      const content = new Uint8Array(arrayBuffer);

      // Add file to IPFS
      const cid = await this.fs.addFile({
        content,
        path: file.name
      });

      const result: HeliaUploadResult = {
        cid: cid.toString(),
        size: file.size,
        url: `${this.config.gatewayUrl}/ipfs/${cid.toString()}`
      };

      // Announce to network for better availability
      await this.announceToNetwork(cid.toString());

      // Actively trigger gateway caching for faster access
      console.log(`🚀 Triggering gateway caching for faster access...`);
      await this.triggerGatewayCaching(cid.toString());

      // Pin to Apillon if enabled
      if (this.config.enableApillonPinning) {
        try {
          const apillonResult = await this.pinToApillonViaAPI(cid.toString(), file);
          if (apillonResult.success) {
            result.apillonPinned = true;
            result.apillonUrl = apillonResult.url;
            console.log('✅ File pinned to Apillon:', apillonResult.url);
          }
        } catch (apillonError) {
          console.warn('⚠️ Apillon pinning failed:', apillonError);
        }
      }

      console.log('✅ File uploaded successfully:', result);
      return result;

    } catch (error) {
      console.error('❌ Upload failed:', error);
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async uploadJSON(data: any): Promise<HeliaUploadResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.fs) {
      throw new Error('UnixFS not initialized');
    }

    try {
      console.log('📤 Uploading JSON data to IPFS...');

      const jsonString = JSON.stringify(data, null, 2);
      const content = new TextEncoder().encode(jsonString);

      const cid = await this.fs.addBytes(content);

      const result: HeliaUploadResult = {
        cid: cid.toString(),
        size: content.length,
        url: `${this.config.gatewayUrl}/ipfs/${cid.toString()}`
      };

      // Announce to network for better availability
      await this.announceToNetwork(cid.toString());

      // Actively trigger gateway caching for faster access
      console.log(`🚀 Triggering gateway caching for JSON content...`);
      await this.triggerGatewayCaching(cid.toString());

      // Pin to Apillon if enabled
      if (this.config.enableApillonPinning) {
        try {
          // Create a temporary blob for JSON data
          const jsonBlob = new Blob([jsonString], { type: 'application/json' });
          const jsonFile = new File([jsonBlob], 'data.json', { type: 'application/json' });
          
          const apillonResult = await this.pinToApillonViaAPI(cid.toString(), jsonFile);
          if (apillonResult.success) {
            result.apillonPinned = true;
            result.apillonUrl = apillonResult.url;
            console.log('✅ JSON pinned to Apillon:', apillonResult.url);
          }
        } catch (apillonError) {
          console.warn('⚠️ Apillon pinning failed:', apillonError);
        }
      }

      console.log('✅ JSON uploaded successfully:', result);
      return result;

    } catch (error) {
      console.error('❌ JSON upload failed:', error);
      throw new Error(`JSON upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Pin content to Apillon via server-side API for guaranteed gateway availability
  private async pinToApillonViaAPI(cid: string, file: File): Promise<ApillonPinResult> {
    try {
      console.log(`📌 Attempting server-side Apillon pinning for CID ${cid}...`);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('cid', cid);

      const response = await fetch('/api/apillon/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        console.log(`✅ Server-side Apillon pinning successful: ${result.url}`);
        return result;
      } else {
        throw new Error(result.error || 'Server-side upload failed');
      }

    } catch (error) {
      console.error('❌ Server-side Apillon pinning failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async getFile(cid: string): Promise<Uint8Array> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.fs) {
      throw new Error('UnixFS not initialized');
    }

    try {
      console.log('📥 Retrieving file from IPFS:', cid);

      const cidObj = CID.parse(cid);
      
      // Try to get as bytes first (for direct content)
      try {
        const result = await this.fs.cat(cidObj);
        const chunks: Uint8Array[] = [];
        
        for await (const chunk of result) {
          chunks.push(chunk);
        }

        // Combine chunks
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
        const combined = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of chunks) {
          combined.set(chunk, offset);
          offset += chunk.length;
        }

        console.log('✅ File retrieved successfully, size:', combined.length);
        return combined;
        
      } catch (error: any) {
        // If it's not a file, it might be raw bytes
        if (error.code === 'ERR_NOT_A_FILE') {
          console.log('📥 Trying to retrieve as raw bytes...');
          const result = await this.fs.cat(cidObj);
          const chunks: Uint8Array[] = [];
          
          for await (const chunk of result) {
            chunks.push(chunk);
          }

          const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
          const combined = new Uint8Array(totalLength);
          let offset = 0;

          for (const chunk of chunks) {
            combined.set(chunk, offset);
            offset += chunk.length;
          }

          console.log('✅ Raw bytes retrieved successfully, size:', combined.length);
          return combined;
        }
        throw error;
      }

    } catch (error) {
      console.error('❌ Failed to retrieve file:', error);
      throw new Error(`Failed to retrieve file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getJSON(cid: string): Promise<any> {
    const data = await this.getFile(cid);
    const jsonString = new TextDecoder().decode(data);
    return JSON.parse(jsonString);
  }

  async isValidCID(cidString: string): Promise<boolean> {
    try {
      CID.parse(cidString);
      return true;
    } catch {
      return false;
    }
  }

  getGatewayUrl(cid: string, gateway?: string): string {
    const baseUrl = gateway || this.config.gatewayUrl;
    return `${baseUrl}/ipfs/${cid}`;
  }

  async getNodeStats() {
    if (!this.helia) {
      throw new Error('Helia not initialized');
    }

    const connections = this.helia.libp2p.getConnections();
    const peers = this.helia.libp2p.getPeers();

    // Get more detailed connection info
    const connectedPeers = connections.map(conn => ({
      peerId: conn.remotePeer.toString(),
      status: conn.status,
      direction: conn.direction,
      protocols: conn.streams.map(s => s.protocol)
    }));

    return {
      peerId: this.helia.libp2p.peerId.toString(),
      connections: connections.length,
      peers: peers.length,
      isStarted: this.helia.libp2p.status === 'started',
      connectedPeers: connectedPeers.slice(0, 5), // Show first 5 for brevity
      ipnsKey: this.ipnsPrivateKey?.publicKey.toString()
    };
  }

  // Verify if content is retrievable from the network
  async verifyContentAvailability(cid: string): Promise<{
    localAvailable: boolean;
    networkRetrievable: boolean;
    gateways: { url: string; accessible: boolean; responseTime?: number }[];
  }> {
    const parsedCID = CID.parse(cid);
    
    // Check local availability
    const localAvailable = await this.helia!.blockstore.has(parsedCID);
    
    // Test gateway accessibility with expanded list of potentially working gateways
    const testGateways = [
      'https://ipfs.io/ipfs/',              // Primary gateway
      'https://dweb.link/ipfs/',            // Protocol Labs gateway
      'https://cloudflare-ipfs.com/ipfs/',  // Cloudflare gateway (very reliable)
      'https://gateway.pinata.cloud/ipfs/', // Pinata gateway (reliable)
      'https://ipfs.eth.aragon.network/ipfs/', // Aragon gateway
      'https://gateway.ipfs.io/ipfs/',      // Alternative IPFS.io endpoint
      'https://storry.tv/ipfs/',            // Keep as fallback despite issues
    ];
    
    console.log(`🔍 Testing content availability across ${testGateways.length} gateways...`);
    
    const gatewayResults = await Promise.allSettled(
      testGateways.map(async (gateway) => {
        const startTime = Date.now();
        try {
          const response = await fetch(gateway + cid, {
            method: 'HEAD',
            signal: AbortSignal.timeout(10000)
          });
          const responseTime = Date.now() - startTime;
          const accessible = response.ok;
          
          if (accessible) {
            console.log(`✅ Gateway ${new URL(gateway).hostname} responded in ${responseTime}ms`);
          } else {
            console.log(`❌ Gateway ${new URL(gateway).hostname} returned ${response.status}`);
          }
          
          return { url: gateway, accessible, responseTime };
        } catch (error) {
          const responseTime = Date.now() - startTime;
          console.log(`❌ Gateway ${new URL(gateway).hostname} failed: ${error}`);
          return { url: gateway, accessible: false, responseTime };
        }
      })
    );
    
    const gateways = gatewayResults.map(result => 
      result.status === 'fulfilled' ? result.value : { url: '', accessible: false }
    );
    
    const networkRetrievable = gateways.some(g => g.accessible);
    
    console.log(`📊 Verification complete: ${gateways.filter(g => g.accessible).length}/${gateways.length} gateways accessible`);
    
    return {
      localAvailable,
      networkRetrievable,
      gateways
    };
  }

  // IPNS Methods for network announcement
  async publishToIPNS(cid: string, options?: { ttl?: number; lifetime?: number }): Promise<string> {
    if (!this.ipnsService || !this.helia || !this.ipnsPrivateKey) {
      throw new Error('IPNS service not initialized');
    }

    try {
      console.log(`📢 Publishing CID ${cid} to IPNS...`);
      
      const ipnsName = await this.ipnsService.publish(
        this.ipnsPrivateKey,
        CID.parse(cid),
        {
          ttl: options?.ttl || 60 * 60 * 1000, // 1 hour
          lifetime: options?.lifetime || 24 * 60 * 60 * 1000 // 24 hours
        }
      );

      const ipnsKey = `/ipns/${this.ipnsPrivateKey.publicKey.toString()}`;
      console.log(`✅ Published to IPNS: ${ipnsKey}`);
      
      return ipnsKey;
    } catch (error) {
      console.error('❌ Failed to publish to IPNS:', error);
      throw error;
    }
  }

  async resolveIPNS(ipnsName: string): Promise<string> {
    if (!this.ipnsService || !this.ipnsPrivateKey) {
      throw new Error('IPNS service not initialized');
    }

    try {
      console.log(`🔍 Resolving IPNS name: ${ipnsName}`);
      
      const resolved = await this.ipnsService.resolve(this.ipnsPrivateKey.publicKey);
      const cid = resolved.toString();
      
      console.log(`✅ IPNS resolved to CID: ${cid}`);
      return cid;
    } catch (error) {
      console.error('❌ Failed to resolve IPNS:', error);
      throw error;
    }
  }

  async announceToNetwork(cid: string): Promise<void> {
    if (!this.helia) {
      throw new Error('Helia not initialized');
    }

    try {
      console.log(`📡 Announcing CID ${cid} to IPFS network...`);
      
      const parsedCID = CID.parse(cid);
      
      // 1. Verify content exists in local blockstore
      const hasContent = await this.helia.blockstore.has(parsedCID);
      if (!hasContent) {
        throw new Error(`Content with CID ${cid} not found in local blockstore`);
      }
      console.log(`✅ Content verified in local blockstore`);
      
      // 2. Pin the content locally to ensure it stays available
      await this.helia.pins.add(parsedCID);
      console.log(`📌 Content pinned locally`);
      
      // 3. Announce to DHT if available
      try {
        const dht = this.helia.libp2p.services.dht as any;
        if (dht && typeof dht.provide === 'function') {
          await dht.provide(parsedCID);
          console.log(`📢 CID announced to DHT`);
        } else {
          console.log(`ℹ️ DHT provider not available, content will be announced on first request`);
        }
      } catch (dhtError) {
        console.warn(`⚠️ DHT announcement failed:`, dhtError);
      }
      
      // 4. Attempt to pin to public gateways for better availability
      await this.pinToPublicGateways(cid);
      
      // 5. Trigger actual content requests to make it available via BitSwap
      await this.triggerNetworkRequests(cid);
      
      console.log(`✅ Network announcement completed for CID ${cid}`);
    } catch (error) {
      console.error('❌ Failed to announce to network:', error);
      throw error;
    }
  }

  // Actively trigger gateway caching by making requests to content
  async triggerGatewayCaching(cid: string): Promise<void> {
    const reliableGateways = [
      'https://cloudflare-ipfs.com/ipfs/',  // Most reliable
      'https://gateway.pinata.cloud/ipfs/', // Very reliable
      'https://ipfs.io/ipfs/',
      'https://dweb.link/ipfs/',
      'https://gateway.ipfs.io/ipfs/',
      'https://ipfs.eth.aragon.network/ipfs/',
      'https://storry.tv/ipfs/',            // Keep as fallback
    ];

    console.log(`🌐 Attempting to access content via public gateways to trigger caching...`);
    
    // Make actual GET requests (not just HEAD) to trigger gateway caching
    const requests = reliableGateways.map(async (gateway) => {
      try {
        const response = await fetch(gateway + cid, {
          method: 'GET',
          signal: AbortSignal.timeout(8000),
          headers: {
            'Range': 'bytes=0-1023' // Just fetch first 1KB to trigger caching
          }
        });
        
        if (response.ok) {
          console.log(`✅ Gateway ${new URL(gateway).hostname} cached content successfully`);
          return { gateway, success: true };
        } else {
          console.log(`⚠️ Gateway ${new URL(gateway).hostname} returned ${response.status}`);
          return { gateway, success: false };
        }
      } catch (error) {
        console.log(`⚠️ Gateway ${new URL(gateway).hostname} not responsive: ${error}`);
        return { gateway, success: false };
      }
    });

    const results = await Promise.allSettled(requests);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    
    console.log(`📊 Gateway caching: ${successful}/${reliableGateways.length} gateways cached content`);
  }

  // Trigger network requests to make content available via BitSwap
  private async triggerNetworkRequests(cid: string): Promise<void> {
    console.log(`🔄 Triggering network requests to share content...`);
    
    try {
      // Try to retrieve our own content through the UnixFS interface
      // This ensures the content is available via BitSwap
      if (this.fs) {
        const data = await this.fs.cat(CID.parse(cid), { signal: AbortSignal.timeout(5000) });
        let chunks = 0;
        for await (const chunk of data) {
          chunks++;
          if (chunks > 10) break; // Just verify the first few chunks
        }
        console.log(`✅ Content verified locally via UnixFS (${chunks} chunks)`);
      }
      
      // Make actual GET requests (not just HEAD) to trigger gateway caching
      const quickGateways = [
        'https://ipfs.io/ipfs/' + cid,
        'https://dweb.link/ipfs/' + cid,
        'https://storry.tv/ipfs/' + cid
      ];
      
      const requestPromises = quickGateways.map(async (url) => {
        try {
          const response = await fetch(url, {
            method: 'GET',
            signal: AbortSignal.timeout(3000),
            headers: { 'Range': 'bytes=0-1024' } // Just get first 1KB
          });
          if (response.ok) {
            console.log(`🚀 Gateway ${new URL(url).hostname} successfully fetched content`);
          }
        } catch (error) {
          // Silent fail for network triggering
        }
      });
      
      await Promise.allSettled(requestPromises);
    } catch (error) {
      console.warn('⚠️ Network triggering completed with some errors:', error);
    }
  }

  // Pin content to public IPFS gateways for better availability
  private async pinToPublicGateways(cid: string): Promise<void> {
    const pinningServices = [
      'https://cloudflare-ipfs.com/ipfs/' + cid,
      'https://gateway.pinata.cloud/ipfs/' + cid,
      'https://ipfs.io/ipfs/' + cid,
      'https://dweb.link/ipfs/' + cid,
      'https://gateway.ipfs.io/ipfs/' + cid,
      'https://ipfs.eth.aragon.network/ipfs/' + cid,
      'https://storry.tv/ipfs/' + cid
    ];

    console.log(`🌐 Attempting to access content via public gateways to trigger caching...`);
    
    // Make HEAD requests to popular gateways to trigger caching
    const pinPromises = pinningServices.map(async (url) => {
      try {
        const response = await fetch(url, { 
          method: 'HEAD',
          signal: AbortSignal.timeout(15000) // 15 second timeout
        });
        if (response.ok) {
          console.log(`✅ Content cached by gateway: ${new URL(url).hostname}`);
          return { url, success: true };
        } else {
          console.log(`⚠️ Gateway ${new URL(url).hostname} returned status: ${response.status}`);
          return { url, success: false };
        }
      } catch (error) {
        console.log(`⚠️ Gateway ${new URL(url).hostname} not responsive: ${error}`);
        return { url, success: false };
      }
    });

    // Wait for at least one gateway to respond positively
    try {
      const results = await Promise.allSettled(pinPromises);
      const successful = results
        .filter(r => r.status === 'fulfilled' && r.value.success)
        .length;
      
      if (successful > 0) {
        console.log(`✅ Content successfully cached by ${successful} gateway(s)`);
      } else {
        console.warn(`⚠️ No gateways successfully cached the content yet`);
      }
    } catch (error) {
      console.warn('⚠️ Gateway caching failed:', error);
    }
  }

  async stop(): Promise<void> {
    if (this.helia) {
      console.log('🛑 Stopping Helia node...');
      await this.helia.stop();
      this.helia = null;
      this.fs = null;
      this.ipnsService = null;
      this.ipnsPrivateKey = null;
      this.isInitialized = false;
      this.initPromise = null;
      console.log('✅ Helia node stopped');
    }
  }
}

// Export singleton instance
export const heliaClient = HeliaClient.getInstance();
