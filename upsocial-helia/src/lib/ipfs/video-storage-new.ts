import { heliaClient, HeliaUploadResult } from './helia-client'

export interface VideoMetadata {
  title: string
  description: string
  duration?: number
  thumbnail?: string
  tags: string[]
  category: string
  uploadedAt: string
  uploadedBy: string
  cid: string
  size: number
  mimeType: string
}

export interface VideoUploadProgress {
  loaded: number
  total: number
  percentage: number
  stage: 'preparing' | 'uploading' | 'processing' | 'complete' | 'error'
  message?: string
}

export class VideoStorageService {
  private isInitialized = false
  private static instance: VideoStorageService | null = null

  private constructor() {}

  static getInstance(): VideoStorageService {
    if (!VideoStorageService.instance) {
      VideoStorageService.instance = new VideoStorageService()
    }
    return VideoStorageService.instance
  }

  async initialize() {
    if (this.isInitialized) return

    try {
      console.log('Initializing VideoStorageService with Helia...')
      await heliaClient.initialize()
      this.isInitialized = true
      console.log('VideoStorageService initialized successfully')
    } catch (error) {
      console.error('Failed to initialize VideoStorageService:', error)
      throw new Error('Failed to initialize video storage')
    }
  }

  async uploadVideo(file: File, onProgress?: (progress: VideoUploadProgress) => void): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      onProgress?.({
        loaded: 0,
        total: file.size,
        percentage: 0,
        stage: 'preparing',
        message: 'Preparing video for upload...'
      })

      console.log('Uploading video to IPFS:', file.name)
      
      onProgress?.({
        loaded: 0,
        total: file.size,
        percentage: 10,
        stage: 'uploading',
        message: 'Uploading to IPFS network...'
      })

      const result = await heliaClient.uploadFile(file)
      
      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 100,
        stage: 'complete',
        message: 'Upload completed successfully!'
      })

      return result.cid
    } catch (error) {
      onProgress?.({
        loaded: 0,
        total: file.size,
        percentage: 0,
        stage: 'error',
        message: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
      throw error
    }
  }

  async uploadVideoWithMetadata(
    file: File, 
    metadata: Omit<VideoMetadata, 'cid' | 'size' | 'uploadedAt'>,
    onProgress?: (progress: VideoUploadProgress) => void
  ): Promise<VideoMetadata> {
    try {
      // Upload the video file
      const videoCid = await this.uploadVideo(file, onProgress)
      
      // Create complete metadata
      const completeMetadata: VideoMetadata = {
        ...metadata,
        cid: videoCid,
        size: file.size,
        uploadedAt: new Date().toISOString()
      }

      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 90,
        stage: 'processing',
        message: 'Saving metadata...'
      })

      // Upload metadata to IPFS
      const metadataResult = await heliaClient.uploadJSON(completeMetadata)
      console.log('Metadata uploaded with CID:', metadataResult.cid)

      onProgress?.({
        loaded: file.size,
        total: file.size,
        percentage: 100,
        stage: 'complete',
        message: 'Video and metadata uploaded successfully!'
      })

      return completeMetadata
    } catch (error) {
      console.error('Failed to upload video with metadata:', error)
      throw error
    }
  }

  async getVideo(cid: string): Promise<Uint8Array> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      console.log('Retrieving video from IPFS:', cid)
      return await heliaClient.getFile(cid)
    } catch (error) {
      console.error('Failed to retrieve video:', error)
      throw new Error('Failed to retrieve video from IPFS')
    }
  }

  async getVideoMetadata(cid: string): Promise<VideoMetadata> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      console.log('Retrieving video metadata from IPFS:', cid)
      return await heliaClient.getJSON(cid)
    } catch (error) {
      console.error('Failed to retrieve video metadata:', error)
      throw new Error('Failed to retrieve video metadata from IPFS')
    }
  }

  async generateThumbnail(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      if (!ctx) {
        reject(new Error('Canvas context not available'))
        return
      }

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        
        video.currentTime = Math.min(video.duration * 0.1, 5) // 10% through or 5 seconds
      })

      video.addEventListener('seeked', () => {
        ctx.drawImage(video, 0, 0)
        const thumbnail = canvas.toDataURL('image/jpeg', 0.8)
        resolve(thumbnail)
      })

      video.addEventListener('error', () => {
        reject(new Error('Failed to generate thumbnail'))
      })

      video.src = URL.createObjectURL(file)
      video.load()
    })
  }

  async isValidCID(cidString: string): Promise<boolean> {
    return await heliaClient.isValidCID(cidString)
  }

  getGatewayUrl(cid: string, gateway?: string): string {
    return heliaClient.getGatewayUrl(cid, gateway)
  }

  async getNodeStats() {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      return await heliaClient.getNodeStats()
    } catch (error) {
      console.error('Failed to get node stats:', error)
      throw error
    }
  }

  async stop(): Promise<void> {
    if (this.isInitialized) {
      await heliaClient.stop()
      this.isInitialized = false
    }
  }

  // Helper method to create a blob URL for video playback
  createVideoURL(videoData: Uint8Array, mimeType: string = 'video/mp4'): string {
    const blob = new Blob([new Uint8Array(videoData)], { type: mimeType })
    return URL.createObjectURL(blob)
  }

  // Helper method to revoke blob URL
  revokeVideoURL(url: string): void {
    URL.revokeObjectURL(url)
  }

  // Helper method to get file info
  getFileInfo(file: File) {
    return {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString(),
      sizeFormatted: this.formatFileSize(file.size)
    }
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// Export singleton instance
export const videoStorage = VideoStorageService.getInstance()
