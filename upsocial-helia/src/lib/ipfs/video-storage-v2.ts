// Video Storage Service - Browser-compatible version using IPFS HTTP API
// This replaces the Helia-based implementation for better browser support

import { ipfsClient, IPFSUploadResult } from './ipfs-client';

export interface VideoMetadata {
  title: string;
  description: string;
  duration: number;
  thumbnail?: string;
  category: string;
  tags: string[];
  createdAt: string;
  author: string;
}

export interface StoredVideo {
  id: string;
  cid: string;
  metadata: VideoMetadata;
  ipfsUrl: string;
  gatewayUrl: string;
  size: number;
  uploadedAt: string;
}

export class VideoStorageService {
  private static instance: VideoStorageService | null = null;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): VideoStorageService {
    if (!VideoStorageService.instance) {
      VideoStorageService.instance = new VideoStorageService();
    }
    return VideoStorageService.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('Initializing Video Storage Service...');
      
      // Test IPFS connectivity
      const connectivity = await ipfsClient.testConnectivity();
      console.log('IPFS Connectivity:', connectivity);

      if (!connectivity.gateway) {
        console.warn('IPFS gateway connectivity issues detected');
      }

      this.isInitialized = true;
      console.log('Video Storage Service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Video Storage Service:', error);
      throw new Error(`Video Storage Service initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async uploadVideo(file: File, metadata: VideoMetadata): Promise<StoredVideo> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('Starting video upload...', { fileName: file.name, size: file.size });

      // Validate file
      this.validateVideoFile(file);

      // Upload to IPFS
      const uploadResult = await this.uploadToIPFS(file);
      
      // Create stored video object
      const storedVideo: StoredVideo = {
        id: crypto.randomUUID(),
        cid: uploadResult.cid,
        metadata: {
          ...metadata,
          createdAt: new Date().toISOString()
        },
        ipfsUrl: uploadResult.url,
        gatewayUrl: ipfsClient.getGatewayUrl(uploadResult.cid),
        size: uploadResult.size,
        uploadedAt: new Date().toISOString()
      };

      // Store in localStorage for persistence
      this.saveVideoLocally(storedVideo);

      console.log('Video upload completed successfully:', storedVideo);
      return storedVideo;
    } catch (error) {
      console.error('Video upload failed:', error);
      throw new Error(`Video upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async uploadToIPFS(file: File): Promise<IPFSUploadResult> {
    try {
      // Try primary method first
      return await ipfsClient.uploadFile(file);
    } catch (error) {
      console.warn('Primary IPFS upload failed, trying Web3.Storage...', error);
      
      try {
        // Fallback to Web3.Storage
        return await ipfsClient.uploadViaWeb3Storage(file);
      } catch (fallbackError) {
        console.error('All IPFS upload methods failed:', fallbackError);
        throw new Error('All IPFS upload services are currently unavailable');
      }
    }
  }

  private validateVideoFile(file: File): void {
    // Check file type
    if (!file.type.startsWith('video/')) {
      throw new Error('File must be a video');
    }

    // Check file size (max 100MB for demo)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      throw new Error('Video file size must be less than 100MB');
    }

    // Check supported formats
    const supportedFormats = ['video/mp4', 'video/webm', 'video/ogg'];
    if (!supportedFormats.includes(file.type)) {
      throw new Error('Supported video formats: MP4, WebM, OGG');
    }
  }

  async getVideo(cid: string): Promise<StoredVideo | null> {
    try {
      // First check localStorage
      const localVideo = this.getVideoFromLocal(cid);
      if (localVideo) {
        return localVideo;
      }

      // If not found locally, try to fetch metadata from IPFS
      // This would require storing metadata separately in production
      console.log('Video not found locally:', cid);
      return null;
    } catch (error) {
      console.error('Failed to get video:', error);
      return null;
    }
  }

  async listVideos(): Promise<StoredVideo[]> {
    try {
      const videos = this.getAllVideosFromLocal();
      console.log(`Found ${videos.length} videos in local storage`);
      return videos;
    } catch (error) {
      console.error('Failed to list videos:', error);
      return [];
    }
  }

  async deleteVideo(cid: string): Promise<boolean> {
    try {
      this.removeVideoFromLocal(cid);
      console.log('Video deleted from local storage:', cid);
      return true;
    } catch (error) {
      console.error('Failed to delete video:', error);
      return false;
    }
  }

  // Local storage methods
  private saveVideoLocally(video: StoredVideo): void {
    try {
      const videos = this.getAllVideosFromLocal();
      const existingIndex = videos.findIndex(v => v.cid === video.cid);
      
      if (existingIndex >= 0) {
        videos[existingIndex] = video;
      } else {
        videos.push(video);
      }

      localStorage.setItem('upsocial_videos', JSON.stringify(videos));
    } catch (error) {
      console.error('Failed to save video locally:', error);
    }
  }

  private getVideoFromLocal(cid: string): StoredVideo | null {
    try {
      const videos = this.getAllVideosFromLocal();
      return videos.find(v => v.cid === cid) || null;
    } catch (error) {
      console.error('Failed to get video from local storage:', error);
      return null;
    }
  }

  private getAllVideosFromLocal(): StoredVideo[] {
    try {
      const videosJson = localStorage.getItem('upsocial_videos');
      return videosJson ? JSON.parse(videosJson) : [];
    } catch (error) {
      console.error('Failed to get videos from local storage:', error);
      return [];
    }
  }

  private removeVideoFromLocal(cid: string): void {
    try {
      const videos = this.getAllVideosFromLocal();
      const filteredVideos = videos.filter(v => v.cid !== cid);
      localStorage.setItem('upsocial_videos', JSON.stringify(filteredVideos));
    } catch (error) {
      console.error('Failed to remove video from local storage:', error);
    }
  }

  // Utility methods
  getVideoUrl(cid: string, gateway?: string): string {
    return ipfsClient.getGatewayUrl(cid, gateway);
  }

  isValidCID(cid: string): boolean {
    return ipfsClient.isValidCID(cid);
  }

  async getStorageStats(): Promise<{
    totalVideos: number;
    totalSize: number;
    isOnline: boolean;
  }> {
    const videos = await this.listVideos();
    const totalSize = videos.reduce((sum, video) => sum + video.size, 0);
    
    const connectivity = await ipfsClient.testConnectivity();
    
    return {
      totalVideos: videos.length,
      totalSize,
      isOnline: connectivity.gateway
    };
  }
}

// Export singleton instance
export const videoStorage = VideoStorageService.getInstance();
