import { C<PERSON> } from 'multiformats/cid'
import { getIPFSDataLayer } from './ipfs-data-layer'
import { UserProfileService } from './user-profile-service'
import type { UserProfile, AuthState } from '../../types'
import CryptoJS from 'crypto-js'

export class AuthManager {
  private static instance: AuthManager
  private dataLayer = getIPFSDataLayer()
  private profileService = UserProfileService.getInstance()
  private currentAuth: AuthState | null = null
  private isInitialized: boolean = false
  private initializationPromise: Promise<void> | null = null

  private constructor() {
    // Services are initialized externally
  }

  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager()
    }
    return AuthManager.instance
  }

  async ensureInitialized(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this.initialize()
    return this.initializationPromise
  }

  private async initialize(): Promise<void> {
    try {
      console.log('Initializing AuthManager...')
      
      // Initialize the hybrid data layer
      await this.dataLayer.initialize()
      
      console.log('Data layer initialized successfully')
      
      // Try to load any existing auth state
      await this.loadStoredAuth()
      
      this.isInitialized = true
      console.log('AuthManager initialized successfully')
    } catch (error) {
      console.error('AuthManager initialization failed:', error)
      this.isInitialized = false
      this.initializationPromise = null
      throw new Error(`Authentication system initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Password-based authentication (traditional)
  async registerWithPassword(
    username: string,
    email: string,
    password: string
  ): Promise<{ userCID: CID; profile: UserProfile }> {
    await this.ensureInitialized()
    
    console.log('Starting password registration for:', email)
    
    // Check if user already exists
    const existingUser = this.getUserMapping(email)
    if (existingUser) {
      throw new Error('User with this email already exists')
    }
    
    const passwordHash = this.hashPassword(password)
    
    const profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'> = {
      username,
      email,
      passwordHash,
      status: true,
      handle: '',
      description: '',
      location: '',
      following: [],
      followers: [],
      liked: [],
      disliked: [],
      history: []
    }

    console.log('Creating user profile on IPFS...')
    const userCID = await this.profileService.createUserProfile(profile)
    console.log('User profile created with CID:', userCID.toString())
    
    const createdProfile = await this.profileService.getUserProfile(userCID)
    
    if (!createdProfile) {
      throw new Error('Failed to create user profile')
    }

    // Store user mapping locally (in real app, would use IPNS)
    this.storeUserMapping(email, userCID)
    console.log('User mapping stored successfully')

    // Auto-login after registration
    const authState: AuthState = {
      isAuthenticated: true,
      userCID,
      profile: createdProfile
    }

    this.currentAuth = authState
    this.saveAuthState(authState)

    return { userCID, profile: createdProfile }
  }

  async loginWithPassword(email: string, password: string): Promise<AuthState> {
    await this.ensureInitialized()
    
    console.log('Starting password login for:', email)
    
    const userCID = this.getUserMapping(email)
    if (!userCID) {
      throw new Error('User not found. Please check your email or register first.')
    }

    console.log('Found user CID:', userCID.toString())
    
    try {
      const profile = await this.profileService.getUserProfile(userCID)
      if (!profile) {
        console.error('Failed to retrieve user profile from IPFS')
        throw new Error('Unable to retrieve user profile. Please check your internet connection and try again.')
      }

      const passwordHash = this.hashPassword(password)
      if (profile.passwordHash !== passwordHash) {
        throw new Error('Invalid password')
      }

      if (!profile.status) {
        throw new Error('Account is disabled')
      }

      const authState: AuthState = {
        isAuthenticated: true,
        userCID,
        profile
      }

      this.currentAuth = authState
      this.saveAuthState(authState)
      
      console.log('Login successful')
      return authState
    } catch (error) {
      console.error('Login error:', error)
      
      // If it's a password validation error, re-throw it
      if (error instanceof Error && (
        error.message.includes('Invalid password') ||
        error.message.includes('Account is disabled')
      )) {
        throw error
      }
      
      // For IPFS-related errors, provide a more helpful message
      throw new Error('Unable to retrieve your account data. This might be due to network issues. Please try again in a few moments.')
    }
  }

  // Wallet-based authentication (Web3)
  async connectWallet(): Promise<AuthState> {
    await this.ensureInitialized()
    
    console.log('Starting wallet connection...')
    
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('MetaMask not found. Please install MetaMask browser extension.')
    }

    try {
      // Request account access
      console.log('Requesting account access...')
      const accounts = await window.ethereum.request({ 
        method: 'eth_requestAccounts' 
      }) as string[]
      
      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found. Please connect your wallet.')
      }

      const address = accounts[0]
      console.log('Connected to wallet address:', address)
      
      // Check if user profile exists for this wallet
      let userCID = this.getUserMapping(address)
      
      if (!userCID) {
        console.log('Creating new profile for wallet...')
        // Create new profile for this wallet
        const newProfile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'> = {
          username: `User_${address.slice(0, 8)}`,
          email: address,
          status: true,
          handle: '',
          description: '',
          location: '',
          following: [],
          followers: [],
          liked: [],
          disliked: [],
          history: []
        }

        userCID = await this.profileService.createUserProfile(newProfile)
        if (userCID) {
          this.storeUserMapping(address, userCID)
          console.log('New wallet profile created with CID:', userCID.toString())
        } else {
          throw new Error('Failed to create wallet profile')
        }
      }

      const loadedProfile = await this.profileService.getUserProfile(userCID)
      if (!loadedProfile) {
        throw new Error('Failed to load user profile')
      }

      const authState: AuthState = {
        isAuthenticated: true,
        userCID,
        profile: loadedProfile
      }

      this.currentAuth = authState
      this.saveAuthState(authState)
      
      console.log('Wallet connection successful')
      return authState
    } catch (error) {
      console.error('Wallet connection failed:', error)
      if (error instanceof Error) {
        throw error
      }
      throw new Error('Failed to connect wallet')
    }
  }

  // Anonymous authentication
  async createAnonymousSession(nickname: string): Promise<{ code: string; authState: AuthState }> {
    const code = this.generateAnonymousCode()
    
    const profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'> = {
      username: nickname,
      email: `anonymous_${code}@temp.local`,
      status: true,
      handle: '',
      description: 'Anonymous user',
      location: '',
      following: [],
      followers: [],
      liked: [],
      disliked: [],
      history: []
    }

    const userCID = await this.profileService.createUserProfile(profile)
    const createdProfile = await this.profileService.getUserProfile(userCID)
    
    if (!createdProfile) {
      throw new Error('Failed to create anonymous profile')
    }

    // Store anonymous user mapping
    this.storeAnonymousMapping(code, nickname, userCID)

    const authState: AuthState = {
      isAuthenticated: true,
      userCID,
      profile: createdProfile
    }

    this.currentAuth = authState
    
    return { code, authState }
  }

  async loginAnonymous(nickname: string, code: string): Promise<AuthState> {
    const userCID = this.getAnonymousMapping(code, nickname)
    if (!userCID) {
      throw new Error('Anonymous session not found')
    }

    const profile = await this.profileService.getUserProfile(userCID)
    if (!profile) {
      throw new Error('Anonymous profile not found')
    }

    const authState: AuthState = {
      isAuthenticated: true,
      userCID,
      profile
    }

    this.currentAuth = authState
    
    return authState
  }

  async logout(): Promise<void> {
    this.currentAuth = null
    this.clearAuthState()
  }

  getCurrentAuth(): AuthState | null {
    return this.currentAuth
  }

  getCurrentUser(): UserProfile | null {
    return this.currentAuth?.profile || null
  }

  async loadStoredAuth(): Promise<AuthState | null> {
    try {
      const stored = localStorage.getItem('upsocial_auth')
      if (!stored) return null

      const authData = JSON.parse(stored)
      if (!authData.userCID) return null

      // Verify the profile still exists
      const profile = await this.profileService.getUserProfile(authData.userCID)
      if (!profile) return null

      const authState: AuthState = {
        isAuthenticated: true,
        userCID: authData.userCID,
        profile
      }

      this.currentAuth = authState
      return authState
    } catch (error) {
      console.error('Failed to load stored auth:', error)
      return null
    }
  }

  // Private utility methods
  private hashPassword(password: string): string {
    return CryptoJS.SHA256(password + 'upsocial_salt').toString()
  }

  private generateAnonymousCode(): string {
    return CryptoJS.lib.WordArray.random(20).toString()
  }

  private storeUserMapping(email: string, userCID: CID): void {
    const mappings = this.getUserMappings()
    mappings[email] = userCID.toString()
    localStorage.setItem('upsocial_user_mappings', JSON.stringify(mappings))
  }

  private getUserMapping(email: string): CID | null {
    const mappings = this.getUserMappings()
    const cidString = mappings[email]
    if (!cidString) return null
    
    try {
      return CID.parse(cidString)
    } catch {
      return null
    }
  }

  private getUserMappings(): Record<string, string> {
    try {
      const stored = localStorage.getItem('upsocial_user_mappings')
      return stored ? JSON.parse(stored) : {}
    } catch {
      return {}
    }
  }

  private storeAnonymousMapping(code: string, nickname: string, userCID: CID): void {
    const mappings = this.getAnonymousMappings()
    mappings[`${nickname}_${code}`] = userCID.toString()
    localStorage.setItem('upsocial_anonymous_mappings', JSON.stringify(mappings))
  }

  private getAnonymousMapping(code: string, nickname: string): CID | null {
    const mappings = this.getAnonymousMappings()
    const cidString = mappings[`${nickname}_${code}`]
    if (!cidString) return null
    
    try {
      return CID.parse(cidString)
    } catch {
      return null
    }
  }

  private getAnonymousMappings(): Record<string, string> {
    try {
      const stored = localStorage.getItem('upsocial_anonymous_mappings')
      return stored ? JSON.parse(stored) : {}
    } catch {
      return {}
    }
  }

  private saveAuthState(authState: AuthState): void {
    const toStore = {
      userCID: authState.userCID?.toString(),
      isAuthenticated: authState.isAuthenticated
    }
    localStorage.setItem('upsocial_auth', JSON.stringify(toStore))
  }

  private clearAuthState(): void {
    localStorage.removeItem('upsocial_auth')
  }
}

// Extend Window interface for MetaMask
declare global {
  interface Window {
    ethereum?: {
      request: (args: { method: string; params?: unknown[] }) => Promise<unknown>
      isMetaMask?: boolean
    }
  }
}
