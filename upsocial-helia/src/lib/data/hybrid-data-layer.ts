import { createHeliaInstance, type HeliaInstance } from '../helia/client'
import ContentService, { type Video, type Channel } from './content-service'

export interface StorageProvider {
  name: string
  type: 'ipfs' | 'apillon' | 'traditional'
  available: boolean
  priority: number
}

export interface DataLayerConfig {
  enableIPFS: boolean
  enableApillon: boolean
  enableTraditionalStorage: boolean
  primaryProvider: 'ipfs' | 'apillon' | 'traditional'
  fallbackProviders: ('ipfs' | 'apillon' | 'traditional')[]
  cacheStrategy: 'aggressive' | 'moderate' | 'minimal'
  syncInterval: number // in milliseconds
}

export interface CacheEntry<T> {
  data: T
  timestamp: number
  provider: string
  hash?: string
  expiry: number
}

export interface SyncStatus {
  isOnline: boolean
  lastSync: Date | null
  pendingOperations: number
  syncErrors: string[]
}

class HybridDataLayer {
  private static instance: HybridDataLayer
  private heliaInstance: HeliaInstance | null = null
  private contentService: ContentService
  private cache: Map<string, CacheEntry<any>> = new Map()
  private config: DataLayerConfig
  private syncStatus: SyncStatus
  private storageProviders: Map<string, StorageProvider> = new Map()
  private pendingOperations: Map<string, Promise<any>> = new Map()

  private constructor() {
    this.contentService = ContentService.getInstance()
    this.config = this.getDefaultConfig()
    this.syncStatus = {
      isOnline: navigator.onLine,
      lastSync: null,
      pendingOperations: 0,
      syncErrors: []
    }

    this.initializeProviders()
    this.setupEventListeners()
    this.startSyncLoop()
  }

  private getDefaultConfig(): DataLayerConfig {
    return {
      enableIPFS: true,
      enableApillon: true,
      enableTraditionalStorage: true,
      primaryProvider: 'ipfs',
      fallbackProviders: ['apillon', 'traditional'],
      cacheStrategy: 'moderate',
      syncInterval: 30000 // 30 seconds
    }
  }

  private async initializeProviders() {
    // Initialize IPFS provider
    try {
      this.heliaInstance = await createHeliaInstance()
      this.storageProviders.set('ipfs', {
        name: 'IPFS/Helia',
        type: 'ipfs',
        available: true,
        priority: 1
      })
    } catch (error) {
      console.warn('IPFS provider initialization failed:', error)
      this.storageProviders.set('ipfs', {
        name: 'IPFS/Helia',
        type: 'ipfs',
        available: false,
        priority: 1
      })
    }

    // Initialize Apillon provider
    this.storageProviders.set('apillon', {
      name: 'Apillon',
      type: 'apillon',
      available: true, // TODO: Check actual availability
      priority: 2
    })

    // Initialize traditional storage provider
    this.storageProviders.set('traditional', {
      name: 'Traditional Storage',
      type: 'traditional',
      available: true,
      priority: 3
    })
  }

  private setupEventListeners() {
    // Network connectivity monitoring
    window.addEventListener('online', () => {
      this.syncStatus.isOnline = true
      this.performSync()
    })

    window.addEventListener('offline', () => {
      this.syncStatus.isOnline = false
    })

    // Visibility change to sync when tab becomes active
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.syncStatus.isOnline) {
        this.performSync()
      }
    })
  }

  private startSyncLoop() {
    setInterval(() => {
      if (this.syncStatus.isOnline) {
        this.performSync()
      }
    }, this.config.syncInterval)
  }

  static getInstance(): HybridDataLayer {
    if (!HybridDataLayer.instance) {
      HybridDataLayer.instance = new HybridDataLayer()
    }
    return HybridDataLayer.instance
  }

  // Configuration methods
  updateConfig(newConfig: Partial<DataLayerConfig>) {
    this.config = { ...this.config, ...newConfig }
  }

  getConfig(): DataLayerConfig {
    return { ...this.config }
  }

  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus }
  }

  getStorageProviders(): StorageProvider[] {
    return Array.from(this.storageProviders.values())
  }

  // Cache management
  private getCacheKey(type: string, id: string): string {
    return `${type}:${id}`
  }

  private isValidCacheEntry<T>(entry: CacheEntry<T>): boolean {
    return Date.now() < entry.expiry
  }

  private setCacheEntry<T>(key: string, data: T, provider: string, hash?: string) {
    const expiry = Date.now() + this.getCacheExpiry()
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      provider,
      hash,
      expiry
    })
  }

  private getCacheExpiry(): number {
    switch (this.config.cacheStrategy) {
      case 'aggressive': return 5 * 60 * 1000 // 5 minutes
      case 'moderate': return 15 * 60 * 1000 // 15 minutes
      case 'minimal': return 60 * 60 * 1000 // 1 hour
      default: return 15 * 60 * 1000
    }
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (entry && this.isValidCacheEntry(entry)) {
      return entry.data
    }
    this.cache.delete(key)
    return null
  }

  private clearExpiredCache() {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now >= entry.expiry) {
        this.cache.delete(key)
      }
    }
  }

  // Provider selection logic
  private getAvailableProviders(): StorageProvider[] {
    return Array.from(this.storageProviders.values())
      .filter(provider => provider.available)
      .sort((a, b) => a.priority - b.priority)
  }

  private async executeWithFallback<T>(
    operation: (provider: StorageProvider) => Promise<T>,
    operationName: string
  ): Promise<T> {
    const providers = this.getAvailableProviders()
    let lastError: Error | null = null

    for (const provider of providers) {
      try {
        const result = await operation(provider)
        return result
      } catch (error) {
        console.warn(`${operationName} failed with ${provider.name}:`, error)
        lastError = error as Error
        
        // Mark provider as temporarily unavailable if it's a network error
        if (error instanceof Error && error.message.includes('network')) {
          provider.available = false
          setTimeout(() => {
            provider.available = true
          }, 60000) // Re-enable after 1 minute
        }
      }
    }

    throw lastError || new Error(`All providers failed for ${operationName}`)
  }

  // Data retrieval methods
  async getVideo(id: string): Promise<Video | null> {
    const cacheKey = this.getCacheKey('video', id)
    
    // Check cache first
    const cached = this.getCachedData<Video>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const video = await this.executeWithFallback(
        async (provider) => {
          switch (provider.type) {
            case 'ipfs':
              return await this.getVideoFromIPFS(id)
            case 'apillon':
              return await this.getVideoFromApillon(id)
            case 'traditional':
            default:
              return await this.contentService.getVideoById(id)
          }
        },
        'getVideo'
      )

      if (video) {
        this.setCacheEntry(cacheKey, video, 'hybrid')
      }

      return video
    } catch (error) {
      console.error('Failed to get video:', error)
      return null
    }
  }

  async getVideos(options: {
    page?: number
    limit?: number
    category?: string
    query?: string
  } = {}): Promise<{
    videos: Video[]
    hasMore: boolean
    nextCursor?: string
  }> {
    const cacheKey = this.getCacheKey('videos', JSON.stringify(options))
    
    // Check cache first
    const cached = this.getCachedData<{ videos: Video[], hasMore: boolean, nextCursor?: string }>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const result = await this.executeWithFallback(
        async (provider) => {
          switch (provider.type) {
            case 'ipfs':
              return await this.getVideosFromIPFS(options)
            case 'apillon':
              return await this.getVideosFromApillon(options)
            case 'traditional':
            default:
              if (options.category) {
                return await this.contentService.getVideosByCategory(options.category, options)
              } else if (options.query) {
                return await this.contentService.searchVideos(options.query, {}, options)
              } else {
                return await this.contentService.getVideos(options)
              }
          }
        },
        'getVideos'
      )

      this.setCacheEntry(cacheKey, result, 'hybrid')
      return result
    } catch (error) {
      console.error('Failed to get videos:', error)
      return { videos: [], hasMore: false }
    }
  }

  // Data storage methods
  async storeVideo(videoFile: File, metadata: {
    title: string
    description?: string
    categories: string[]
    tags: string[]
    thumbnail?: File
  }): Promise<Video | null> {
    this.syncStatus.pendingOperations++

    try {
      const result = await this.executeWithFallback(
        async (provider) => {
          switch (provider.type) {
            case 'ipfs':
              return await this.storeVideoToIPFS(videoFile, metadata)
            case 'apillon':
              return await this.storeVideoToApillon(videoFile, metadata)
            case 'traditional':
            default:
              return await this.contentService.uploadVideo(videoFile, metadata)
          }
        },
        'storeVideo'
      )

      // Invalidate related cache entries
      this.invalidateVideoCache()

      return result
    } catch (error) {
      console.error('Failed to store video:', error)
      return null
    } finally {
      this.syncStatus.pendingOperations--
    }
  }

  // IPFS-specific methods
  private async getVideoFromIPFS(id: string): Promise<Video | null> {
    if (!this.heliaInstance) throw new Error('IPFS not available')
    
    try {
      // TODO: Implement IPFS video retrieval
      // This would involve resolving IPNS records and fetching video metadata
      console.log('Getting video from IPFS:', id)
      return null
    } catch (error) {
      throw new Error(`IPFS video retrieval failed: ${error}`)
    }
  }

  private async getVideosFromIPFS(options: any): Promise<{
    videos: Video[]
    hasMore: boolean
    nextCursor?: string
  }> {
    if (!this.heliaInstance) throw new Error('IPFS not available')
    
    try {
      // TODO: Implement IPFS video listing
      console.log('Getting videos from IPFS:', options)
      return { videos: [], hasMore: false }
    } catch (error) {
      throw new Error(`IPFS video listing failed: ${error}`)
    }
  }

  private async storeVideoToIPFS(videoFile: File, metadata: any): Promise<Video | null> {
    if (!this.heliaInstance) throw new Error('IPFS not available')
    
    try {
      // Upload video file to IPFS
      const videoArrayBuffer = await videoFile.arrayBuffer()
      const videoData = new Uint8Array(videoArrayBuffer)
      const videoCid = await this.heliaInstance.fs.addBytes(videoData)

      // Upload thumbnail if provided
      let thumbnailCid
      if (metadata.thumbnail) {
        const thumbnailArrayBuffer = await metadata.thumbnail.arrayBuffer()
        const thumbnailData = new Uint8Array(thumbnailArrayBuffer)
        thumbnailCid = await this.heliaInstance.fs.addBytes(thumbnailData)
      }

      // Create video metadata
      const video: Video = {
        id: `video-${Date.now()}`,
        title: metadata.title,
        creator: 'CurrentUser', // TODO: Get from auth
        thumbnail: thumbnailCid ? `/ipfs/${thumbnailCid}` : '/assets/default.png',
        videoUrl: `/ipfs/${videoCid}`,
        views: 0,
        likes: 0,
        duration: '0:00', // TODO: Calculate from video
        ipfsHash: videoCid.toString(),
        createdAt: new Date(),
        categories: metadata.categories,
        tags: metadata.tags,
        description: metadata.description
      }

      // Store metadata to IPFS
      const metadataJson = JSON.stringify(video)
      const metadataData = new TextEncoder().encode(metadataJson)
      const metadataCid = await this.heliaInstance.fs.addBytes(metadataData)

      // TODO: Update IPNS record with new content
      console.log('Video stored to IPFS:', { videoCid, metadataCid })

      return video
    } catch (error) {
      throw new Error(`IPFS video storage failed: ${error}`)
    }
  }

  // Apillon-specific methods
  private async getVideoFromApillon(id: string): Promise<Video | null> {
    try {
      // TODO: Implement Apillon video retrieval
      console.log('Getting video from Apillon:', id)
      return null
    } catch (error) {
      throw new Error(`Apillon video retrieval failed: ${error}`)
    }
  }

  private async getVideosFromApillon(options: any): Promise<{
    videos: Video[]
    hasMore: boolean
    nextCursor?: string
  }> {
    try {
      // TODO: Implement Apillon video listing
      console.log('Getting videos from Apillon:', options)
      return { videos: [], hasMore: false }
    } catch (error) {
      throw new Error(`Apillon video listing failed: ${error}`)
    }
  }

  private async storeVideoToApillon(videoFile: File, metadata: any): Promise<Video | null> {
    try {
      // TODO: Implement Apillon video storage
      console.log('Storing video to Apillon:', { videoFile, metadata })
      return null
    } catch (error) {
      throw new Error(`Apillon video storage failed: ${error}`)
    }
  }

  // Sync operations
  private async performSync() {
    try {
      this.syncStatus.lastSync = new Date()
      this.clearExpiredCache()
      
      // TODO: Implement data synchronization between providers
      console.log('Performing data sync...')
      
      // Clear old sync errors
      this.syncStatus.syncErrors = []
    } catch (error) {
      console.error('Sync failed:', error)
      this.syncStatus.syncErrors.push(error instanceof Error ? error.message : 'Unknown sync error')
    }
  }

  // Cache invalidation
  private invalidateVideoCache() {
    for (const key of this.cache.keys()) {
      if (key.startsWith('video:') || key.startsWith('videos:')) {
        this.cache.delete(key)
      }
    }
  }

  // Analytics and monitoring
  getCacheStats() {
    const totalEntries = this.cache.size
    const expiredEntries = Array.from(this.cache.values()).filter(
      entry => Date.now() >= entry.expiry
    ).length

    return {
      totalEntries,
      expiredEntries,
      validEntries: totalEntries - expiredEntries,
      memoryUsage: JSON.stringify(Array.from(this.cache.entries())).length
    }
  }

  // Utility methods
  async clearCache() {
    this.cache.clear()
  }

  async clearCacheByType(type: string) {
    for (const key of this.cache.keys()) {
      if (key.startsWith(`${type}:`)) {
        this.cache.delete(key)
      }
    }
  }

  // Shutdown
  async shutdown() {
    this.cache.clear()
    this.pendingOperations.clear()
    if (this.heliaInstance) {
      await this.heliaInstance.stop()
    }
  }
}

export default HybridDataLayer