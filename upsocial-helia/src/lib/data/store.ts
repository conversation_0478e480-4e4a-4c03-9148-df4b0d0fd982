import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { AuthState, UserProfile, ContentItem, Channel, Playlist, PeerInfo } from '../../types'
import type { CID } from 'multiformats/cid'

interface AppStore {
  // Authentication state
  auth: AuthState
  setAuth: (auth: AuthState) => void
  clearAuth: () => void

  // User data
  userProfile: UserProfile | null
  setUserProfile: (profile: UserProfile) => void

  // Content state
  feed: ContentItem[]
  setFeed: (content: ContentItem[]) => void
  addToFeed: (content: ContentItem) => void
  updateFeedItem: (cid: string, updates: Partial<ContentItem>) => void

  // User's content
  myContent: ContentItem[]
  setMyContent: (content: ContentItem[]) => void
  addMyContent: (content: ContentItem) => void

  // Channels
  channels: Channel[]
  setChannels: (channels: Channel[]) => void
  addChannel: (channel: Channel) => void
  updateChannel: (id: string, updates: Partial<Channel>) => void

  // Playlists
  playlists: Playlist[]
  setPlaylists: (playlists: Playlist[]) => void
  addPlaylist: (playlist: Playlist) => void
  updatePlaylist: (id: string, updates: Partial<Playlist>) => void

  // Network state
  peers: PeerInfo[]
  setPeers: (peers: PeerInfo[]) => void
  isOnline: boolean
  setOnline: (online: boolean) => void
  syncStatus: 'syncing' | 'synced' | 'error'
  setSyncStatus: (status: 'syncing' | 'synced' | 'error') => void

  // UI state
  isLoading: boolean
  setLoading: (loading: boolean) => void
  currentView: 'dashboard' | 'profile' | 'upload' | 'channels' | 'playlists' | 'settings'
  setCurrentView: (view: 'dashboard' | 'profile' | 'upload' | 'channels' | 'playlists' | 'settings') => void

  // Search and discovery
  searchQuery: string
  setSearchQuery: (query: string) => void
  searchResults: ContentItem[]
  setSearchResults: (results: ContentItem[]) => void

  // Notifications
  notifications: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    message: string
    timestamp: string
  }>
  addNotification: (notification: {
    type: 'info' | 'success' | 'warning' | 'error'
    message: string
  }) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
}

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Authentication state
      auth: {
        isAuthenticated: false
      },
      setAuth: (auth) => set({ auth, userProfile: auth.profile || null }),
      clearAuth: () => set({ 
        auth: { isAuthenticated: false }, 
        userProfile: null,
        myContent: [],
        channels: [],
        playlists: []
      }),

      // User data
      userProfile: null,
      setUserProfile: (userProfile) => set({ userProfile }),

      // Content state
      feed: [],
      setFeed: (feed) => set({ feed }),
      addToFeed: (content) => set((state) => ({ 
        feed: [content, ...state.feed] 
      })),
      updateFeedItem: (cid, updates) => set((state) => ({
        feed: state.feed.map(item => 
          item.contentCID.toString() === cid 
            ? { ...item, ...updates } 
            : item
        )
      })),

      // User's content
      myContent: [],
      setMyContent: (myContent) => set({ myContent }),
      addMyContent: (content) => set((state) => ({ 
        myContent: [content, ...state.myContent] 
      })),

      // Channels
      channels: [],
      setChannels: (channels) => set({ channels }),
      addChannel: (channel) => set((state) => ({ 
        channels: [...state.channels, channel] 
      })),
      updateChannel: (id, updates) => set((state) => ({
        channels: state.channels.map(channel => 
          channel.id === id ? { ...channel, ...updates } : channel
        )
      })),

      // Playlists
      playlists: [],
      setPlaylists: (playlists) => set({ playlists }),
      addPlaylist: (playlist) => set((state) => ({ 
        playlists: [...state.playlists, playlist] 
      })),
      updatePlaylist: (id, updates) => set((state) => ({
        playlists: state.playlists.map(playlist => 
          playlist.id === id ? { ...playlist, ...updates } : playlist
        )
      })),

      // Network state
      peers: [],
      setPeers: (peers) => set({ peers }),
      isOnline: false,
      setOnline: (isOnline) => set({ isOnline }),
      syncStatus: 'syncing',
      setSyncStatus: (syncStatus) => set({ syncStatus }),

      // UI state
      isLoading: false,
      setLoading: (isLoading) => set({ isLoading }),
      currentView: 'dashboard',
      setCurrentView: (currentView) => set({ currentView }),

      // Search and discovery
      searchQuery: '',
      setSearchQuery: (searchQuery) => set({ searchQuery }),
      searchResults: [],
      setSearchResults: (searchResults) => set({ searchResults }),

      // Notifications
      notifications: [],
      addNotification: (notification) => set((state) => ({
        notifications: [...state.notifications, {
          ...notification,
          id: Date.now().toString(),
          timestamp: new Date().toISOString()
        }]
      })),
      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      clearNotifications: () => set({ notifications: [] })
    }),
    {
      name: 'upsocial-app-store',
      partialize: (state) => ({
        // Only persist certain parts of the state
        auth: state.auth,
        userProfile: state.userProfile,
        currentView: state.currentView,
        // Don't persist dynamic data like feed, peers, etc.
      })
    }
  )
)

// Selector hooks for common operations
export const useAuth = () => useAppStore((state) => state.auth)
export const useUserProfile = () => useAppStore((state) => state.userProfile)
export const useFeed = () => useAppStore((state) => state.feed)
export const useMyContent = () => useAppStore((state) => state.myContent)
export const useChannels = () => useAppStore((state) => state.channels)
export const usePlaylists = () => useAppStore((state) => state.playlists)
export const useIsLoading = () => useAppStore((state) => state.isLoading)
export const useCurrentView = () => useAppStore((state) => state.currentView)
export const useNotifications = () => useAppStore((state) => state.notifications)
