import { C<PERSON> } from 'multiformats/cid'
import { getHeliaInstance } from '../helia/client'
import type { UserProfile, ContentItem, Channel, Playlist, IPFSData } from '../../types'
import CryptoJS from 'crypto-js'

export class IPFSDataLayer {
  private static instance: IPFSDataLayer
  private initialized = false
  
  static getInstance(): IPFSDataLayer {
    if (!IPFSDataLayer.instance) {
      IPFSDataLayer.instance = new IPFSDataLayer()
    }
    return IPFSDataLayer.instance
  }

  async initialize(): Promise<void> {
    try {
      console.log('Initializing IPFS Data Layer...')
      
      // Initialize Helia instance
      await getHeliaInstance()
      
      this.initialized = true
      console.log('IPFS Data Layer initialized successfully')
    } catch (error) {
      console.error('Failed to initialize IPFS Data Layer:', error)
      throw new Error(`IPFS Data Layer initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  isInitialized(): boolean {
    return this.initialized
  }

  // Generic IPFS operations
  async storeData<T>(data: T): Promise<CID> {
    try {
      console.log('Storing data on IPFS...')
      const { strings } = await getHeliaInstance()
      const wrappedData: IPFSData<T> = {
        data,
        timestamp: new Date().toISOString(),
        version: 1
      }
      const dataString = JSON.stringify(wrappedData)
      const cid = await strings.add(dataString)
      console.log('Data stored successfully with CID:', cid.toString())
      
      // Cache the data locally for fallback
      const cacheKey = `ipfs_cache_${cid.toString()}`
      try {
        localStorage.setItem(cacheKey, dataString)
        console.log('Data cached locally for fallback')
      } catch (cacheError) {
        console.warn('Failed to cache data locally:', cacheError)
      }

      // Pin to Apillon for network propagation (don't await - fire and forget)
      this.pinToApillon(cid.toString(), dataString).catch(error => 
        console.warn('Failed to pin to Apillon:', error)
      )
      
      return cid
    } catch (error) {
      console.error('Failed to store data on IPFS:', error)
      throw new Error(`Failed to store data: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  // Private method to pin data to Apillon via API route
  private async pinToApillon(cid: string, data: string): Promise<void> {
    try {
      console.log('Pinning to Apillon via API route, CID:', cid)
      const response = await fetch('/api/apillon/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          cid: cid,
          data: data
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Successfully pinned to Apillon:', result)
      } else {
        const error = await response.json()
        console.error('Failed to pin to Apillon:', error)
      }
    } catch (error) {
      console.error('Error pinning to Apillon:', error)
      // Don't throw - pinning is a background operation
    }
  }

  // IPNS Methods for mutable content
  private async publishToIPNS(cid: string, name: string): Promise<string | null> {
    try {
      console.log('Publishing to IPNS via API route, CID:', cid, 'Name:', name)
      const response = await fetch('/api/apillon/ipns/publish', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          cid: cid,
          name: name
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Successfully published to IPNS:', result)
        return result.ipnsResult?.name || name
      } else {
        const error = await response.json()
        console.error('Failed to publish to IPNS:', error)
        return null
      }
    } catch (error) {
      console.error('Error publishing to IPNS:', error)
      return null
    }
  }

  private async resolveIPNS(name: string): Promise<string | null> {
    try {
      console.log('Resolving IPNS name via API route:', name)
      const response = await fetch(`/api/apillon/ipns/resolve?name=${encodeURIComponent(name)}`)

      if (response.ok) {
        const result = await response.json()
        console.log('Successfully resolved IPNS:', result)
        return result.cid
      } else {
        const error = await response.json()
        console.error('Failed to resolve IPNS:', error)
        return null
      }
    } catch (error) {
      console.error('Error resolving IPNS:', error)
      return null
    }
  }

  async retrieveData<T>(cid: CID): Promise<IPFSData<T> | null> {
    console.log('Retrieving data from IPFS, CID:', cid.toString())
    
    // First try with cached data
    const cacheKey = `ipfs_cache_${cid.toString()}`
    const cachedData = localStorage.getItem(cacheKey)
    
    if (cachedData) {
      console.log('Found cached data, using as primary source')
      try {
        return JSON.parse(cachedData) as IPFSData<T>
      } catch (parseError) {
        console.error('Failed to parse cached data:', parseError)
      }
    }
    
    // Try Helia first with timeout
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('IPFS retrieval timeout')), 8000) // 8 second timeout
      })
      
      const { strings } = await getHeliaInstance()
      const retrievalPromise = strings.get(cid)
      const resultString = await Promise.race([retrievalPromise, timeoutPromise])
      const result = JSON.parse(resultString)
      
      // Cache successful retrieval
      try {
        localStorage.setItem(cacheKey, resultString)
      } catch (cacheError) {
        console.warn('Failed to cache data:', cacheError)
      }
      
      console.log('Data retrieved successfully via Helia')
      return result as IPFSData<T>
    } catch (heliaError) {
      console.error('Helia retrieval failed:', heliaError)
      
      // Fallback to Apillon gateway
      try {
        console.log('Trying Apillon gateway fallback...')
        const response = await fetch(`/api/apillon/retrieve?cid=${cid.toString()}`, {
          signal: AbortSignal.timeout(10000)
        })
        
        if (response.ok) {
          const resultString = await response.text()
          const result = JSON.parse(resultString)
          
          // Cache successful retrieval
          try {
            localStorage.setItem(cacheKey, resultString)
          } catch (cacheError) {
            console.warn('Failed to cache data:', cacheError)
          }
          
          console.log('Data retrieved successfully via Apillon gateway')
          return result as IPFSData<T>
        } else {
          console.error('Apillon gateway failed with status:', response.status)
        }
      } catch (apillonError) {
        console.error('Apillon gateway retrieval failed:', apillonError)
      }
      
      // Final fallback to public gateways
      try {
        console.log('Trying public IPFS gateways fallback...')
        const response = await fetch(`/api/ipfs?cid=${cid.toString()}`, {
          signal: AbortSignal.timeout(10000)
        })
        
        if (response.ok) {
          const resultString = await response.text()
          const result = JSON.parse(resultString)
          
          // Cache successful retrieval
          try {
            localStorage.setItem(cacheKey, resultString)
          } catch (cacheError) {
            console.warn('Failed to cache data:', cacheError)
          }
          
          console.log('Data retrieved successfully via public gateways')
          return result as IPFSData<T>
        } else {
          console.error('Public gateways failed with status:', response.status)
        }
      } catch (apiError) {
        console.error('Public gateway retrieval failed:', apiError)
      }
      
      // If we had cached data but couldn't parse it, return null
      return null
    }
  }

  async storeFile(file: File): Promise<CID> {
    const { fs } = await getHeliaInstance()
    const arrayBuffer = await file.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)
    return await fs.addFile({
      path: file.name,
      content: uint8Array
    })
  }

  async retrieveFile(cid: CID): Promise<Uint8Array> {
    const { fs } = await getHeliaInstance()
    const chunks: Uint8Array[] = []
    for await (const chunk of fs.cat(cid)) {
      chunks.push(chunk)
    }
    const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
    const result = new Uint8Array(totalLength)
    let offset = 0
    for (const chunk of chunks) {
      result.set(chunk, offset)
      offset += chunk.length
    }
    return result
  }

  // User Profile operations
  async createUserProfile(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<CID> {
    const userProfile: UserProfile = {
      ...profile,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return await this.storeData(userProfile)
  }

  async updateUserProfile(cid: CID, updates: Partial<UserProfile>): Promise<CID> {
    const existingData = await this.retrieveData<UserProfile>(cid)
    if (!existingData) {
      throw new Error('User profile not found')
    }
    
    const updatedProfile: UserProfile = {
      ...existingData.data,
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    return await this.storeData(updatedProfile)
  }

  async getUserProfile(cid: CID): Promise<UserProfile | null> {
    const data = await this.retrieveData<UserProfile>(cid)
    return data?.data || null
  }

  // Enhanced User Profile operations with IPNS support
  async createUserProfileWithIPNS(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ cid: CID, ipnsName: string | null }> {
    const userProfile: UserProfile = {
      ...profile,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    const cid = await this.storeData(userProfile)
    
    // Create IPNS name for this user profile using their username or email
    const ipnsName = profile.username || profile.email.replace('@', '-at-').replace(/[^a-zA-Z0-9-]/g, '-')
    const publishedName = await this.publishToIPNS(cid.toString(), `user-${ipnsName}`)
    
    return {
      cid,
      ipnsName: publishedName
    }
  }

  async updateUserProfileWithIPNS(currentCID: CID, updates: Partial<UserProfile>, ipnsName?: string): Promise<{ cid: CID, ipnsName: string | null }> {
    const existingData = await this.retrieveData<UserProfile>(currentCID)
    if (!existingData) {
      throw new Error('User profile not found')
    }
    
    const updatedProfile: UserProfile = {
      ...existingData.data,
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    const newCID = await this.storeData(updatedProfile)
    
    // Update IPNS record if name is provided
    let publishedName: string | null = null
    if (ipnsName) {
      publishedName = await this.publishToIPNS(newCID.toString(), ipnsName)
    }
    
    return {
      cid: newCID,
      ipnsName: publishedName
    }
  }

  async getUserProfileByIPNS(ipnsName: string): Promise<UserProfile | null> {
    try {
      // Resolve IPNS name to get current CID
      const cid = await this.resolveIPNS(ipnsName)
      if (!cid) {
        console.warn('Could not resolve IPNS name:', ipnsName)
        return null
      }
      
      // Parse CID and retrieve data
      const cidObj = CID.parse(cid)
      return await this.getUserProfile(cidObj)
    } catch (error) {
      console.error('Failed to get user profile by IPNS:', error)
      return null
    }
  }

  // Content operations
  async createContent(content: Omit<ContentItem, 'id' | 'createdAt' | 'updatedAt'>): Promise<CID> {
    const contentItem: ContentItem = {
      ...content,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return await this.storeData(contentItem)
  }

  async updateContent(cid: CID, updates: Partial<ContentItem>): Promise<CID> {
    const existingData = await this.retrieveData<ContentItem>(cid)
    if (!existingData) {
      throw new Error('Content not found')
    }
    
    const updatedContent: ContentItem = {
      ...existingData.data,
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    return await this.storeData(updatedContent)
  }

  async getContent(cid: CID): Promise<ContentItem | null> {
    const data = await this.retrieveData<ContentItem>(cid)
    return data?.data || null
  }

  // Channel operations
  async createChannel(channel: Omit<Channel, 'id' | 'createdAt' | 'updatedAt'>): Promise<CID> {
    const channelData: Channel = {
      ...channel,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return await this.storeData(channelData)
  }

  async updateChannel(cid: CID, updates: Partial<Channel>): Promise<CID> {
    const existingData = await this.retrieveData<Channel>(cid)
    if (!existingData) {
      throw new Error('Channel not found')
    }
    
    const updatedChannel: Channel = {
      ...existingData.data,
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    return await this.storeData(updatedChannel)
  }

  async getChannel(cid: CID): Promise<Channel | null> {
    const data = await this.retrieveData<Channel>(cid)
    return data?.data || null
  }

  // Playlist operations
  async createPlaylist(playlist: Omit<Playlist, 'id' | 'createdAt' | 'updatedAt'>): Promise<CID> {
    const playlistData: Playlist = {
      ...playlist,
      id: this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return await this.storeData(playlistData)
  }

  async updatePlaylist(cid: CID, updates: Partial<Playlist>): Promise<CID> {
    const existingData = await this.retrieveData<Playlist>(cid)
    if (!existingData) {
      throw new Error('Playlist not found')
    }
    
    const updatedPlaylist: Playlist = {
      ...existingData.data,
      ...updates,
      updatedAt: new Date().toISOString()
    }
    
    return await this.storeData(updatedPlaylist)
  }

  async getPlaylist(cid: CID): Promise<Playlist | null> {
    const data = await this.retrieveData<Playlist>(cid)
    return data?.data || null
  }

  // Social operations
  async followUser(userCID: CID, targetUserCID: CID): Promise<CID> {
    const userData = await this.retrieveData<UserProfile>(userCID)
    if (!userData) {
      throw new Error('User not found')
    }

    const following = userData.data.following || []
    if (!following.find(cid => cid.toString() === targetUserCID.toString())) {
      following.push(targetUserCID)
    }

    return await this.updateUserProfile(userCID, { following })
  }

  async unfollowUser(userCID: CID, targetUserCID: CID): Promise<CID> {
    const userData = await this.retrieveData<UserProfile>(userCID)
    if (!userData) {
      throw new Error('User not found')
    }

    const following = userData.data.following?.filter(
      cid => cid.toString() !== targetUserCID.toString()
    ) || []

    return await this.updateUserProfile(userCID, { following })
  }

  async likeContent(userCID: CID, contentCID: CID): Promise<CID> {
    const userData = await this.retrieveData<UserProfile>(userCID)
    if (!userData) {
      throw new Error('User not found')
    }

    const liked = userData.data.liked || []
    const disliked = userData.data.disliked || []

    // Remove from disliked if present
    const newDisliked = disliked.filter(cid => cid.toString() !== contentCID.toString())
    
    // Add to liked if not already present
    if (!liked.find(cid => cid.toString() === contentCID.toString())) {
      liked.push(contentCID)
    }

    return await this.updateUserProfile(userCID, { 
      liked, 
      disliked: newDisliked 
    })
  }

  async dislikeContent(userCID: CID, contentCID: CID): Promise<CID> {
    const userData = await this.retrieveData<UserProfile>(userCID)
    if (!userData) {
      throw new Error('User not found')
    }

    const liked = userData.data.liked || []
    const disliked = userData.data.disliked || []

    // Remove from liked if present
    const newLiked = liked.filter(cid => cid.toString() !== contentCID.toString())
    
    // Add to disliked if not already present
    if (!disliked.find(cid => cid.toString() === contentCID.toString())) {
      disliked.push(contentCID)
    }

    return await this.updateUserProfile(userCID, { 
      liked: newLiked, 
      disliked 
    })
  }

  // Utility functions
  private generateId(): string {
    return CryptoJS.lib.WordArray.random(16).toString()
  }

  async searchContent(query: string, limit: number = 20): Promise<ContentItem[]> {
    // This is a simplified search - in a real implementation,
    // you'd want to maintain search indices
    // For now, this is a placeholder that would need to be implemented
    // with a more sophisticated indexing system
    console.warn('Search functionality not yet implemented')
    return []
  }

  async getRecommendedContent(userCID: CID, limit: number = 20): Promise<ContentItem[]> {
    // Placeholder for recommendation algorithm
    // Would analyze user's liked content, following, etc.
    console.warn('Recommendation functionality not yet implemented')
    return []
  }
}

// Create and export a singleton instance
let ipfsDataLayerInstance: IPFSDataLayer | null = null

export const getIPFSDataLayer = (): IPFSDataLayer => {
  if (!ipfsDataLayerInstance) {
    ipfsDataLayerInstance = new IPFSDataLayer()
  }
  return ipfsDataLayerInstance
}

export default IPFSDataLayer
