import { createHeliaInstance, type HeliaInstance } from '../helia/client'
import { Video, VideoSearchFilters } from '../../types/video'

export type { Video, VideoSearchFilters as SearchFilters }

export interface Channel {
  id: string
  name: string
  handle: string
  description: string
  avatar?: string
  banner?: string
  location?: string
  tags: string[]
  createdAt: Date
  ownerId: string
  stats: {
    videos: number
    followers: number
    totalViews: number
  }
}

export interface PaginationOptions {
  page?: number
  limit?: number
  cursor?: string
}

class ContentService {
  private static instance: ContentService
  private heliaInstance: HeliaInstance | null = null
  private mockVideos: Video[] = []

  private constructor() {
    this.initializeHelia()
    this.generateMockData()
  }

  private async initializeHelia() {
    try {
      this.heliaInstance = await createHeliaInstance()
    } catch (error) {
      console.error('Failed to initialize Helia:', error)
    }
  }

  static getInstance(): ContentService {
    if (!ContentService.instance) {
      ContentService.instance = new ContentService()
    }
    return ContentService.instance
  }

  private generateMockData() {
    // Generate mock video data for development
    this.mockVideos = Array.from({ length: 50 }, (_, index) => ({
      id: `video-${index + 1}`,
      title: this.generateVideoTitle(),
      creator: this.generateCreatorName(),
      thumbnail: '/assets/default.png',
      videoUrl: this.generateVideoUrl(),
      views: Math.floor(Math.random() * 100000),
      likes: Math.floor(Math.random() * 10000),
      duration: this.generateDuration(),
      ipfsHash: `Qm${Math.random().toString(36).substring(7)}`,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      categories: this.generateCategories(),
      tags: this.generateTags(),
      description: this.generateDescription(),
      channelId: `channel-${Math.floor(Math.random() * 10) + 1}`
    }))
  }

  private generateVideoTitle(): string {
    const titles = [
      'Amazing Web3 Tutorial',
      'Decentralized Video Streaming',
      'IPFS Made Simple',
      'Future of Social Media',
      'Blockchain Explained',
      'NFT Creation Guide',
      'DeFi Protocols Overview',
      'Smart Contract Development',
      'Cryptocurrency Trading Tips',
      'Metaverse Technology',
      'Digital Art Creation',
      'Gaming on Blockchain',
      'Music Production Tutorial',
      'Cooking with Tech',
      'Travel Vlog Adventures',
      'Fitness and Health Tips',
      'Educational Content',
      'Comedy Sketches',
      'Documentary Stories',
      'Science Experiments'
    ]
    return titles[Math.floor(Math.random() * titles.length)]
  }

  private generateCreatorName(): string {
    const creators = [
      'TechGuru', 'CryptoExplorer', 'BlockchainBasics', 'DigitalNomad',
      'CodeMaster', 'ArtisticVision', 'MusicMaker', 'FoodieExpert',
      'TravelBug', 'FitnessCoach', 'EduTeacher', 'ComedyKing',
      'DocMaker', 'ScienceNerd', 'GameStreamer', 'CreativeDesigner'
    ]
    return creators[Math.floor(Math.random() * creators.length)]
  }

  private generateVideoUrl(): string {
    // In production, this would be IPFS URLs
    return `https://sample-videos.com/video-${Math.floor(Math.random() * 100)}.mp4`
  }

  private generateDuration(): string {
    const minutes = Math.floor(Math.random() * 30) + 1
    const seconds = Math.floor(Math.random() * 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  private generateCategories(): string[] {
    const categories = [
      'Animation', 'Autos & Vehicles', 'Beauty & Fashion', 'Comedy',
      'Cooking & Food', 'DIY & Crafts', 'Documentary', 'Education',
      'Entertainment', 'Film & Animation', 'Gaming', 'Health & Fitness',
      'How-to & Style', 'Kids & Family', 'Music', 'News & Politics',
      'Nonprofits & Activism', 'People & Blogs', 'Pets & Animals',
      'Science & Technology', 'Sports', 'Travel & Events',
      'Unboxing & Reviews', 'Blogs'
    ]
    const count = Math.floor(Math.random() * 3) + 1
    return categories.sort(() => 0.5 - Math.random()).slice(0, count)
  }

  private generateTags(): string[] {
    const tags = [
      'web3', 'blockchain', 'ipfs', 'decentralized', 'crypto',
      'nft', 'defi', 'tutorial', 'guide', 'tips', 'tricks',
      'howto', 'review', 'unboxing', 'vlog', 'comedy',
      'music', 'art', 'gaming', 'tech', 'science'
    ]
    const count = Math.floor(Math.random() * 5) + 1
    return tags.sort(() => 0.5 - Math.random()).slice(0, count)
  }

  private generateDescription(): string {
    const descriptions = [
      'An amazing tutorial covering the basics of this topic.',
      'In-depth analysis and practical examples included.',
      'Everything you need to know to get started.',
      'Advanced techniques and best practices revealed.',
      'Step-by-step guide with real-world applications.'
    ]
    return descriptions[Math.floor(Math.random() * descriptions.length)]
  }

  // Public API methods

  async getVideos(options: PaginationOptions = {}): Promise<{
    videos: Video[]
    hasMore: boolean
    nextCursor?: string
  }> {
    const { page = 1, limit = 20 } = options
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    const videos = this.mockVideos.slice(startIndex, endIndex)
    const hasMore = endIndex < this.mockVideos.length

    return {
      videos,
      hasMore,
      nextCursor: hasMore ? `page-${page + 1}` : undefined
    }
  }

  async getVideosByCategory(
    category: string,
    options: PaginationOptions = {}
  ): Promise<{
    videos: Video[]
    hasMore: boolean
    nextCursor?: string
  }> {
    let filteredVideos = this.mockVideos

    if (category === 'FOR ME') {
      // TODO: Implement personalized algorithm
      filteredVideos = this.mockVideos.sort(() => 0.5 - Math.random())
    } else if (category === 'SUBSCRIPTIONS') {
      // TODO: Filter by subscribed channels
      filteredVideos = this.mockVideos.filter(() => Math.random() > 0.7)
    } else if (category !== 'NEWEST') {
      filteredVideos = this.mockVideos.filter(video =>
        video.categories.includes(category) ||
        video.title.toLowerCase().includes(category.toLowerCase())
      )
    }

    const { page = 1, limit = 20 } = options
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    const videos = filteredVideos.slice(startIndex, endIndex)
    const hasMore = endIndex < filteredVideos.length

    return {
      videos,
      hasMore,
      nextCursor: hasMore ? `page-${page + 1}` : undefined
    }
  }

  async searchVideos(
    query: string,
    filters: VideoSearchFilters = {},
    options: PaginationOptions = {}
  ): Promise<{
    videos: Video[]
    hasMore: boolean
    nextCursor?: string
  }> {
    let filteredVideos = this.mockVideos.filter(video =>
      video.title.toLowerCase().includes(query.toLowerCase()) ||
      video.description?.toLowerCase().includes(query.toLowerCase()) ||
      video.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ||
      video.creator.toLowerCase().includes(query.toLowerCase())
    )

    // Apply filters
    if (filters.category) {
      filteredVideos = filteredVideos.filter(video =>
        video.categories.includes(filters.category!)
      )
    }

    if (filters.duration) {
      filteredVideos = filteredVideos.filter(video => {
        const [minutes] = video.duration.split(':').map(Number)
        switch (filters.duration) {
          case 'short': return minutes < 4
          case 'medium': return minutes >= 4 && minutes <= 20
          case 'long': return minutes > 20
          default: return true
        }
      })
    }

    if (filters.uploadDate) {
      const now = new Date()
      filteredVideos = filteredVideos.filter(video => {
        const timeDiff = now.getTime() - video.createdAt.getTime()
        switch (filters.uploadDate) {
          case 'hour': return timeDiff < 60 * 60 * 1000
          case 'day': return timeDiff < 24 * 60 * 60 * 1000
          case 'week': return timeDiff < 7 * 24 * 60 * 60 * 1000
          case 'month': return timeDiff < 30 * 24 * 60 * 60 * 1000
          case 'year': return timeDiff < 365 * 24 * 60 * 60 * 1000
          default: return true
        }
      })
    }

    // Apply sorting
    if (filters.sortBy) {
      filteredVideos.sort((a, b) => {
        switch (filters.sortBy) {
          case 'date':
            return b.createdAt.getTime() - a.createdAt.getTime()
          case 'views':
            return b.views - a.views
          case 'rating':
            return b.likes - a.likes
          case 'relevance':
          default:
            // Simple relevance scoring based on query matches
            const aScore = this.calculateRelevanceScore(a, query)
            const bScore = this.calculateRelevanceScore(b, query)
            return bScore - aScore
        }
      })
    }

    const { page = 1, limit = 20 } = options
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    const videos = filteredVideos.slice(startIndex, endIndex)
    const hasMore = endIndex < filteredVideos.length

    return {
      videos,
      hasMore,
      nextCursor: hasMore ? `page-${page + 1}` : undefined
    }
  }

  private calculateRelevanceScore(video: Video, query: string): number {
    let score = 0
    const lowerQuery = query.toLowerCase()

    // Title match (highest weight)
    if (video.title.toLowerCase().includes(lowerQuery)) {
      score += 10
    }

    // Description match
    if (video.description?.toLowerCase().includes(lowerQuery)) {
      score += 5
    }

    // Tag matches
    video.tags?.forEach(tag => {
      if (tag.toLowerCase().includes(lowerQuery)) {
        score += 3
      }
    })

    // Creator match
    if (video.creator.toLowerCase().includes(lowerQuery)) {
      score += 2
    }

    return score
  }

  async getVideoById(id: string): Promise<Video | null> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))

    return this.mockVideos.find(video => video.id === id) || null
  }

  async likeVideo(videoId: string): Promise<boolean> {
    const video = this.mockVideos.find(v => v.id === videoId)
    if (video) {
      video.likes += 1
      // TODO: Store like in IPFS/database
      return true
    }
    return false
  }

  async shareVideo(videoId: string): Promise<string | null> {
    const video = this.mockVideos.find(v => v.id === videoId)
    if (video) {
      // Generate shareable URL
      const shareUrl = `${window.location.origin}/video/${videoId}`
      // TODO: Store share event in analytics
      return shareUrl
    }
    return null
  }

  async uploadVideo(videoFile: File, metadata: {
    title: string
    description?: string
    categories: string[]
    tags: string[]
    thumbnail?: File
  }): Promise<Video | null> {
    try {
      // TODO: Implement actual IPFS upload via Helia
      const ipfsHash = await this.uploadToIPFS(videoFile)
      const thumbnailHash = metadata.thumbnail ? await this.uploadToIPFS(metadata.thumbnail) : undefined

      const newVideo: Video = {
        id: `video-${Date.now()}`,
        title: metadata.title,
        creator: 'CurrentUser', // TODO: Get from auth
        thumbnail: thumbnailHash ? `/api/ipfs/${thumbnailHash}` : '/assets/default.png',
        videoUrl: `/api/ipfs/${ipfsHash}`,
        views: 0,
        likes: 0,
        duration: '0:00', // TODO: Calculate actual duration
        ipfsHash,
        createdAt: new Date(),
        categories: metadata.categories,
        tags: metadata.tags,
        description: metadata.description
      }

      this.mockVideos.unshift(newVideo)
      return newVideo
    } catch (error) {
      console.error('Upload failed:', error)
      return null
    }
  }

  private async uploadToIPFS(file: File): Promise<string> {
    if (!this.heliaInstance) {
      // Simulate upload for now
      return `Qm${Math.random().toString(36).substring(7)}`
    }

    try {
      // Convert file to Uint8Array
      const arrayBuffer = await file.arrayBuffer()
      const fileData = new Uint8Array(arrayBuffer)
      
      // Upload to IPFS using Helia
      const cid = await this.heliaInstance.fs.addBytes(fileData)
      return cid.toString()
    } catch (error) {
      console.error('IPFS upload failed:', error)
      // Fallback to mock hash
      return `Qm${Math.random().toString(36).substring(7)}`
    }
  }

  async getTrendingVideos(limit: number = 10): Promise<Video[]> {
    // Sort by views and recency
    const trending = [...this.mockVideos]
      .sort((a, b) => {
        const aScore = a.views + (Date.now() - a.createdAt.getTime()) / 1000000
        const bScore = b.views + (Date.now() - b.createdAt.getTime()) / 1000000
        return bScore - aScore
      })
      .slice(0, limit)

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))

    return trending
  }
}

export default ContentService