import { CID } from 'multiformats/cid'
import { getIPFSDataLayer } from './ipfs-data-layer'
import type { UserProfile } from '../../types'

export class UserProfileService {
  private static instance: UserProfileService
  private dataLayer = getIPFSDataLayer()

  static getInstance(): UserProfileService {
    if (!UserProfileService.instance) {
      UserProfileService.instance = new UserProfileService()
    }
    return UserProfileService.instance
  }

  async createUserProfile(profileData: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<CID> {
    try {
      // Generate profile with required fields
      const profile: UserProfile = {
        id: this.generateId(),
        ...profileData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      console.log('Creating user profile:', profile.email)
      
      // Store using IPFS data layer
      const cid = await this.dataLayer.createUserProfile(profile)
      console.log('User profile created with CID:', cid.toString())
      
      return cid
    } catch (error) {
      console.error('Failed to create user profile:', error)
      throw error
    }
  }

  async getUserProfile(userCID: CID): Promise<UserProfile | null> {
    try {
      console.log('Retrieving user profile:', userCID.toString())
      
      const profile = await this.dataLayer.getUserProfile(userCID)
      
      if (!profile) {
        console.warn('User profile not found:', userCID.toString())
        return null
      }

      // Validate profile structure
      if (!this.isValidProfile(profile)) {
        console.error('Invalid profile structure:', profile)
        return null
      }

      console.log('User profile retrieved successfully')
      return profile as UserProfile
    } catch (error) {
      console.error('Failed to retrieve user profile:', error)
      return null
    }
  }

  async updateUserProfile(userCID: CID, updates: Partial<UserProfile>): Promise<CID> {
    try {
      const existingProfile = await this.getUserProfile(userCID)
      if (!existingProfile) {
        throw new Error('User profile not found')
      }

      const updatedProfile: UserProfile = {
        ...existingProfile,
        ...updates,
        updatedAt: new Date().toISOString()
      }

      // Create new profile (IPFS is immutable)
      const newCID = await this.dataLayer.updateUserProfile(userCID, updates)
      console.log('User profile updated with new CID:', newCID.toString())
      
      return newCID
    } catch (error) {
      console.error('Failed to update user profile:', error)
      throw error
    }
  }

  async deleteUserProfile(userCID: CID): Promise<boolean> {
    try {
      // Note: IPFS data is immutable, so we can't actually delete it
      // In a real implementation, you might mark it as deleted or remove it from indexes
      console.log('Cannot delete IPFS data - marking as removed from local cache')
      
      // Remove from local cache if it exists
      const cacheKey = `ipfs_cache_${userCID.toString()}`
      localStorage.removeItem(cacheKey)
      
      return true
    } catch (error) {
      console.error('Failed to delete user profile:', error)
      return false
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  private isValidProfile(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.id === 'string' &&
      typeof data.username === 'string' &&
      typeof data.email === 'string' &&
      typeof data.status === 'boolean' &&
      Array.isArray(data.following) &&
      Array.isArray(data.followers) &&
      Array.isArray(data.liked) &&
      Array.isArray(data.disliked) &&
      Array.isArray(data.history)
    )
  }
}

export default UserProfileService
