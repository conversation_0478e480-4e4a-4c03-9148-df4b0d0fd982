import { C<PERSON> } from 'multiformats/cid'
import CryptoJ<PERSON> from 'crypto-js'
import { getHeliaInstance } from '../helia/client'
import type { 
  UserProfile, 
  ContentItem, 
  Channel, 
  Playlist, 
  AnonymousUser,
  IPFSData 
} from '@/types'

const ENCRYPT_PASS = 'upsocial' // Using same encryption key as backend

/**
 * Data Layer for UpSocial using Helia/IPFS
 * This replaces the OrbitDB functionality from the original backend
 */

// User Management Functions
export class UserManager {
  private static async encryptPassword(password: string): Promise<string> {
    return CryptoJS.AES.encrypt(password, ENCRYPT_PASS).toString()
  }

  private static async decryptPassword(encryptedPassword: string): Promise<string> {
    const bytes = CryptoJS.AES.decrypt(encryptedPassword, ENCRYPT_PASS)
    return bytes.toString(CryptoJS.enc.Utf8)
  }

  static async registerUser(userData: {
    username: string
    email: string
    password: string
  }): Promise<{ success: boolean; message: string; userCID?: CID }> {
    try {
      const { strings, ipns } = await getHeliaInstance()
      
      // Check if user exists by looking up existing users
      const existingUsers = await this.getAllUsers()
      const emailExists = existingUsers.some(user => user.email === userData.email)
      const usernameExists = existingUsers.some(user => user.username === userData.username)

      if (emailExists) {
        return { success: false, message: `${userData.email} is already registered!` }
      }

      if (usernameExists) {
        return { success: false, message: `${userData.username} is same!` }
      }

      const encryptedPassword = await this.encryptPassword(userData.password)
      
      const newUser: UserProfile = {
        id: existingUsers.length.toString(),
        username: userData.username,
        email: userData.email,
        passwordHash: encryptedPassword,
        status: true,
        handle: '',
        description: '',
        location: '',
        following: [],
        followers: [],
        liked: [],
        disliked: [],
        history: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const ipfsData: IPFSData<UserProfile> = {
        data: newUser,
        timestamp: new Date().toISOString(),
        version: 1
      }

      const userCID = await strings.add(JSON.stringify(ipfsData))
      
      // Store user reference in a global users index
      await this.addUserToIndex(userData.email, userCID)

      return { 
        success: true, 
        message: `${userData.email} is registered success!`, 
        userCID 
      }
    } catch (error) {
      console.error('Registration error:', error)
      return { success: false, message: 'Registration failed' }
    }
  }

  static async loginUser(credentials: {
    email: string
    password: string
  }): Promise<{ success: boolean; message: string; userData?: UserProfile }> {
    try {
      const users = await this.getAllUsers()
      const user = users.find(u => u.email === credentials.email && u.status)

      if (!user || !user.passwordHash) {
        return { success: false, message: 'Auth failed!' }
      }

      const decryptedPassword = await this.decryptPassword(user.passwordHash)
      
      if (decryptedPassword !== credentials.password) {
        return { success: false, message: 'Auth failed!' }
      }

      return {
        success: true,
        message: 'Auth success!',
        userData: user
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: 'Login failed' }
    }
  }

  static async getUserByEmail(email: string): Promise<UserProfile | null> {
    try {
      const users = await this.getAllUsers()
      return users.find(user => user.email === email) || null
    } catch (error) {
      console.error('Get user error:', error)
      return null
    }
  }

  static async getAllUsers(): Promise<UserProfile[]> {
    try {
      const { strings } = await getHeliaInstance()
      
      // Get users index (this would be stored at a known CID)
      const usersIndexCID = await this.getUsersIndexCID()
      if (!usersIndexCID) return []

      const usersIndexString = await strings.get(usersIndexCID)
      const usersIndex = JSON.parse(usersIndexString)
      const users: UserProfile[] = []

      for (const userCID of usersIndex.userCIDs || []) {
        try {
          const userDataString = await strings.get(userCID)
          const userData = JSON.parse(userDataString)
          if (userData.data) {
            users.push(userData.data)
          }
        } catch (error) {
          console.error('Error loading user:', error)
        }
      }

      return users
    } catch (error) {
      console.error('Get all users error:', error)
      return []
    }
  }

  static async updateUser(userEmail: string, updates: Partial<UserProfile>): Promise<boolean> {
    try {
      const { strings } = await getHeliaInstance()
      const user = await this.getUserByEmail(userEmail)
      
      if (!user) return false

      const updatedUser: UserProfile = {
        ...user,
        ...updates,
        updatedAt: new Date().toISOString()
      }

      const ipfsData: IPFSData<UserProfile> = {
        data: updatedUser,
        timestamp: new Date().toISOString(),
        version: 1
      }

      const newUserCID = await strings.add(JSON.stringify(ipfsData))
      await this.updateUserInIndex(userEmail, newUserCID)

      return true
    } catch (error) {
      console.error('Update user error:', error)
      return false
    }
  }

  private static async getUsersIndexCID(): Promise<CID | null> {
    // This would typically be stored in IPNS for mutability
    // For now, we'll store in localStorage as a fallback
    const stored = typeof window !== 'undefined' ? localStorage.getItem('usersIndexCID') : null
    return stored ? CID.parse(stored) : null
  }

  private static async addUserToIndex(email: string, userCID: CID): Promise<void> {
    // Add user to the global users index
    // This is a simplified implementation
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('usersIndex')
      const index = stored ? JSON.parse(stored) : { userCIDs: [], emailMap: {} }
      index.userCIDs.push(userCID.toString())
      index.emailMap[email] = userCID.toString()
      localStorage.setItem('usersIndex', JSON.stringify(index))
    }
  }

  private static async updateUserInIndex(email: string, newUserCID: CID): Promise<void> {
    // Update user reference in the global users index
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('usersIndex')
      if (stored) {
        const index = JSON.parse(stored)
        index.emailMap[email] = newUserCID.toString()
        localStorage.setItem('usersIndex', JSON.stringify(index))
      }
    }
  }
}

// Content Management Functions
export class ContentManager {
  static async uploadContent(contentData: {
    email: string
    title: string
    description: string
    keywords: string[]
    category: number[]
    contentCID: CID
    thumbnailCID?: CID
    channelName?: string
  }): Promise<{ success: boolean; message: string; contentCID?: CID }> {
    try {
      const { strings } = await getHeliaInstance()
      
      const allContent = await this.getAllContent()
      const contentId = allContent.length

      const newContent: ContentItem = {
        id: contentId.toString(),
        authorCID: CID.parse(''), // Will be set based on user
        title: contentData.title,
        description: contentData.description,
        keywords: contentData.keywords,
        category: contentData.category,
        contentCID: contentData.contentCID,
        thumbnailCID: contentData.thumbnailCID,
        status: true,
        liked: 0,
        disliked: 0,
        watched: 0,
        shared: 0,
        postDate: new Date().toISOString(),
        comments: [],
        channelName: contentData.channelName || 'Personal Profile',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      const ipfsData: IPFSData<ContentItem> = {
        data: newContent,
        timestamp: new Date().toISOString(),
        version: 1
      }

      const contentItemCID = await strings.add(JSON.stringify(ipfsData))
      await this.addContentToIndex(contentItemCID)

      return {
        success: true,
        message: 'uploaded success',
        contentCID: contentItemCID
      }
    } catch (error) {
      console.error('Upload content error:', error)
      return { success: false, message: 'Upload failed' }
    }
  }

  static async getAllContent(limit?: number): Promise<ContentItem[]> {
    try {
      const { strings } = await getHeliaInstance()
      
      // Get content index
      const contentIndex = await this.getContentIndex()
      const content: ContentItem[] = []

      const contentCIDs = limit ? contentIndex.slice(0, limit) : contentIndex

      for (const contentCID of contentCIDs) {
        try {
          const contentDataString = await strings.get(CID.parse(contentCID))
          const contentData = JSON.parse(contentDataString)
          if (contentData.data) {
            content.push(contentData.data)
          }
        } catch (error) {
          console.error('Error loading content:', error)
        }
      }

      // Sort by post date (newest first)
      return content.sort((a, b) => new Date(b.postDate).getTime() - new Date(a.postDate).getTime())
    } catch (error) {
      console.error('Get all content error:', error)
      return []
    }
  }

  static async getContentByCategory(categoryName: string): Promise<ContentItem[]> {
    try {
      const allContent = await this.getAllContent()
      const categoryId = this.getCategoryIdByName(categoryName)
      
      if (!categoryId) return []

      return allContent.filter(content => content.category.includes(categoryId))
    } catch (error) {
      console.error('Get content by category error:', error)
      return []
    }
  }

  static async likeContent(contentId: string, userEmail: string): Promise<boolean> {
    try {
      // Implementation for liking content
      // This would update both the content and user's liked list
      return true
    } catch (error) {
      console.error('Like content error:', error)
      return false
    }
  }

  private static async getContentIndex(): Promise<string[]> {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('contentIndex')
      return stored ? JSON.parse(stored) : []
    }
    return []
  }

  private static async addContentToIndex(contentCID: CID): Promise<void> {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('contentIndex')
      const index = stored ? JSON.parse(stored) : []
      index.unshift(contentCID.toString()) // Add to beginning for newest first
      localStorage.setItem('contentIndex', JSON.stringify(index))
    }
  }

  private static getCategoryIdByName(categoryName: string): number | null {
    const categories = [
      { id: 1, name: 'Animation' },
      { id: 2, name: 'Autos & Vehicles' },
      { id: 3, name: 'Beauty & Fashion' },
      { id: 4, name: 'Comedy' },
      { id: 5, name: 'Cooking & Food' },
      { id: 6, name: 'DIY & Crafts' },
      { id: 7, name: 'Documentary' },
      { id: 8, name: 'Education' },
      { id: 9, name: 'Entertainment' },
      { id: 10, name: 'Film & Animation' },
      { id: 11, name: 'Gaming' },
      { id: 12, name: 'Health & Fitness' },
      { id: 13, name: 'How-to & Style' },
      { id: 14, name: 'Kids & Family' },
      { id: 15, name: 'Music' },
      { id: 16, name: 'News & Politics' },
      { id: 17, name: 'Nonprofits & Activism' },
      { id: 18, name: 'People & Blogs' },
      { id: 19, name: 'Pets & Animals' },
      { id: 20, name: 'Science & Technology' },
      { id: 21, name: 'Sports' },
      { id: 22, name: 'Travel & Events' },
      { id: 23, name: 'Unboxing & Reviews' },
      { id: 24, name: 'Blogs' }
    ]
    
    const category = categories.find(c => c.name === categoryName)
    return category ? category.id : null
  }
}

// File Upload Functions
export class FileManager {
  static async uploadFile(file: File): Promise<{ success: boolean; cid?: CID; error?: string }> {
    try {
      const { fs } = await getHeliaInstance()
      
      const arrayBuffer = await file.arrayBuffer()
      const uint8Array = new Uint8Array(arrayBuffer)
      
      const cid = await fs.addFile({
        path: file.name,
        content: uint8Array
      })

      return { success: true, cid }
    } catch (error) {
      console.error('File upload error:', error)
      return { success: false, error: 'File upload failed' }
    }
  }

  static async getFile(cid: CID): Promise<Uint8Array | null> {
    try {
      const { fs } = await getHeliaInstance()
      
      const chunks: Uint8Array[] = []
      for await (const chunk of fs.cat(cid)) {
        chunks.push(chunk)
      }
      
      // Combine chunks
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0)
      const result = new Uint8Array(totalLength)
      let offset = 0
      for (const chunk of chunks) {
        result.set(chunk, offset)
        offset += chunk.length
      }
      
      return result
    } catch (error) {
      console.error('Get file error:', error)
      return null
    }
  }

  static getIPFSUrl(cid: CID): string {
    return `https://ipfs.io/ipfs/${cid.toString()}`
  }
}

// Anonymous User Functions
export class AnonymousManager {
  static async getHashCode(nickName: string): Promise<{ success: boolean; code?: string; message: string }> {
    try {
      const anonymousUsers = await this.getAllAnonymousUsers()
      
      // Check if nick name already exists
      const existingUser = anonymousUsers.find(user => user.nickName === nickName)
      if (existingUser) {
        return {
          success: true,
          code: existingUser.code,
          message: `This is your code: ${existingUser.code}`
        }
      }

      // Generate new hash code (20 random hex chars like the backend)
      const hashCode = Array.from({ length: 20 }, () => 
        Math.floor(Math.random() * 16).toString(16)
      ).join('')

      const newAnonymousUser: AnonymousUser = {
        id: anonymousUsers.length.toString(),
        nickName,
        code: hashCode,
        createdAt: new Date().toISOString()
      }

      await this.addAnonymousUser(newAnonymousUser)

      return {
        success: true,
        code: hashCode,
        message: `This is your code: ${hashCode}`
      }
    } catch (error) {
      console.error('Get hash code error:', error)
      return { success: false, message: 'Failed to generate code' }
    }
  }

  static async verifyHashCode(nickName: string, code: string): Promise<{ success: boolean; message: string }> {
    try {
      const anonymousUsers = await this.getAllAnonymousUsers()
      const user = anonymousUsers.find(u => u.nickName === nickName && u.code === code)
      
      if (user) {
        return { success: true, message: 'Success!' }
      } else {
        return { success: false, message: 'Failed! Get hash Code first' }
      }
    } catch (error) {
      console.error('Verify hash code error:', error)
      return { success: false, message: 'Verification failed' }
    }
  }

  static async getNameFromHashCode(code: string): Promise<{ success: boolean; name?: string; message: string }> {
    try {
      const anonymousUsers = await this.getAllAnonymousUsers()
      const user = anonymousUsers.find(u => u.code === code)
      
      if (user) {
        return { success: true, name: user.nickName, message: 'Success!' }
      } else {
        return { success: false, message: 'Failed! Get hash Code first' }
      }
    } catch (error) {
      console.error('Get name from hash code error:', error)
      return { success: false, message: 'Failed to get name' }
    }
  }

  private static async getAllAnonymousUsers(): Promise<AnonymousUser[]> {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('anonymousUsers')
      return stored ? JSON.parse(stored) : []
    }
    return []
  }

  private static async addAnonymousUser(user: AnonymousUser): Promise<void> {
    if (typeof window !== 'undefined') {
      const users = await this.getAllAnonymousUsers()
      users.push(user)
      localStorage.setItem('anonymousUsers', JSON.stringify(users))
    }
  }
}
