export interface Video {
  id: string
  title: string
  creator: string
  thumbnail: string
  videoUrl: string
  views: number
  likes: number
  duration: string
  ipfsHash?: string
  createdAt: Date | string | number // Allow multiple date formats for flexibility
  categories: string[]
  tags?: string[]
  description?: string
  
  // Additional metadata
  metadata?: {
    resolution?: string
    bitrate?: number
    codec?: string
    fps?: number
    size?: number
  }
  
  // Interaction data
  interactions?: {
    liked?: boolean
    shared?: boolean
    saved?: boolean
    reported?: boolean
  }
  
  // Creator info
  creatorInfo?: {
    avatar?: string
    verified?: boolean
    followers?: number
    bio?: string
  }
}

export interface VideoSearchFilters {
  query?: string
  category?: string
  duration?: 'short' | 'medium' | 'long'
  uploadDate?: 'hour' | 'day' | 'week' | 'month' | 'year'
  sortBy?: 'newest' | 'oldest' | 'popular' | 'trending' | 'relevance' | 'date' | 'views' | 'rating'
  minViews?: number
  maxViews?: number
}

export interface VideoStats {
  views: number
  likes: number
  shares: number
  comments: number
  saves: number
}

export interface VideoPlaylist {
  id: string
  name: string
  description?: string
  videos: Video[]
  createdAt: Date | string | number
  updatedAt: Date | string | number
  isPublic: boolean
  creator: string
}
