import type { CID } from 'multiformats/cid'

// User Profile Types
export interface UserProfile {
  id: string
  username: string
  email: string
  passwordHash?: string // Optional for wallet-based auth
  status: boolean
  handle: string
  description: string
  location: string
  photo?: CID | string
  following: CID[] // References to other user profiles
  followers: CID[] // References to other user profiles
  liked: CID[] // References to content
  disliked: CID[] // References to content
  history: ContentHistory[]
  createdAt: string
  updatedAt: string
}

// Content Types
export interface ContentItem {
  id: string
  authorCID: CID // Reference to user profile
  title: string
  description: string
  keywords: string[]
  category: number[]
  contentCID: CID // Reference to actual content file
  thumbnailCID?: CID // Reference to thumbnail
  status: boolean
  liked: number
  disliked: number
  watched: number
  shared: number
  postDate: string
  comments: Comment[]
  channelName?: string
  createdAt: string
  updatedAt: string
}

// Channel Types
export interface Channel {
  id: string
  channelName: string
  ownerCID: CID // Reference to user profile
  handleUrl: string
  aboutChannel: string
  tags: string[]
  location: string
  url: string
  photoCID?: CID
  followers: CID[] // References to user profiles
  contentCIDs: CID[] // References to content items
  createdAt: string
  updatedAt: string
}

// Playlist Types
export interface Playlist {
  id: string
  name: string
  description: string
  ownerCID: CID // Reference to user profile
  photoCID?: CID
  contentCIDs: CID[] // References to content items
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

// Comment Types
export interface Comment {
  id: string
  authorCID: CID // Reference to user profile
  content: string
  timestamp: string
  replies?: Comment[]
}

// History Types
export interface ContentHistory {
  contentCID: CID
  viewDate: string
  watchTime?: number
}

// Anonymous User Types
export interface AnonymousUser {
  id: string
  nickName: string
  code: string
  createdAt: string
}

// Category Types
export interface Category {
  id: number
  name: string
}

// File Upload Types
export interface UploadedFile {
  filename: string
  cid: CID
  size: number
  mimeType: string
  uploadedAt: string
}

// Authentication Types
export interface AuthState {
  isAuthenticated: boolean
  userCID?: CID
  profile?: UserProfile
  privateKey?: Uint8Array
}

// IPFS Storage Types
export interface IPFSData<T = unknown> {
  data: T
  timestamp: string
  signature?: string
  version: number
}

// Network Types
export interface PeerInfo {
  peerId: string
  addresses: string[]
  protocols: string[]
}

// Application State Types
export interface AppState {
  auth: AuthState
  peers: PeerInfo[]
  isOnline: boolean
  syncStatus: 'syncing' | 'synced' | 'error'
}

// Content Categories (matching original)
export const CONTENT_CATEGORIES: Category[] = [
  { id: 1, name: 'Animation' },
  { id: 2, name: 'Autos & Vehicles' },
  { id: 3, name: 'Beauty & Fashion' },
  { id: 4, name: 'Comedy' },
  { id: 5, name: 'Cooking & Food' },
  { id: 6, name: 'DIY & Crafts' },
  { id: 7, name: 'Documentary' },
  { id: 8, name: 'Education' },
  { id: 9, name: 'Entertainment' },
  { id: 10, name: 'Film & Animation' },
  { id: 11, name: 'Gaming' },
  { id: 12, name: 'Health & Fitness' },
  { id: 13, name: 'How-to & Style' },
  { id: 14, name: 'Kids & Family' },
  { id: 15, name: 'Music' },
  { id: 16, name: 'News & Politics' },
  { id: 17, name: 'Nonprofits & Activism' },
  { id: 18, name: 'People & Blogs' },
  { id: 19, name: 'Pets & Animals' },
  { id: 20, name: 'Science & Technology' },
  { id: 21, name: 'Sports' },
  { id: 22, name: 'Travel & Events' },
  { id: 23, name: 'Unboxing & Reviews' },
  { id: 24, name: 'Blogs' }
]
