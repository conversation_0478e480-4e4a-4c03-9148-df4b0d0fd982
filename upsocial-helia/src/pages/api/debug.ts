import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check client-side environment variables (NEXT_PUBLIC_*)
    const clientVars = {
      NEXT_PUBLIC_APILLON_API_KEY: !!process.env.NEXT_PUBLIC_APILLON_API_KEY,
      NEXT_PUBLIC_APILLON_API_SECRET: !!process.env.NEXT_PUBLIC_APILLON_API_SECRET,
      NEXT_PUBLIC_APILLON_BUCKET_UUID: !!process.env.NEXT_PUBLIC_APILLON_BUCKET_UUID,
      NEXT_PUBLIC_APILLON_IPNS_UUID: !!process.env.NEXT_PUBLIC_APILLON_IPNS_UUID,
    };

    // Get all environment variables that contain <PERSON>LLON
    const allApillonVars = Object.keys(process.env)
      .filter(key => key.includes('APILLON'))
      .reduce((acc, key) => {
        // Only show that the key exists, not the value
        acc[key] = !!process.env[key];
        return acc;
      }, {} as Record<string, boolean>);

    const response = {
      timestamp: new Date().toISOString(),
      message: 'Environment variables debug info',
      hasClientVars: Object.values(clientVars).some(Boolean),
      clientVars,
      allApillonVarsCount: Object.keys(allApillonVars).length,
      allApillonVars,
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
    };
    
    return NextResponse.json(response);
  } catch (error) {
    return NextResponse.json({ 
      error: 'Failed to check environment variables',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
