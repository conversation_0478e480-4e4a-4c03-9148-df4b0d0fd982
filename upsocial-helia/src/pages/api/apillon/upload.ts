import { NextApiRequest, NextApiResponse } from 'next'
import { Storage } from '@apillon/sdk'
import formidable from 'formidable'
import fs from 'fs'

// Disable body parser for file upload
export const config = {
  api: {
    bodyParser: false,
  },
}

interface ApillonUploadResponse {
  success: boolean
  cid?: string
  uuid?: string
  url?: string
  error?: string
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApillonUploadResponse>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', 'POST')
    return res.status(405).json({ success: false, error: 'Method not allowed' })
  }

  try {
    // Initialize Apillon Storage
    const storage = new Storage({
      key: process.env.APILLON_API_KEY!,
      secret: process.env.APILLON_SECRET!
    })

    const bucketUuid = process.env.APILLON_BUCKET_UUID
    if (!bucketUuid) {
      return res.status(500).json({ 
        success: false, 
        error: 'Apillon bucket UUID not configured' 
      })
    }

    const bucket = storage.bucket(bucketUuid)

    // Parse form data
    const form = formidable({
      uploadDir: '/tmp',
      keepExtensions: true,
      maxFileSize: 500 * 1024 * 1024, // 500MB limit
    })

    const [fields, files] = await form.parse(req)
    
    const file = Array.isArray(files.file) ? files.file[0] : files.file
    const cid = Array.isArray(fields.cid) ? fields.cid[0] : fields.cid

    if (!file) {
      return res.status(400).json({ 
        success: false, 
        error: 'No file provided' 
      })
    }

    if (!cid) {
      return res.status(400).json({ 
        success: false, 
        error: 'No CID provided' 
      })
    }

    console.log(`📌 Server-side Apillon upload for CID: ${cid}`)

    // Create FileMetadata object for Apillon
    const fileBuffer = fs.readFileSync(file.filepath)
    const fileMetadata = {
      fileName: file.originalFilename || 'upload',
      content: fileBuffer
    }

    // Upload to Apillon using uploadFiles method
    const uploadResults = await bucket.uploadFiles([fileMetadata])

    // Clean up temporary file
    fs.unlinkSync(file.filepath)

    if (uploadResults && uploadResults.length > 0) {
      const uploadResult = uploadResults[0]
      const apillonUrl = `https://ipfs.apillon.io/ipfs/${cid}`
      
      console.log(`✅ Server-side Apillon upload successful: ${apillonUrl}`)
      
      return res.status(200).json({
        success: true,
        cid,
        uuid: uploadResult.fileUuid || 'unknown',
        url: apillonUrl
      })
    } else {
      return res.status(500).json({
        success: false,
        error: 'Upload failed - no results returned'
      })
    }

  } catch (error) {
    console.error('❌ Server-side Apillon upload failed:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
