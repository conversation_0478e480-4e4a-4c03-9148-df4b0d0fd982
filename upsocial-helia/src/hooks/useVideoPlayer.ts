'use client'

import { useState, useRef, useCallback, useEffect } from 'react'

interface VideoState {
  isPlaying: boolean
  isMuted: boolean
  currentTime: number
  duration: number
  isLoading: boolean
  error: string | null
  volume: number
  playbackRate: number
}

interface UseVideoPlayerReturn extends VideoState {
  videoRef: React.RefObject<HTMLVideoElement>
  play: () => Promise<void>
  pause: () => void
  togglePlay: () => Promise<void>
  seek: (time: number) => void
  setVolume: (volume: number) => void
  toggleMute: () => void
  setPlaybackRate: (rate: number) => void
  reset: () => void
}

const initialState: VideoState = {
  isPlaying: false,
  isMuted: true,
  currentTime: 0,
  duration: 0,
  isLoading: false,
  error: null,
  volume: 1,
  playbackRate: 1
}

export function useVideoPlayer(autoPlay: boolean = false): UseVideoPlayerReturn {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [state, setState] = useState<VideoState>(initialState)
  
  const updateState = useCallback((updates: Partial<VideoState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  const play = useCallback(async () => {
    const video = videoRef.current
    if (!video) return

    try {
      await video.play()
      updateState({ isPlaying: true, error: null })
    } catch (error) {
      console.error('Play failed:', error)
      updateState({ 
        error: 'Failed to play video',
        isPlaying: false 
      })
    }
  }, [updateState])

  const pause = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    video.pause()
    updateState({ isPlaying: false })
  }, [updateState])

  const togglePlay = useCallback(async () => {
    if (state.isPlaying) {
      pause()
    } else {
      await play()
    }
  }, [state.isPlaying, play, pause])

  const seek = useCallback((time: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(0, Math.min(time, state.duration))
    updateState({ currentTime: video.currentTime })
  }, [state.duration, updateState])

  const setVolume = useCallback((volume: number) => {
    const video = videoRef.current
    if (!video) return

    const clampedVolume = Math.max(0, Math.min(1, volume))
    video.volume = clampedVolume
    updateState({ 
      volume: clampedVolume,
      isMuted: clampedVolume === 0
    })
  }, [updateState])

  const toggleMute = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    const newMutedState = !state.isMuted
    video.muted = newMutedState
    updateState({ isMuted: newMutedState })
  }, [state.isMuted, updateState])

  const setPlaybackRate = useCallback((rate: number) => {
    const video = videoRef.current
    if (!video) return

    const clampedRate = Math.max(0.25, Math.min(2, rate))
    video.playbackRate = clampedRate
    updateState({ playbackRate: clampedRate })
  }, [updateState])

  const reset = useCallback(() => {
    const video = videoRef.current
    if (video) {
      video.currentTime = 0
      video.pause()
    }
    setState(initialState)
  }, [])

  // Set up event listeners
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadStart = () => updateState({ isLoading: true })
    
    const handleLoadedData = () => {
      updateState({
        isLoading: false,
        duration: video.duration,
        error: null
      })
      
      if (autoPlay) {
        play().catch(console.error)
      }
    }
    
    const handleTimeUpdate = () => {
      updateState({ currentTime: video.currentTime })
    }
    
    const handlePlay = () => updateState({ isPlaying: true })
    const handlePause = () => updateState({ isPlaying: false })
    
    const handleVolumeChange = () => {
      updateState({
        volume: video.volume,
        isMuted: video.muted
      })
    }
    
    const handleRateChange = () => {
      updateState({ playbackRate: video.playbackRate })
    }
    
    const handleError = () => {
      updateState({
        error: 'Failed to load video',
        isLoading: false,
        isPlaying: false
      })
    }
    
    const handleEnded = () => {
      updateState({ isPlaying: false, currentTime: 0 })
    }

    // Add event listeners
    video.addEventListener('loadstart', handleLoadStart)
    video.addEventListener('loadeddata', handleLoadedData)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('volumechange', handleVolumeChange)
    video.addEventListener('ratechange', handleRateChange)
    video.addEventListener('error', handleError)
    video.addEventListener('ended', handleEnded)

    return () => {
      video.removeEventListener('loadstart', handleLoadStart)
      video.removeEventListener('loadeddata', handleLoadedData)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('volumechange', handleVolumeChange)
      video.removeEventListener('ratechange', handleRateChange)
      video.removeEventListener('error', handleError)
      video.removeEventListener('ended', handleEnded)
    }
  }, [autoPlay, play, updateState])

  return {
    ...state,
    videoRef,
    play,
    pause,
    togglePlay,
    seek,
    setVolume,
    toggleMute,
    setPlaybackRate,
    reset
  }
}