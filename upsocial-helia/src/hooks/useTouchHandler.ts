import { useRef, useCallback } from 'react'

// Enhanced touch handler for better mobile experience
export const useTouchHandler = (onSwipeUp: () => void, onSwipeDown: () => void) => {
  const touchStartY = useRef<number>(0)
  const touchEndY = useRef<number>(0)
  const touchStartTime = useRef<number>(0)
  const isDragging = useRef<boolean>(false)
  const initialTouchY = useRef<number>(0)
  
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    touchStartY.current = e.touches[0].clientY
    initialTouchY.current = e.touches[0].clientY
    touchStartTime.current = Date.now()
    isDragging.current = false
  }, [])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    const currentY = e.touches[0].clientY
    const deltaY = Math.abs(currentY - initialTouchY.current)
    
    // Only prevent default if we're clearly swiping vertically and moved significantly
    if (deltaY > 20) {
      // Only try to prevent default if the event is not passive
      try {
        e.preventDefault()
      } catch (error) {
        // Ignore error from passive event listener
      }
      isDragging.current = true
    }
  }, [])

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!isDragging.current) return
    
    touchEndY.current = e.changedTouches[0].clientY
    const swipeDistance = touchStartY.current - touchEndY.current
    const swipeTime = Date.now() - touchStartTime.current
    const swipeVelocity = Math.abs(swipeDistance) / swipeTime
    
    // Minimum distance and velocity for a valid swipe
    const minDistance = 40
    const minVelocity = 0.05
    
    if (Math.abs(swipeDistance) > minDistance && swipeVelocity > minVelocity) {
      if (swipeDistance > 0) {
        onSwipeUp()
      } else {
        onSwipeDown()
      }
    }
    
    // Reset state
    isDragging.current = false
  }, [onSwipeUp, onSwipeDown])

  return { handleTouchStart, handleTouchMove, handleTouchEnd }
}
