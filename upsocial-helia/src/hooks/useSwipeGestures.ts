'use client'

import { useRef, useCallback, useEffect } from 'react'

interface SwipeGestureOptions {
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  threshold?: number
  preventScroll?: boolean
}

interface SwipeState {
  startX: number
  startY: number
  currentX: number
  currentY: number
  isDragging: boolean
  startTime: number
}

export function useSwipeGestures({
  onSwipeUp,
  onSwipeDown,
  onSwipeLeft,
  onSwipeRight,
  threshold = 50,
  preventScroll = true
}: SwipeGestureOptions) {
  const elementRef = useRef<HTMLElement>(null)
  const swipeState = useRef<SwipeState>({
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    isDragging: false,
    startTime: 0
  })

  const handleStart = useCallback((clientX: number, clientY: number) => {
    swipeState.current = {
      startX: clientX,
      startY: clientY,
      currentX: clientX,
      currentY: clientY,
      isDragging: true,
      startTime: Date.now()
    }
  }, [])

  const handleMove = useCallback((clientX: number, clientY: number) => {
    if (!swipeState.current.isDragging) return

    swipeState.current.currentX = clientX
    swipeState.current.currentY = clientY
  }, [])

  const handleEnd = useCallback(() => {
    if (!swipeState.current.isDragging) return

    const { startX, startY, currentX, currentY, startTime } = swipeState.current
    const deltaX = currentX - startX
    const deltaY = currentY - startY
    const deltaTime = Date.now() - startTime
    
    // Reset dragging state
    swipeState.current.isDragging = false

    // Check if the swipe was fast enough (within 300ms) and far enough
    if (deltaTime > 300) return

    const absX = Math.abs(deltaX)
    const absY = Math.abs(deltaY)

    // Determine if it's a horizontal or vertical swipe
    if (absX > absY) {
      // Horizontal swipe
      if (absX > threshold) {
        if (deltaX > 0) {
          onSwipeRight?.()
        } else {
          onSwipeLeft?.()
        }
      }
    } else {
      // Vertical swipe
      if (absY > threshold) {
        if (deltaY > 0) {
          onSwipeDown?.()
        } else {
          onSwipeUp?.()
        }
      }
    }
  }, [threshold, onSwipeUp, onSwipeDown, onSwipeLeft, onSwipeRight])

  // Touch event handlers
  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (preventScroll) {
      e.preventDefault()
    }
    const touch = e.touches[0]
    handleStart(touch.clientX, touch.clientY)
  }, [handleStart, preventScroll])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (preventScroll) {
      e.preventDefault()
    }
    const touch = e.touches[0]
    handleMove(touch.clientX, touch.clientY)
  }, [handleMove, preventScroll])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (preventScroll) {
      e.preventDefault()
    }
    handleEnd()
  }, [handleEnd, preventScroll])

  // Mouse event handlers (for desktop)
  const handleMouseDown = useCallback((e: MouseEvent) => {
    handleStart(e.clientX, e.clientY)
  }, [handleStart])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    handleMove(e.clientX, e.clientY)
  }, [handleMove])

  const handleMouseUp = useCallback(() => {
    handleEnd()
  }, [handleEnd])

  // Set up event listeners
  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    // Touch events
    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: false })

    // Mouse events
    element.addEventListener('mousedown', handleMouseDown)
    
    // Add mouse move and up to document to handle cases where mouse leaves element
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    return () => {
      // Touch events
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)

      // Mouse events
      element.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, handleMouseDown, handleMouseMove, handleMouseUp])

  return {
    elementRef,
    isDragging: swipeState.current.isDragging
  }
}