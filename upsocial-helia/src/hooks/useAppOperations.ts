import { useState, useEffect, useCallback } from 'react'
import { CID } from 'multiformats/cid'
import { AuthManager } from '../lib/data/auth-manager'
import { IPFSDataLayer } from '../lib/data/ipfs-data-layer'
import { useAppStore } from '../lib/data/store'
import type { UserProfile, ContentItem, Channel, Playlist, AuthState } from '../types'

// Hook for authentication operations
export function useAuth() {
  const { auth, setAuth, clearAuth, setLoading, addNotification } = useAppStore()
  const [authManager] = useState(() => AuthManager.getInstance())

  const register = useCallback(async (username: string, email: string, password: string) => {
    try {
      setLoading(true)
      const { userCID, profile } = await authManager.registerWithPassword(username, email, password)
      const authState: AuthState = {
        isAuthenticated: true,
        userCID,
        profile
      }
      setAuth(authState)
      addNotification({ type: 'success', message: 'Registration successful!' })
      return true
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Registration failed' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [authManager, setAuth, setLoading, addNotification])

  const login = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true)
      const authState = await authManager.loginWithPassword(email, password)
      setAuth(authState)
      addNotification({ type: 'success', message: 'Login successful!' })
      return true
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Login failed' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [authManager, setAuth, setLoading, addNotification])

  const connectWallet = useCallback(async () => {
    try {
      setLoading(true)
      const authState = await authManager.connectWallet()
      setAuth(authState)
      addNotification({ type: 'success', message: 'Wallet connected!' })
      return true
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Wallet connection failed' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [authManager, setAuth, setLoading, addNotification])

  const createAnonymousSession = useCallback(async (nickname: string) => {
    try {
      setLoading(true)
      const { code, authState } = await authManager.createAnonymousSession(nickname)
      setAuth(authState)
      addNotification({ 
        type: 'success', 
        message: `Anonymous session created! Your code: ${code}` 
      })
      return code
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to create anonymous session' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [authManager, setAuth, setLoading, addNotification])

  const loginAnonymous = useCallback(async (nickname: string, code: string) => {
    try {
      setLoading(true)
      const authState = await authManager.loginAnonymous(nickname, code)
      setAuth(authState)
      addNotification({ type: 'success', message: 'Anonymous login successful!' })
      return true
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Anonymous login failed' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [authManager, setAuth, setLoading, addNotification])

  const logout = useCallback(async () => {
    await authManager.logout()
    clearAuth()
    addNotification({ type: 'info', message: 'Logged out successfully' })
  }, [authManager, clearAuth, addNotification])

  const loadStoredAuth = useCallback(async () => {
    try {
      const authState = await authManager.loadStoredAuth()
      if (authState) {
        setAuth(authState)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to load stored auth:', error)
      return false
    }
  }, [authManager, setAuth])

  return {
    auth,
    register,
    login,
    connectWallet,
    createAnonymousSession,
    loginAnonymous,
    logout,
    loadStoredAuth
  }
}

// Hook for content operations
export function useContent() {
  const { 
    feed, setFeed, addToFeed, updateFeedItem,
    myContent, setMyContent, addMyContent,
    auth, setLoading, addNotification 
  } = useAppStore()
  const [dataLayer] = useState(() => IPFSDataLayer.getInstance())

  const uploadContent = useCallback(async (
    title: string,
    description: string,
    keywords: string[],
    category: number[],
    file: File,
    thumbnail?: File
  ) => {
    if (!auth.isAuthenticated || !auth.userCID) {
      addNotification({ type: 'error', message: 'Please log in to upload content' })
      return null
    }

    try {
      setLoading(true)
      
      // Upload the main content file
      const contentCID = await dataLayer.storeFile(file)
      
      // Upload thumbnail if provided
      let thumbnailCID: CID | undefined
      if (thumbnail) {
        thumbnailCID = await dataLayer.storeFile(thumbnail)
      }

      // Create content metadata
      const content: Omit<ContentItem, 'id' | 'createdAt' | 'updatedAt'> = {
        authorCID: auth.userCID,
        title,
        description,
        keywords,
        category,
        contentCID,
        thumbnailCID,
        status: true,
        liked: 0,
        disliked: 0,
        watched: 0,
        shared: 0,
        postDate: new Date().toISOString(),
        comments: []
      }

      const contentMetadataCID = await dataLayer.createContent(content)
      const createdContent = await dataLayer.getContent(contentMetadataCID)
      
      if (createdContent) {
        addMyContent(createdContent)
        addToFeed(createdContent)
        addNotification({ type: 'success', message: 'Content uploaded successfully!' })
        return contentMetadataCID
      }

      throw new Error('Failed to create content metadata')
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Upload failed' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [auth, dataLayer, setLoading, addNotification, addMyContent, addToFeed])

  const likeContent = useCallback(async (contentCID: CID) => {
    if (!auth.isAuthenticated || !auth.userCID) {
      addNotification({ type: 'error', message: 'Please log in to like content' })
      return false
    }

    try {
      await dataLayer.likeContent(auth.userCID, contentCID)
      
      // Update the content's like count locally
      updateFeedItem(contentCID.toString(), { 
        liked: (feed.find(item => item.contentCID.toString() === contentCID.toString())?.liked || 0) + 1 
      })
      
      return true
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to like content' 
      })
      return false
    }
  }, [auth, dataLayer, addNotification, updateFeedItem, feed])

  const dislikeContent = useCallback(async (contentCID: CID) => {
    if (!auth.isAuthenticated || !auth.userCID) {
      addNotification({ type: 'error', message: 'Please log in to dislike content' })
      return false
    }

    try {
      await dataLayer.dislikeContent(auth.userCID, contentCID)
      
      // Update the content's dislike count locally
      updateFeedItem(contentCID.toString(), { 
        disliked: (feed.find(item => item.contentCID.toString() === contentCID.toString())?.disliked || 0) + 1 
      })
      
      return true
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to dislike content' 
      })
      return false
    }
  }, [auth, dataLayer, addNotification, updateFeedItem, feed])

  const loadMyContent = useCallback(async () => {
    if (!auth.isAuthenticated || !auth.userCID) return

    try {
      // This is a placeholder - in a real implementation, you'd maintain
      // an index of user's content or traverse IPFS links
      setLoading(true)
      // For now, just return empty array
      setMyContent([])
    } catch (error) {
      console.error('Failed to load user content:', error)
    } finally {
      setLoading(false)
    }
  }, [auth, setMyContent, setLoading])

  const loadFeed = useCallback(async () => {
    try {
      setLoading(true)
      // Placeholder for loading feed - would implement content discovery here
      // For now, just return empty array
      setFeed([])
    } catch (error) {
      console.error('Failed to load feed:', error)
    } finally {
      setLoading(false)
    }
  }, [setFeed, setLoading])

  return {
    feed,
    myContent,
    uploadContent,
    likeContent,
    dislikeContent,
    loadMyContent,
    loadFeed
  }
}

// Hook for channel operations
export function useChannels() {
  const { 
    channels, setChannels, addChannel, updateChannel,
    auth, setLoading, addNotification 
  } = useAppStore()
  const [dataLayer] = useState(() => IPFSDataLayer.getInstance())

  const createChannel = useCallback(async (
    channelName: string,
    handleUrl: string,
    aboutChannel: string,
    tags: string[],
    location: string,
    url: string,
    photo?: File
  ) => {
    if (!auth.isAuthenticated || !auth.userCID) {
      addNotification({ type: 'error', message: 'Please log in to create a channel' })
      return null
    }

    try {
      setLoading(true)
      
      let photoCID: CID | undefined
      if (photo) {
        photoCID = await dataLayer.storeFile(photo)
      }

      const channel: Omit<Channel, 'id' | 'createdAt' | 'updatedAt'> = {
        channelName,
        ownerCID: auth.userCID,
        handleUrl,
        aboutChannel,
        tags,
        location,
        url,
        photoCID,
        followers: [],
        contentCIDs: []
      }

      const channelCID = await dataLayer.createChannel(channel)
      const createdChannel = await dataLayer.getChannel(channelCID)
      
      if (createdChannel) {
        addChannel(createdChannel)
        addNotification({ type: 'success', message: 'Channel created successfully!' })
        return channelCID
      }

      throw new Error('Failed to create channel')
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to create channel' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [auth, dataLayer, setLoading, addNotification, addChannel])

  const loadUserChannels = useCallback(async () => {
    if (!auth.isAuthenticated || !auth.userCID) return

    try {
      setLoading(true)
      // Placeholder - would implement channel discovery here
      setChannels([])
    } catch (error) {
      console.error('Failed to load channels:', error)
    } finally {
      setLoading(false)
    }
  }, [auth, setChannels, setLoading])

  return {
    channels,
    createChannel,
    loadUserChannels
  }
}

// Hook for playlist operations
export function usePlaylists() {
  const { 
    playlists, setPlaylists, addPlaylist, updatePlaylist,
    auth, setLoading, addNotification 
  } = useAppStore()
  const [dataLayer] = useState(() => IPFSDataLayer.getInstance())

  const createPlaylist = useCallback(async (
    name: string,
    description: string,
    isPublic: boolean,
    photo?: File
  ) => {
    if (!auth.isAuthenticated || !auth.userCID) {
      addNotification({ type: 'error', message: 'Please log in to create a playlist' })
      return null
    }

    try {
      setLoading(true)
      
      let photoCID: CID | undefined
      if (photo) {
        photoCID = await dataLayer.storeFile(photo)
      }

      const playlist: Omit<Playlist, 'id' | 'createdAt' | 'updatedAt'> = {
        name,
        description,
        ownerCID: auth.userCID,
        photoCID,
        contentCIDs: [],
        isPublic
      }

      const playlistCID = await dataLayer.createPlaylist(playlist)
      const createdPlaylist = await dataLayer.getPlaylist(playlistCID)
      
      if (createdPlaylist) {
        addPlaylist(createdPlaylist)
        addNotification({ type: 'success', message: 'Playlist created successfully!' })
        return playlistCID
      }

      throw new Error('Failed to create playlist')
    } catch (error) {
      addNotification({ 
        type: 'error', 
        message: error instanceof Error ? error.message : 'Failed to create playlist' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [auth, dataLayer, setLoading, addNotification, addPlaylist])

  const loadUserPlaylists = useCallback(async () => {
    if (!auth.isAuthenticated || !auth.userCID) return

    try {
      setLoading(true)
      // Placeholder - would implement playlist discovery here
      setPlaylists([])
    } catch (error) {
      console.error('Failed to load playlists:', error)
    } finally {
      setLoading(false)
    }
  }, [auth, setPlaylists, setLoading])

  return {
    playlists,
    createPlaylist,
    loadUserPlaylists
  }
}
