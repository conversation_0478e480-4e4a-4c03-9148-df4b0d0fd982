'use client'
import { useState } from 'react'
import { heliaClient } from '../../lib/ipfs/helia-client'
import { videoStorage } from '../../lib/ipfs/video-storage'

interface TestResult {
  success: boolean
  message: string
  data?: any
  timestamp: string
}

export default function ApillonTestPage() {
  const [results, setResults] = useState<TestResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [testCID, setTestCID] = useState('')

  const addResult = (result: Omit<TestResult, 'timestamp'>) => {
    setResults(prev => [...prev, {
      ...result,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const clearResults = () => {
    setResults([])
  }

  const testHeliaWithApillon = async () => {
    setIsLoading(true)
    try {
      addResult({ success: true, message: '🚀 Testing Helia client with Apillon integration...' })
      
      // Initialize Helia with Apillon enabled
      const config = {
        enableApillonPinning: true,
        enableWebRTC: false,
        enableMDNS: true
      }
      
      const client = heliaClient
      await client.initialize()
      
      const stats = await client.getNodeStats()
      addResult({ 
        success: true, 
        message: `✅ Helia initialized successfully`, 
        data: {
          peerId: stats.peerId,
          connections: stats.connections,
          peers: stats.peers
        }
      })

      if (selectedFile) {
        addResult({ success: true, message: `📤 Uploading file: ${selectedFile.name}` })
        
        const uploadResult = await client.uploadFile(selectedFile)
        
        addResult({ 
          success: true, 
          message: `✅ File uploaded successfully`,
          data: {
            cid: uploadResult.cid,
            size: uploadResult.size,
            url: uploadResult.url,
            apillonPinned: uploadResult.apillonPinned,
            apillonUrl: uploadResult.apillonUrl
          }
        })

        if (uploadResult.apillonPinned) {
          addResult({ 
            success: true, 
            message: `🌟 File pinned to Apillon: ${uploadResult.apillonUrl}` 
          })
        }

        setTestCID(uploadResult.cid)

        // Test content availability
        setTimeout(async () => {
          try {
            const availability = await client.verifyContentAvailability(uploadResult.cid)
            const accessibleGateways = availability.gateways.filter(g => g.accessible)
            
            addResult({
              success: availability.networkRetrievable,
              message: `📊 Content available via ${accessibleGateways.length}/${availability.gateways.length} gateways`,
              data: {
                localAvailable: availability.localAvailable,
                networkRetrievable: availability.networkRetrievable,
                accessibleGateways: accessibleGateways.map(g => ({
                  gateway: new URL(g.url).hostname,
                  responseTime: g.responseTime
                }))
              }
            })
          } catch (error) {
            addResult({
              success: false,
              message: `❌ Failed to verify content availability: ${error}`
            })
          }
        }, 3000)
      }

    } catch (error) {
      addResult({
        success: false,
        message: `❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
    setIsLoading(false)
  }

  const testVideoUpload = async () => {
    if (!selectedFile) {
      addResult({ success: false, message: '❌ Please select a video file first' })
      return
    }

    setIsLoading(true)
    try {
      addResult({ success: true, message: '🎥 Testing video upload with Apillon...' })

      const progress = (progress: any) => {
        addResult({
          success: true,
          message: `📊 Upload progress: ${progress.percentage}% - ${progress.message}`
        })
      }

      const videoResult = await videoStorage.uploadVideo(selectedFile, progress)
      
      addResult({
        success: true,
        message: '✅ Video upload completed',
        data: {
          cid: videoResult.cid,
          size: videoResult.size,
          apillonPinned: videoResult.apillonPinned,
          apillonUrl: videoResult.apillonUrl
        }
      })

      setTestCID(videoResult.cid)

    } catch (error) {
      addResult({
        success: false,
        message: `❌ Video upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
    setIsLoading(false)
  }

  const testCIDRetrieval = async () => {
    if (!testCID) {
      addResult({ success: false, message: '❌ No CID available for testing' })
      return
    }

    setIsLoading(true)
    try {
      addResult({ success: true, message: `🔍 Testing retrieval of CID: ${testCID}` })

      // Test direct gateway access
      const gateways = [
        'https://ipfs.io/ipfs/',
        'https://cf-ipfs.com/ipfs/',
        'https://dweb.link/ipfs/',
        'https://ipfs.apillon.io/ipfs/',
        'https://gateway.pinata.cloud/ipfs/'
      ]

      for (const gateway of gateways) {
        try {
          const response = await fetch(gateway + testCID, {
            method: 'HEAD',
            signal: AbortSignal.timeout(5000)
          })
          
          addResult({
            success: response.ok,
            message: `${response.ok ? '✅' : '❌'} ${new URL(gateway).hostname}: ${response.status}`
          })
        } catch (error) {
          addResult({
            success: false,
            message: `❌ ${new URL(gateway).hostname}: Failed to connect`
          })
        }
      }

    } catch (error) {
      addResult({
        success: false,
        message: `❌ Retrieval test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
    setIsLoading(false)
  }

  const testApillonAPI = async () => {
    if (!selectedFile) {
      addResult({ success: false, message: '❌ Please select a file first' })
      return
    }

    setIsLoading(true)
    try {
      addResult({ success: true, message: '🔌 Testing Apillon API route...' })

      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('cid', 'test-cid-' + Date.now())

      const response = await fetch('/api/apillon/upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      addResult({
        success: result.success,
        message: result.success ? 
          `✅ Apillon API successful: ${result.url}` : 
          `❌ Apillon API failed: ${result.error}`,
        data: result
      })

    } catch (error) {
      addResult({
        success: false,
        message: `❌ Apillon API test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
    setIsLoading(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          🔬 Apillon IPFS Integration Test Suite
        </h1>

        {/* File Selection */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">📁 File Selection</h2>
          <input
            type="file"
            onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            accept="video/*,image/*,*/*"
          />
          {selectedFile && (
            <p className="mt-2 text-sm text-gray-600">
              Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          )}
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">🧪 Test Controls</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={testHeliaWithApillon}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              Test Helia + Apillon
            </button>
            <button
              onClick={testVideoUpload}
              disabled={isLoading || !selectedFile}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
            >
              Test Video Upload
            </button>
            <button
              onClick={testCIDRetrieval}
              disabled={isLoading || !testCID}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              Test CID Retrieval
            </button>
            <button
              onClick={testApillonAPI}
              disabled={isLoading || !selectedFile}
              className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 disabled:opacity-50"
            >
              Test Apillon API
            </button>
          </div>
          <div className="mt-4 flex gap-4">
            <button
              onClick={clearResults}
              disabled={isLoading}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:opacity-50"
            >
              Clear Results
            </button>
            {testCID && (
              <div className="flex-1">
                <label htmlFor="test-cid" className="sr-only">Test CID</label>
                <input
                  id="test-cid"
                  type="text"
                  value={testCID}
                  readOnly
                  className="w-full px-3 py-2 border rounded-lg bg-gray-50 text-sm"
                  placeholder="Test CID will appear here"
                  title="Generated test CID"
                />
              </div>
            )}
          </div>
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold mb-4">📊 Test Results</h2>
          {results.length === 0 ? (
            <p className="text-gray-500 italic">No test results yet. Run a test to see results here.</p>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border-l-4 ${
                    result.success 
                      ? 'bg-green-50 border-green-400' 
                      : 'bg-red-50 border-red-400'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <p className={`font-medium ${
                      result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {result.message}
                    </p>
                    <span className="text-xs text-gray-500 ml-4">
                      {result.timestamp}
                    </span>
                  </div>
                  {result.data && (
                    <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Loading Indicator */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 shadow-xl">
              <div className="flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="text-gray-700">Running test...</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
