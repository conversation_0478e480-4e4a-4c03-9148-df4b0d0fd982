'use client'

import React, { useState } from 'react'
import VideoUpload from '@/components/video/VideoUpload'
import IPFSVideoPlayer from '@/components/video/IPFSVideoPlayer'
import { VideoMetadata } from '@/lib/ipfs/video-storage'

interface UploadedVideo {
  metadata: VideoMetadata
  uploadedAt: Date
}

export default function VideoDemo() {
  const [uploadedVideos, setUploadedVideos] = useState<UploadedVideo[]>([])
  const [selectedVideo, setSelectedVideo] = useState<UploadedVideo | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const handleUploadComplete = (metadata: VideoMetadata) => {
    const newVideo: UploadedVideo = {
      metadata,
      uploadedAt: new Date()
    }
    
    setUploadedVideos(prev => [newVideo, ...prev])
    setSelectedVideo(newVideo)
    setError(null)
    setSuccess(`Video "${metadata.title}" uploaded successfully to IPFS!`)
    
    // Clear success message after 5 seconds
    setTimeout(() => setSuccess(null), 5000)
  }

  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage)
    setSuccess(null)
    
    // Clear error message after 10 seconds
    setTimeout(() => setError(null), 10000)
  }

  const handleVideoSelect = (video: UploadedVideo) => {
    setSelectedVideo(video)
    setError(null)
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🎬 Decentralized Video Platform
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Upload and stream videos directly on IPFS using Helia. 
            Your content is stored on the distributed web, ensuring censorship resistance and true decentralization.
          </p>
          <div className="mt-4 text-sm text-gray-500">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full mr-2">
              📡 IPFS Network
            </span>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full mr-2">
              🔒 P2P Storage
            </span>
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full">
              🌐 Helia v5.5.0
            </span>
          </div>
        </div>

        {/* Status Messages */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <div className="text-green-600 text-xl mr-3">✅</div>
              <div>
                <h3 className="text-green-800 font-medium">Upload Successful!</h3>
                <p className="text-green-700">{success}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <div className="text-red-600 text-xl mr-3">❌</div>
              <div>
                <h3 className="text-red-800 font-medium">Upload Error</h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-6">📤 Upload Video</h2>
            <VideoUpload
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              maxFileSize={500}
              acceptedFormats={['video/mp4', 'video/webm', 'video/ogg', 'video/mov', 'video/avi']}
            />
          </div>

          {/* Video Library */}
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-6">
              📚 Video Library ({uploadedVideos.length})
            </h2>
            
            {uploadedVideos.length === 0 ? (
              <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                <div className="text-4xl mb-4">📹</div>
                <h3 className="text-lg font-medium text-gray-600 mb-2">No videos uploaded yet</h3>
                <p className="text-gray-500">Upload your first video to get started!</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {uploadedVideos.map((video, index) => (
                  <div
                    key={index}
                    className={`bg-white rounded-lg shadow p-4 cursor-pointer transition-all border-2 ${
                      selectedVideo === video
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-transparent hover:border-gray-200 hover:shadow-md'
                    }`}
                    onClick={() => handleVideoSelect(video)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="text-2xl">🎬</div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 truncate">
                          {video.metadata.title}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                          {video.metadata.description || 'No description'}
                        </p>
                        
                        <div className="flex flex-wrap items-center gap-2 mt-2">
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                            {formatFileSize(video.metadata.size)}
                          </span>
                          <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                            {video.metadata.category}
                          </span>
                          <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded font-mono">
                            CID: {video.metadata.cid.slice(0, 8)}...
                          </span>
                        </div>
                        
                        {video.metadata.tags && video.metadata.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {video.metadata.tags.slice(0, 3).map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded"
                              >
                                #{tag}
                              </span>
                            ))}
                            {video.metadata.tags.length > 3 && (
                              <span className="text-xs text-gray-500">
                                +{video.metadata.tags.length - 3} more
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="text-xs text-gray-400 mt-2">
                          Uploaded: {formatDate(video.uploadedAt)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Video Player Section */}
        {selectedVideo && (
          <div className="mt-12">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">
                🎥 Now Playing: {selectedVideo.metadata.title}
              </h2>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="flex items-center">
                  📡 IPFS Network
                </span>
                <span className="flex items-center">
                  📊 {formatFileSize(selectedVideo.metadata.size)}
                </span>
                <a
                  href={`https://ipfs.io/ipfs/${selectedVideo.metadata.cid}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  View on IPFS Gateway →
                </a>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6">
              <IPFSVideoPlayer
                cid={selectedVideo.metadata.cid}
                metadata={selectedVideo.metadata}
                className="w-full h-64 md:h-96 lg:h-[500px]"
                onError={(error) => setError(`Video playback error: ${error}`)}
                onLoadStart={() => console.log('Video loading started')}
                onLoadEnd={() => console.log('Video loading completed')}
              />
              
              {/* Video Details */}
              <div className="mt-6 border-t pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Video Information</h3>
                    <dl className="space-y-2 text-sm">
                      <div>
                        <dt className="font-medium text-gray-600">Title:</dt>
                        <dd className="text-gray-900">{selectedVideo.metadata.title}</dd>
                      </div>
                      {selectedVideo.metadata.description && (
                        <div>
                          <dt className="font-medium text-gray-600">Description:</dt>
                          <dd className="text-gray-900">{selectedVideo.metadata.description}</dd>
                        </div>
                      )}
                      <div>
                        <dt className="font-medium text-gray-600">Category:</dt>
                        <dd className="text-gray-900">{selectedVideo.metadata.category}</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-600">Uploaded by:</dt>
                        <dd className="text-gray-900">{selectedVideo.metadata.uploadedBy}</dd>
                      </div>
                    </dl>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-800 mb-3">Technical Details</h3>
                    <dl className="space-y-2 text-sm">
                      <div>
                        <dt className="font-medium text-gray-600">IPFS CID:</dt>
                        <dd className="text-gray-900 font-mono text-xs break-all">
                          {selectedVideo.metadata.cid}
                        </dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-600">File Size:</dt>
                        <dd className="text-gray-900">{formatFileSize(selectedVideo.metadata.size)}</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-600">MIME Type:</dt>
                        <dd className="text-gray-900">{selectedVideo.metadata.mimeType}</dd>
                      </div>
                      <div>
                        <dt className="font-medium text-gray-600">Upload Date:</dt>
                        <dd className="text-gray-900">{formatDate(selectedVideo.uploadedAt)}</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-16 text-center text-gray-500">
          <p className="mb-2">
            Powered by{' '}
            <a href="https://helia.io" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
              Helia IPFS
            </a>
            {' '}and{' '}
            <a href="https://ipfs.tech" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
              IPFS Network
            </a>
          </p>
          <p className="text-sm">
            🌐 Truly decentralized • 🔒 Censorship resistant • 📡 P2P distributed
          </p>
        </div>
      </div>
    </div>
  )
}
