import { NextRequest, NextResponse } from 'next/server'

// List of public IPFS gateways to try
const IPFS_GATEWAYS = [
  'https://ipfs.io/ipfs/',
  'https://gateway.pinata.cloud/ipfs/',
  'https://cloudflare-ipfs.com/ipfs/',
  'https://dweb.link/ipfs/',
  'https://gateway.ipfs.io/ipfs/',
]

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const cid = searchParams.get('cid')
  
  if (!cid) {
    return NextResponse.json({ error: 'CID parameter is required' }, { status: 400 })
  }

  // Try each gateway until one works
  for (const gateway of IPFS_GATEWAYS) {
    try {
      console.log(`Trying gateway: ${gateway}${cid}`)
      
      const response = await fetch(`${gateway}${cid}`, {
        headers: {
          'Accept': 'application/json, text/plain, */*',
        },
        // Add timeout
        signal: AbortSignal.timeout(10000)
      })
      
      if (response.ok) {
        const data = await response.text()
        console.log(`Successfully retrieved from ${gateway}`)
        
        return new NextResponse(data, {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
          }
        })
      }
    } catch (error) {
      console.log(`Gateway ${gateway} failed:`, error)
      continue
    }
  }
  
  return NextResponse.json(
    { error: 'Failed to retrieve data from all IPFS gateways' }, 
    { status: 502 }
  )
}
