import { NextRequest, NextResponse } from 'next/server'
import { heliaClient } from '@/lib/ipfs/helia-client'

export async function GET() {
  try {
    console.log('🧪 Testing Helia IPFS client...')
    
    // Initialize Helia
    await heliaClient.initialize()
    console.log('✅ Helia initialized successfully')
    
    // Get node stats
    const stats = await heliaClient.getNodeStats()
    console.log('📊 Node stats:', stats)
    
    // Test JSON upload
    const testData = {
      message: 'Hello from Helia!',
      timestamp: new Date().toISOString(),
      test: true
    }
    
    const result = await heliaClient.uploadJSON(testData)
    console.log('📤 JSON uploaded:', result)
    
    // Test retrieval
    const retrieved = await heliaClient.getJSON(result.cid)
    console.log('📥 JSON retrieved:', retrieved)
    
    return NextResponse.json({
      success: true,
      message: 'Helia IPFS client working correctly',
      stats,
      uploadTest: {
        uploaded: testData,
        cid: result.cid,
        retrieved: retrieved,
        url: result.url
      }
    })
  } catch (error) {
    console.error('❌ Helia test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided'
      }, { status: 400 })
    }
    
    console.log('📁 Testing file upload:', file.name, 'Size:', file.size)
    
    // Initialize Helia
    await heliaClient.initialize()
    
    // Upload file
    const result = await heliaClient.uploadFile(file)
    console.log('📤 File uploaded:', result)
    
    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      result
    })
  } catch (error) {
    console.error('❌ File upload test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
