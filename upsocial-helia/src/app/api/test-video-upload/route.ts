import { NextRequest, NextResponse } from 'next/server'
import { videoStorage } from '@/lib/ipfs/video-storage'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    console.log('🎬 Testing video upload with local file...')
    
    // Initialize Helia
    await videoStorage.initialize()
    
    // Read the video file from local filesystem
    const videoPath = '/home/<USER>/Documents/code/upsocial/upsocial-helia/284568_small.mp4'
    
    if (!fs.existsSync(videoPath)) {
      throw new Error(`Video file not found at: ${videoPath}`)
    }
    
    console.log('📁 Reading video file from:', videoPath)
    const videoBuffer = fs.readFileSync(videoPath)
    const videoBlob = new Blob([videoBuffer], { type: 'video/mp4' })
    
    // Create a File-like object
    const videoFile = new File([videoBlob], '284568_small.mp4', { type: 'video/mp4' })
    
    console.log('📊 Video file stats:', {
      name: videoFile.name,
      size: videoFile.size,
      type: videoFile.type
    })
    
    // Upload video with metadata
    const metadata = {
      title: 'Test Video Upload - 284568_small.mp4',
      description: 'Testing Helia IPFS video upload functionality',
      tags: ['test', 'helia', 'ipfs', 'video'],
      category: 'Tech Demo',
      uploadedBy: 'test-user',
      mimeType: videoFile.type
    }
    
    let uploadProgress = 0
    const progressCallback = (progress: any) => {
      uploadProgress = progress.percentage || 0
      console.log(`📤 Upload progress: ${uploadProgress}%`)
    }
    
    console.log('🚀 Starting video upload to IPFS...')
    const result = await videoStorage.uploadVideoWithMetadata(
      videoFile, 
      metadata, 
      progressCallback
    )
    
    console.log('✅ Video upload completed successfully!')
    console.log('📊 Upload result:', result)
    
    // Get node stats
    const nodeStats = await videoStorage.getNodeStats()
    
    return NextResponse.json({
      success: true,
      message: 'Video uploaded successfully to IPFS',
      data: {
        result,
        nodeStats,
        videoFile: {
          name: videoFile.name,
          size: videoFile.size,
          type: videoFile.type
        }
      }
    })
    
  } catch (error) {
    console.error('❌ Video upload test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace available'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Video upload test endpoint - use POST to upload the test video',
    videoPath: '/home/<USER>/Documents/code/upsocial/upsocial-helia/284568_small.mp4'
  })
}
