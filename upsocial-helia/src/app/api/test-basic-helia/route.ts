import { NextRequest, NextResponse } from 'next/server'
import { heliaClient } from '@/lib/ipfs/helia-client'

export async function GET() {
  try {
    console.log('🔍 Testing basic Helia initialization...')
    
    // Initialize Helia
    console.log('📡 Initializing Helia client...')
    await heliaClient.initialize()
    
    // Get node stats
    console.log('📊 Getting node statistics...')
    const stats = await heliaClient.getNodeStats()
    
    console.log('✅ Basic Helia test completed successfully')
    
    return NextResponse.json({
      success: true,
      message: 'Helia basic test successful',
      data: {
        nodeStats: stats,
        timestamp: new Date().toISOString()
      }
    })
    
  } catch (error) {
    console.error('❌ Basic Helia test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace available'
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    console.log('📤 Testing simple JSON upload...')
    
    // Initialize Helia
    await heliaClient.initialize()
    
    // Upload simple JSON
    const testData = {
      message: 'Hello from Helia IPFS!',
      timestamp: new Date().toISOString(),
      test: true
    }
    
    console.log('🚀 Uploading test JSON...')
    const result = await heliaClient.uploadJSON(testData)
    
    console.log('📥 Retrieving uploaded JSON...')
    const retrieved = await heliaClient.getJSON(result.cid)
    
    console.log('✅ JSON upload/retrieval test successful')
    
    return NextResponse.json({
      success: true,
      message: 'JSON upload/retrieval test successful',
      data: {
        uploaded: testData,
        retrieved,
        uploadResult: result,
        match: JSON.stringify(testData) === JSON.stringify(retrieved)
      }
    })
    
  } catch (error) {
    console.error('❌ JSON test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace available'
    }, { status: 500 })
  }
}
