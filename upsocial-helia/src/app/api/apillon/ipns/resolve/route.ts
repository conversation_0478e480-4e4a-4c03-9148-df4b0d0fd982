import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const name = searchParams.get('name')
    
    if (!name) {
      return NextResponse.json(
        { error: 'Missing name parameter' },
        { status: 400 }
      )
    }

    // Get environment variables
    const apiKey = process.env.APILLON_API_KEY
    const apiSecret = process.env.APILLON_API_SECRET
    const ipnsUuid = process.env.APILLON_IPNS_UUID

    if (!apiKey || !apiSecret || !ipnsUuid) {
      console.error('Missing Apillon IPNS configuration in environment variables')
      return NextResponse.json(
        { error: 'Missing Apillon IPNS configuration' },
        { status: 500 }
      )
    }

    console.log('Resolving IPNS name via Apillon:', name)

    try {
      // Use Apillon's IPNS service to resolve the name
      const response = await fetch(`https://api.apillon.io/ipns/${ipnsUuid}/resolve/${encodeURIComponent(name)}`, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Successfully resolved IPNS:', result)

        return NextResponse.json({
          success: true,
          name,
          cid: result.cid,
          ipnsResult: result
        })
      } else {
        const errorText = await response.text()
        console.error('IPNS resolution failed:', response.status, errorText)
        return NextResponse.json(
          { 
            error: 'Failed to resolve IPNS name',
            details: errorText
          },
          { status: response.status }
        )
      }

    } catch (apillonError) {
      console.error('Apillon IPNS resolution failed:', apillonError)
      return NextResponse.json(
        { 
          error: 'Failed to resolve IPNS name',
          details: apillonError instanceof Error ? apillonError.message : 'Unknown error'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
