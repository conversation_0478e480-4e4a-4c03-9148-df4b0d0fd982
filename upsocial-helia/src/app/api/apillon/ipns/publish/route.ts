import { NextRequest, NextResponse } from 'next/server'

interface IPNSPublishRequest {
  cid: string
  name?: string
}

export async function POST(request: NextRequest) {
  try {
    // Get environment variables
    const apiKey = process.env.APILLON_API_KEY
    const apiSecret = process.env.APILLON_API_SECRET
    const ipnsUuid = process.env.APILLON_IPNS_UUID

    if (!apiKey || !apiSecret || !ipnsUuid) {
      console.error('Missing Apillon IPNS configuration in environment variables')
      return NextResponse.json(
        { error: 'Missing Apillon IPNS configuration' },
        { status: 500 }
      )
    }

    const { cid, name }: IPNSPublishRequest = await request.json()
    
    if (!cid) {
      return NextResponse.json(
        { error: 'Missing required field: cid' },
        { status: 400 }
      )
    }

    console.log('Publishing to IPNS via Apillon, CID:', cid)

    try {
      // Use Apillon's IPNS service via their API
      const response = await fetch(`https://api.apillon.io/ipns/${ipnsUuid}/publish`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          cid: cid,
          name: name || `content-${Date.now()}`
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Successfully published to IPNS:', result)

        return NextResponse.json({
          success: true,
          cid,
          ipnsResult: result
        })
      } else {
        const errorText = await response.text()
        console.error('IPNS publication failed:', response.status, errorText)
        return NextResponse.json(
          { 
            error: 'Failed to publish to IPNS',
            details: errorText
          },
          { status: response.status }
        )
      }

    } catch (apillonError) {
      console.error('Apillon IPNS publication failed:', apillonError)
      return NextResponse.json(
        { 
          error: 'Failed to publish to IPNS',
          details: apillonError instanceof Error ? apillonError.message : 'Unknown error'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
