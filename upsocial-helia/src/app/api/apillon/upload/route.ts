import { NextRequest, NextResponse } from 'next/server'
import { Storage } from '@apillon/sdk'

interface UploadRequest {
  cid: string
  data: string
}

export async function POST(request: NextRequest) {
  try {
    // Get environment variables
    const apiKey = process.env.APILLON_API_KEY
    const apiSecret = process.env.APILLON_API_SECRET
    const bucketUuid = process.env.APILLON_BUCKET_UUID

    if (!apiKey || !apiSecret || !bucketUuid) {
      console.error('Missing Apillon configuration in environment variables')
      return NextResponse.json(
        { error: 'Missing Apillon configuration' },
        { status: 500 }
      )
    }

    const { cid, data }: UploadRequest = await request.json()
    
    if (!cid || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: cid and data' },
        { status: 400 }
      )
    }

    console.log('Pinning to Apillon, CID:', cid)

    try {
      // Initialize Apillon storage
      const storage = new Storage({
        key: apiK<PERSON>,
        secret: apiSecret
      })

      const bucket = storage.bucket(bucketUuid)

      // Create a file buffer from the data
      const buffer = Buffer.from(data, 'utf-8')
      
      // Upload file to Apillon with CID as filename
      const uploadResult = await bucket.uploadFiles([
        {
          fileName: `${cid}.json`,
          content: buffer,
          contentType: 'application/json'
        }
      ])

      console.log('Successfully pinned to Apillon:', uploadResult)

      return NextResponse.json({
        success: true,
        cid,
        apillonResult: uploadResult
      })

    } catch (apillonError) {
      console.error('Apillon upload failed:', apillonError)
      return NextResponse.json(
        { 
          error: 'Failed to pin to Apillon',
          details: apillonError instanceof Error ? apillonError.message : 'Unknown error'
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
