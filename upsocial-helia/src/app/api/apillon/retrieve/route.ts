import { NextRequest, NextResponse } from 'next/server'
import { Storage } from '@apillon/sdk'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const cid = searchParams.get('cid')
    
    if (!cid) {
      return NextResponse.json(
        { error: 'Missing CID parameter' },
        { status: 400 }
      )
    }

    // Get environment variables
    const apiKey = process.env.APILLON_API_KEY
    const apiSecret = process.env.APILLON_API_SECRET
    const bucketUuid = process.env.APILLON_BUCKET_UUID

    if (!apiKey || !apiSecret || !bucketUuid) {
      console.error('Missing Apillon configuration in environment variables')
      return NextResponse.json(
        { error: 'Missing Apillon configuration' },
        { status: 500 }
      )
    }

    console.log('Retrieving from Apillon, CID:', cid)

    try {
      // Initialize Apillon storage
      const storage = new Storage({
        key: apiKey,
        secret: apiSecret
      })

      const bucket = storage.bucket(bucketUuid)

      // Try to get file info first
      const filesList = await bucket.listFiles()
      const filesArray = filesList.items || []
      const targetFile = filesArray.find((file: any) => file.name === `${cid}.json`)
      
      if (!targetFile) {
        console.log('File not found in Apillon bucket:', `${cid}.json`)
        return NextResponse.json(
          { error: 'File not found in Apillon storage' },
          { status: 404 }
        )
      }

      // Get the file content using the file's direct URL
      if (targetFile.link) {
        const fileResponse = await fetch(targetFile.link)
        
        if (fileResponse.ok) {
          const content = await fileResponse.text()
          console.log('Successfully retrieved from Apillon:', cid)
          
          // Return the content directly as text (not JSON) so it can be parsed by the client
          return new NextResponse(content, {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET',
              'Access-Control-Allow-Headers': 'Content-Type'
            }
          })
        } else {
          throw new Error(`Failed to fetch file content: ${fileResponse.status}`)
        }
      } else {
        throw new Error('File link not available')
      }

    } catch (error: unknown) {
      console.error('Failed to retrieve from Apillon:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return NextResponse.json(
        { error: 'Failed to retrieve from Apillon', details: errorMessage },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
