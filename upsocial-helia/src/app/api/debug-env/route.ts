// Debug endpoint to check environment variables
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('Debug endpoint called');
    
    // Check client-side environment variables (NEXT_PUBLIC_*)
    const clientVars = {
      NEXT_PUBLIC_APILLON_API_KEY: process.env.NEXT_PUBLIC_APILLON_API_KEY ? '✓ Set' : '✗ Missing',
      NEXT_PUBLIC_APILLON_API_SECRET: process.env.NEXT_PUBLIC_APILLON_API_SECRET ? '✓ Set' : '✗ Missing',
      NEXT_PUBLIC_APILLON_BUCKET_UUID: process.env.NEXT_PUBLIC_APILLON_BUCKET_UUID ? '✓ Set' : '✗ Missing',
      NEXT_PUBLIC_APILLON_IPNS_UUID: process.env.NEXT_PUBLIC_APILLON_IPNS_UUID ? '✓ Set' : '✗ Missing',
    };

    // Check server-side environment variables (without NEXT_PUBLIC_ prefix)
    const serverVars = {
      APILLON_API_KEY: process.env.APILLON_API_KEY ? '✓ Set' : '✗ Missing',
      APILLON_API_SECRET: process.env.APILLON_API_SECRET ? '✓ Set' : '✗ Missing',
      APILLON_BUCKET_UUID: process.env.APILLON_BUCKET_UUID ? '✓ Set' : '✗ Missing',
      APILLON_IPNS_UUID: process.env.APILLON_IPNS_UUID ? '✓ Set' : '✗ Missing',
    };

    // Get all environment variables that start with NEXT_PUBLIC_APILLON or APILLON
    const allApillonVars = Object.keys(process.env)
      .filter(key => key.includes('APILLON'))
      .reduce((acc, key) => {
        acc[key] = process.env[key] ? '✓ Set' : '✗ Missing';
        return acc;
      }, {} as Record<string, string>);

    const response = {
      timestamp: new Date().toISOString(),
      message: 'Environment variables debug info',
      clientVars,
      serverVars,
      allApillonVars,
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
    };

    console.log('Debug response:', response);
    
    return NextResponse.json(response, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({ 
      error: 'Failed to check environment variables',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    });
  }
}

// Add CORS support for preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
