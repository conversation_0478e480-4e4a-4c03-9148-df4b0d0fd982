import { NextRequest, NextResponse } from 'next/server'

export async function GET() {
  const cid = 'bafkreibe5w7q33kdnb7jj6jrtfztx2gulskezs44om6wddwog3tfsjmxle'
  
  const gateways = [
    'https://ipfs.io/ipfs/',
    'https://gateway.pinata.cloud/ipfs/',
    'https://cloudflare-ipfs.com/ipfs/',
    'https://dweb.link/ipfs/',
    'https://gateway.ipfs.io/ipfs/',
  ]

  const results = []
  
  for (const gateway of gateways) {
    try {
      const response = await fetch(`${gateway}${cid}`, {
        signal: AbortSignal.timeout(5000)
      })
      
      if (response.ok) {
        const data = await response.text()
        results.push({
          gateway,
          status: 'success',
          data: data.substring(0, 200) + '...' // First 200 chars
        })
        break // Stop at first success
      } else {
        results.push({
          gateway,
          status: 'failed',
          error: `HTTP ${response.status}`
        })
      }
    } catch (error) {
      results.push({
        gateway,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
  
  return NextResponse.json({ cid, results })
}
