import { NextRequest, NextResponse } from 'next/server'
import { VideoStorageService } from '@/lib/ipfs/video-storage'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    console.log('🎬 Testing video upload workflow...')
    
    // Initialize video storage service
    const storageService = VideoStorageService.getInstance()
    await storageService.initialize()
    
    // Read the test video file
    const videoPath = '/home/<USER>/Documents/code/upsocial/upsocial-helia/284568_small.mp4'
    
    if (!fs.existsSync(videoPath)) {
      throw new Error(`Video file not found at: ${videoPath}`)
    }
    
    console.log('📁 Reading video file from:', videoPath)
    const videoBuffer = fs.readFileSync(videoPath)
    const videoBlob = new Blob([videoBuffer], { type: 'video/mp4' })
    
    // Create a File-like object
    const videoFile = new File([videoBlob], '284568_small.mp4', { type: 'video/mp4' })
    
    console.log('📊 Video file stats:', {
      name: videoFile.name,
      size: videoFile.size,
      type: videoFile.type
    })
    
    let progress = 0
    const progressCallback = (progressData: any) => {
      progress = progressData.percentage || 0
      console.log(`📤 Upload progress: ${progress}%`, progressData.stage)
    }
    
    // Test the full upload workflow
    console.log('🚀 Step 1: Uploading video file...')
    const videoCID = await storageService.uploadVideo(videoFile, progressCallback)
    console.log('✅ Video uploaded:', videoCID)
    
    console.log('🖼️ Step 2: Generating thumbnail...')
    const thumbnailDataUrl = await storageService.generateThumbnail(videoFile)
    console.log('✅ Thumbnail generated (length):', thumbnailDataUrl.length)
    
    console.log('📤 Step 3: Uploading thumbnail...')
    const thumbnailCID = await storageService.uploadThumbnail(thumbnailDataUrl)
    console.log('✅ Thumbnail uploaded:', thumbnailCID)
    
    console.log('⏱️ Step 4: Getting video duration...')
    const duration = await storageService.getVideoDuration(videoFile)
    console.log('✅ Duration detected:', duration, 'seconds')
    
    console.log('📋 Step 5: Creating metadata...')
    const metadata = {
      title: 'Test Video Upload - Complete Workflow',
      description: 'Testing the complete video upload workflow with Helia IPFS',
      category: 'Technology',
      duration,
      size: videoFile.size,
      createdAt: new Date().toISOString(),
      videoCID,
      thumbnailCID,
      creator: 'Test User'
    }
    
    console.log('📤 Step 6: Uploading metadata...')
    const metadataCID = await storageService.uploadMetadata(metadata)
    console.log('✅ Metadata uploaded:', metadataCID)
    
    // Create the video object that would be saved to localStorage
    const videoData = {
      id: metadataCID,
      title: metadata.title,
      description: metadata.description,
      creator: metadata.creator,
      thumbnail: `https://ipfs.io/ipfs/${thumbnailCID}`,
      videoUrl: `https://ipfs.io/ipfs/${videoCID}`,
      ipfsHash: videoCID,
      views: 0,
      likes: 0,
      duration: `${Math.floor(duration / 60)}:${Math.floor(duration % 60).toString().padStart(2, '0')}`,
      createdAt: new Date(),
      categories: [metadata.category],
      metadata: {
        resolution: '720p',
        bitrate: 1000,
        codec: 'h264',
        fps: 30,
        size: videoFile.size
      }
    }
    
    console.log('✅ Complete workflow test successful!')
    
    return NextResponse.json({
      success: true,
      message: 'Complete video upload workflow test successful',
      data: {
        videoCID,
        thumbnailCID,
        metadataCID,
        duration: `${Math.floor(duration / 60)}:${Math.floor(duration % 60).toString().padStart(2, '0')}`,
        videoData,
        urls: {
          video: `https://ipfs.io/ipfs/${videoCID}`,
          thumbnail: `https://ipfs.io/ipfs/${thumbnailCID}`,
          metadata: `https://ipfs.io/ipfs/${metadataCID}`
        }
      }
    })
    
  } catch (error) {
    console.error('❌ Complete workflow test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : 'No stack trace available'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Complete video upload workflow test endpoint - use POST to test',
    testFile: '/home/<USER>/Documents/code/upsocial/upsocial-helia/284568_small.mp4'
  })
}
