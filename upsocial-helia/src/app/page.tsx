'use client'

import { useState, useEffect } from 'react'
import { Search, Menu, Upload, User, Settings } from 'lucide-react'
import VideoCard from '@/components/video/VideoCard'
import CategoryTabs from '@/components/feed/CategoryTabs'
import VideoModal from '@/components/video/VideoModal'
import AuthModal from '@/components/auth/SimpleAuthModal'
import VideoUploadModal from '@/components/upload/VideoUploadModal'
import { Video } from '@/types/video'
import { AuthManager } from '@/lib/data/auth-manager'
import type { AuthState } from '@/types'

// Featured video that auto-plays at the top (like old app)
const FEATURED_VIDEO = {
  id: 'featured',
  title: 'Welcome to UpSocial - Decentralized Video Platform',
  creator: 'UpSocial Team',
  thumbnail: '/assets/background.jpg',
  videoUrl: 'https://g.upsocial.com/ipfs/QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
  views: 125000,
  likes: 8900,
  duration: '2:15',
  ipfsHash: 'QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
  createdAt: new Date('2024-01-01'),
  categories: ['Welcome', 'Platform'],
  description: 'Discover the future of decentralized video sharing with UpSocial'
}

// Mock video data for the grid
const MOCK_VIDEOS: Video[] = [
  {
    id: '1',
    title: 'Amazing Nature Documentary',
    creator: 'NatureExplorer',
    thumbnail: '/assets/default.png',
    videoUrl: 'https://g.upsocial.com/ipfs/QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
    views: 12500,
    likes: 890,
    duration: '3:42',
    ipfsHash: 'QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
    createdAt: new Date('2024-01-15'),
    categories: ['Nature', 'Documentary']
  },
  {
    id: '2',
    title: 'Tech Innovation Showcase',
    creator: 'TechGuru',
    thumbnail: '/assets/default.png',
    videoUrl: 'https://g.upsocial.com/ipfs/Qmd9jWF4ajEop3AyJirP4q2N8nFzL5GyeoB75pTqRPSAUr',
    views: 8750,
    likes: 623,
    duration: '5:18',
    ipfsHash: 'Qmd9jWF4ajEop3AyJirP4q2N8nFzL5GyeoB75pTqRPSAUr',
    createdAt: new Date('2024-01-20'),
    categories: ['Technology', 'Innovation']
  },
  {
    id: '3',
    title: 'Creative Art Process',
    creator: 'ArtistLife',
    thumbnail: '/assets/default.png',
    videoUrl: 'https://g.upsocial.com/ipfs/QmUYUkvJFCpdt3dKqhGkAX9cpi3PydC2hvVHSTv1RYQQUS',
    views: 15200,
    likes: 1240,
    duration: '7:25',
    ipfsHash: 'QmUYUkvJFCpdt3dKqhGkAX9cpi3PydC2hvVHSTv1RYQQUS',
    createdAt: new Date('2024-01-18'),
    categories: ['Art', 'Creative']
  },
  {
    id: '4',
    title: 'Cooking Masterclass',
    creator: 'ChefMaster',
    thumbnail: '/assets/default.png',
    videoUrl: 'https://g.upsocial.com/ipfs/QmaeDhrZPgxQ3qypFdzqFos1gu7QnotfghYCPYjP2pVMNJ',
    views: 9850,
    likes: 743,
    duration: '12:33',
    ipfsHash: 'QmaeDhrZPgxQ3qypFdzqFos1gu7QnotfghYCPYjP2pVMNJ',
    createdAt: new Date('2024-01-12'),
    categories: ['Cooking', 'Food']
  },
  {
    id: '5',
    title: 'Gaming Highlights',
    creator: 'GamerPro',
    thumbnail: '/assets/default.png',
    videoUrl: 'https://g.upsocial.com/ipfs/QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
    views: 34500,
    likes: 2340,
    duration: '10:22',
    ipfsHash: 'QmXF1SaqxcFCTDrBrygXqdrHFT9nUhroHZDQ6p672xCRns',
    createdAt: new Date('2024-01-28'),
    categories: ['Gaming', 'Entertainment']
  }
]

export default function HomePage() {
  const [selectedCategory, setSelectedCategory] = useState('NEWEST')
  const [searchQuery, setSearchQuery] = useState('')
  const [videos, setVideos] = useState<Video[]>(MOCK_VIDEOS)
  const [filteredVideos, setFilteredVideos] = useState<Video[]>(MOCK_VIDEOS)
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null)
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const [authState, setAuthState] = useState<AuthState | null>(null)

  // Load videos from localStorage on mount
  useEffect(() => {
    const loadVideos = () => {
      try {
        const storedVideos = JSON.parse(localStorage.getItem('videos') || '[]')
        console.log('Loaded videos from localStorage:', storedVideos.length)
        
        // Combine stored videos with mock videos
        const allVideos = [...storedVideos, ...MOCK_VIDEOS]
        setVideos(allVideos)
        setFilteredVideos(allVideos)
      } catch (error) {
        console.error('Error loading videos from localStorage:', error)
        setVideos(MOCK_VIDEOS)
        setFilteredVideos(MOCK_VIDEOS)
      }
    }

    loadVideos()
  }, [])

  // Check auth state but don't block the UI
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authManager = AuthManager.getInstance()
        const currentAuth = authManager.getCurrentAuth()
        setAuthState(currentAuth)
      } catch (error) {
        console.error('Auth check failed:', error)
      }
    }
    checkAuth()
  }, [])

  // Filter videos based on category and search
  useEffect(() => {
    let filtered = videos

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(video =>
        video.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.creator.toLowerCase().includes(searchQuery.toLowerCase()) ||
        video.categories.some(cat => cat.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Filter by category
    if (selectedCategory && selectedCategory !== 'NEWEST') {
      if (selectedCategory === 'FOR ME') {
        // TODO: Implement personalized algorithm
        filtered = filtered
      } else if (selectedCategory === 'SUBSCRIPTIONS') {
        // TODO: Filter by subscribed creators
        filtered = filtered
      } else {
        filtered = filtered.filter(video =>
          video.categories.some(cat => 
            cat.toLowerCase().includes(selectedCategory.toLowerCase())
          )
        )
      }
    }

    setFilteredVideos(filtered)
  }, [selectedCategory, searchQuery, videos])

  const handleVideoSelect = (video: Video) => {
    setSelectedVideo(video)
    setIsVideoModalOpen(true)
  }

  const handleVideoModalClose = () => {
    setIsVideoModalOpen(false)
    setSelectedVideo(null)
  }

  const handleLikeAction = (videoId: string) => {
    if (!authState?.isAuthenticated) {
      setShowAuthModal(true)
      return
    }
    console.log('Like video:', videoId)
    // TODO: Implement like functionality
  }

  const handleProtectedAction = (action: string) => {
    if (!authState?.isAuthenticated) {
      setShowAuthModal(true)
      return
    }
    
    // Handle different protected actions
    switch (action) {
      case 'upload':
        setShowUploadModal(true)
        break
      case 'profile':
        console.log('Navigate to profile')
        break
      case 'settings':
        console.log('Navigate to settings')
        break
      default:
        console.log('Protected action:', action)
    }
  }

  const handleUploadComplete = (videoData: Video) => {
    console.log('Upload completed, refreshing videos...', videoData)
    
    // Reload videos from localStorage
    try {
      const storedVideos = JSON.parse(localStorage.getItem('videos') || '[]')
      const allVideos = [...storedVideos, ...MOCK_VIDEOS]
      setVideos(allVideos)
      setFilteredVideos(allVideos)
      
      // Close upload modal
      setShowUploadModal(false)
      
      // Show success message
      console.log('Videos refreshed successfully')
    } catch (error) {
      console.error('Error refreshing videos:', error)
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <header className="bg-black border-b border-gray-800 sticky top-0 z-40">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <img 
              src="/assets/logo_icon.png" 
              alt="UpSocial" 
              className="w-8 h-8"
              onError={(e) => {
                const target = e.currentTarget as HTMLImageElement
                const sibling = target.nextElementSibling as HTMLElement
                target.style.display = 'none'
                if (sibling) sibling.style.display = 'block'
              }}
            />
            <h1 className="text-xl font-bold hidden">UpSocial</h1>
            
            {/* Search Bar */}
            <div className="hidden md:flex items-center bg-gray-800 rounded-full px-4 py-2 w-96">
              <Search className="w-4 h-4 text-gray-400 mr-2" />
              <input
                type="text"
                placeholder="Search videos..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-transparent text-white placeholder-gray-400 w-full outline-none"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Mobile Search */}
            <button className="md:hidden text-white hover:text-gray-300" title="Search">
              <Search className="w-6 h-6" />
            </button>

            {/* Menu */}
            <button 
              onClick={() => setShowMenu(!showMenu)}
              className="text-white hover:text-gray-300 relative"
              title="Menu"
            >
              <Menu className="w-6 h-6" />
              
              {/* Dropdown Menu */}
              {showMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-700 rounded-lg shadow-lg">
                  {authState?.isAuthenticated ? (
                    <>
                      <button 
                        onClick={() => handleProtectedAction('upload')}
                        className="w-full text-left px-4 py-2 hover:bg-gray-800 flex items-center space-x-2"
                      >
                        <Upload className="w-4 h-4" />
                        <span>Upload Video</span>
                      </button>
                      <button 
                        onClick={() => handleProtectedAction('profile')}
                        className="w-full text-left px-4 py-2 hover:bg-gray-800 flex items-center space-x-2"
                      >
                        <User className="w-4 h-4" />
                        <span>My Profile</span>
                      </button>
                      <button 
                        onClick={() => handleProtectedAction('settings')}
                        className="w-full text-left px-4 py-2 hover:bg-gray-800 flex items-center space-x-2"
                      >
                        <Settings className="w-4 h-4" />
                        <span>Settings</span>
                      </button>
                    </>
                  ) : (
                    <button 
                      onClick={() => setShowAuthModal(true)}
                      className="w-full text-left px-4 py-2 hover:bg-gray-800 flex items-center space-x-2"
                    >
                      <User className="w-4 h-4" />
                      <span>Sign In / Register</span>
                    </button>
                  )}
                </div>
              )}
            </button>
          </div>
        </div>
      </header>

      {/* Featured Video Section - Smaller */}
      <section className="relative">
        <div className="aspect-[16/9] md:aspect-[21/9] bg-gray-900">
          <video
            className="w-full h-full object-cover"
            src={FEATURED_VIDEO.videoUrl}
            poster={FEATURED_VIDEO.thumbnail}
            autoPlay
            muted
            loop
            playsInline
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          <div className="absolute bottom-4 left-4 right-4">
            <h2 className="text-xl md:text-2xl font-bold mb-2">{FEATURED_VIDEO.title}</h2>
            <p className="text-gray-300 mb-2 text-sm md:text-base">{FEATURED_VIDEO.description}</p>
            <div className="flex items-center space-x-4 text-xs md:text-sm text-gray-400">
              <span>{FEATURED_VIDEO.creator}</span>
              <span>•</span>
              <span>{FEATURED_VIDEO.views.toLocaleString()} views</span>
              <span>•</span>
              <span>{FEATURED_VIDEO.duration}</span>
            </div>
          </div>
        </div>
      </section>

      {/* Category Tabs */}
      <CategoryTabs
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
        className="sticky top-16 z-30 bg-black border-b border-gray-800"
      />

      {/* Video Grid */}
      <main className="p-4">
        <div className="columns-2 md:columns-3 lg:columns-4 xl:columns-5 gap-4 space-y-4">
          {filteredVideos.map((video, index) => (
            <div 
              key={video.id} 
              className={`break-inside-avoid mb-4 ${
                index % 3 === 0 ? 'h-96' : index % 3 === 1 ? 'h-80' : 'h-88'
              }`}
            >
              <VideoCard
                video={video}
                onClick={handleVideoSelect}
                onLike={(videoId) => handleLikeAction(videoId)}
                onShare={(videoId) => handleProtectedAction('share')}
                className="h-full"
              />
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredVideos.length === 0 && (
          <div className="text-center py-16">
            <div className="text-gray-400 text-6xl mb-4">📹</div>
            <h3 className="text-xl font-semibold text-gray-300 mb-2">
              No videos found
            </h3>
            <p className="text-gray-500">
              {searchQuery ? `No videos match "${searchQuery}"` : 'No videos in this category'}
            </p>
          </div>
        )}
      </main>

      {/* Video Modal */}
      {selectedVideo && (
        <VideoModal
          video={selectedVideo}
          isOpen={isVideoModalOpen}
          onClose={handleVideoModalClose}
          onLike={(videoId: string) => handleLikeAction(videoId)}
          onShare={(videoId: string) => handleProtectedAction('share')}
        />
      )}

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          onAuthSuccess={(authState: AuthState) => {
            setAuthState(authState)
            setShowAuthModal(false)
          }}
        />
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <VideoUploadModal
          isOpen={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          onUploadComplete={handleUploadComplete}
        />
      )}
    </div>
  )
}
