'use client';

import { useState, useEffect } from 'react';

export default function DebugEnvPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetch('/api/debug-env')
      .then(res => res.json())
      .then(data => {
        setData(data);
        setLoading(false);
      })
      .catch(err => {
        setError(err.message);
        setLoading(false);
      });
  }, []);

  if (loading) return <div className="p-8">Loading...</div>;
  if (error) return <div className="p-8 text-red-600">Error: {error}</div>;

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Environment Variables Debug</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Client-side Variables (NEXT_PUBLIC_*)</h2>
          <pre className="text-sm">{JSON.stringify(data.clientVars, null, 2)}</pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Server-side Variables</h2>
          <pre className="text-sm">{JSON.stringify(data.serverVars, null, 2)}</pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">All Apillon Variables Found</h2>
          <pre className="text-sm">{JSON.stringify(data.allApillonVars, null, 2)}</pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Environment Info</h2>
          <p><strong>NODE_ENV:</strong> {data.nodeEnv}</p>
          <p><strong>VERCEL_ENV:</strong> {data.vercelEnv}</p>
          <p><strong>Timestamp:</strong> {data.timestamp}</p>
        </div>
      </div>
    </div>
  );
}
