'use client'

import { useState } from 'react'
import { videoStorage } from '@/lib/ipfs/video-storage'
import { heliaClient } from '@/lib/ipfs/helia-client'

export default function HeliaTestPage() {
  const [isInitializing, setIsInitializing] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [nodeStats, setNodeStats] = useState<any>(null)
  const [verifying, setVerifying] = useState(false)
  const [availability, setAvailability] = useState<any>(null)
  const [testCid, setTestCid] = useState('')

  const initializeHelia = async () => {
    setIsInitializing(true)
    setError(null)
    
    try {
      console.log('🚀 Initializing Helia...')
      await videoStorage.initialize()
      setIsInitialized(true)
      
      // Get node stats
      const stats = await videoStorage.getNodeStats()
      setNodeStats(stats)
      console.log('✅ Helia initialized successfully')
    } catch (err) {
      console.error('❌ Failed to initialize Helia:', err)
      setError(err instanceof Error ? err.message : 'Failed to initialize')
    } finally {
      setIsInitializing(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    setError(null)
    setResult(null)

    try {
      console.log('📤 Uploading file:', file.name)
      
      const progress = (progress: any) => {
        console.log('Progress:', progress)
      }

      if (file.type.startsWith('video/')) {
        // Upload video with metadata
        const metadata = {
          title: file.name,
          description: 'Test video upload via Helia',
          tags: ['test', 'helia', 'ipfs'],
          category: 'Tech',
          creator: 'test-user',
          mimeType: file.type,
          duration: 0, // Will be set during upload
          videoCID: '', // Will be set during upload
          thumbnailCID: '', // Will be set during upload
          createdAt: new Date().toISOString()
        }
        
        const videoMetadata = await videoStorage.uploadVideoWithMetadata(file, metadata, progress)
        setResult({
          type: 'video',
          data: videoMetadata
        })
      } else {
        // Upload regular file
        const cid = await videoStorage.uploadVideo(file, progress)
        setResult({
          type: 'file',
          data: { cid, name: file.name, size: file.size }
        })
      }
      
      console.log('✅ Upload completed successfully')
    } catch (err) {
      console.error('❌ Upload failed:', err)
      setError(err instanceof Error ? err.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const testApiRoute = async () => {
    try {
      setError(null)
      console.log('🧪 Testing API route...')
      
      const response = await fetch('/api/test-helia')
      const data = await response.json()
      
      if (data.success) {
        console.log('✅ API test successful:', data)
        setResult({
          type: 'api-test',
          data: data
        })
      } else {
        throw new Error(data.error)
      }
    } catch (err) {
      console.error('❌ API test failed:', err)
      setError(err instanceof Error ? err.message : 'API test failed')
    }
  }

  const testVideoUpload = async () => {
    try {
      setError(null)
      setUploading(true)
      console.log('🎬 Testing video upload...')
      
      const response = await fetch('/api/test-video-upload', {
        method: 'POST'
      })
      const data = await response.json()
      
      if (data.success) {
        console.log('✅ Video upload test successful:', data)
        setResult({
          type: 'video-upload',
          data: data.data
        })
      } else {
        throw new Error(data.error)
      }
    } catch (err) {
      console.error('❌ Video upload test failed:', err)
      setError(err instanceof Error ? err.message : 'Video upload test failed')
    } finally {
      setUploading(false)
    }
  }

  const testIPNSAnnouncement = async () => {
    if (!result?.data?.cid) {
      setError('Please upload a file first to get a CID for IPNS testing')
      return
    }

    setUploading(true)
    setError(null)

    try {
      console.log('📢 Testing IPNS announcement for CID:', result.data.cid)
      
      // Test network announcement
      await heliaClient.announceToNetwork(result.data.cid)
      
      // Test IPNS publishing
      const ipnsKey = await heliaClient.publishToIPNS(result.data.cid)
      
      console.log('✅ IPNS tests completed')
      setResult({
        ...result,
        ipns: {
          announced: true,
          ipnsKey: ipnsKey,
          message: 'CID announced to network and published to IPNS'
        }
      })
    } catch (err) {
      console.error('❌ IPNS test failed:', err)
      setError(err instanceof Error ? err.message : 'IPNS test failed')
    } finally {
      setUploading(false)
    }
  }

  const verifyContentAvailability = async () => {
    if (!result?.data?.cid && !result?.data?.videoCID && !testCid.trim()) {
      setError('Please upload content or enter a CID to verify availability')
      return
    }

    setVerifying(true)
    setError(null)
    setAvailability(null)

    try {
      const cid = testCid.trim() || result.data.cid || result.data.videoCID
      console.log('🔍 Verifying content availability for CID:', cid)
      
      const availability = await heliaClient.verifyContentAvailability(cid)
      setAvailability(availability)
      
      console.log('✅ Content verification completed:', availability)
    } catch (err) {
      console.error('❌ Content verification failed:', err)
      setError(err instanceof Error ? err.message : 'Content verification failed')
    } finally {
      setVerifying(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Helia IPFS Test Page</h1>
      
      {/* Initialization */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">1. Initialize Helia Client</h2>
        <div className="flex gap-4 items-center">
          <button
            onClick={initializeHelia}
            disabled={isInitializing || isInitialized}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isInitializing ? 'Initializing...' : isInitialized ? 'Initialized ✅' : 'Initialize Helia'}
          </button>
          {isInitialized && (
            <span className="text-green-600 font-medium">✅ Ready for uploads</span>
          )}
        </div>
        
        {nodeStats && (
          <div className="mt-4 p-4 bg-gray-50 rounded">
            <h3 className="font-medium mb-2">Node Stats:</h3>
            <pre className="text-sm">{JSON.stringify(nodeStats, null, 2)}</pre>
          </div>
        )}
      </div>

      {/* API Test */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">2. Test API Route</h2>
        <button
          onClick={testApiRoute}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
        >
          Test Helia API
        </button>
      </div>

      {/* Video Upload Test */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">3. Test Video Upload</h2>
        <div className="space-y-4">
          <p className="text-gray-600">
            Test uploading the local video file: <code className="bg-gray-100 px-2 py-1 rounded">284568_small.mp4</code>
          </p>
          <button
            onClick={testVideoUpload}
            disabled={!isInitialized || uploading}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50"
          >
            {uploading ? '🎬 Uploading Video...' : '🎬 Upload Test Video'}
          </button>
          
          {!isInitialized && (
            <p className="text-gray-500">Please initialize Helia first</p>
          )}
        </div>
      </div>

      {/* File Upload */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">4. Upload File</h2>
        <div className="space-y-4">
          <label className="block">
            <span className="text-sm font-medium text-gray-700 mb-2 block">
              Choose a file to upload:
            </span>
            <input
              type="file"
              onChange={handleFileUpload}
              disabled={!isInitialized || uploading}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </label>
          
          {uploading && (
            <div className="text-blue-600">📤 Uploading file...</div>
          )}
          
          {!isInitialized && (
            <p className="text-gray-500">Please initialize Helia first</p>
          )}
        </div>
      </div>

      {/* IPNS Testing */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">5. IPNS Network Announcement</h2>
        <div className="flex gap-4 items-center">
          <button
            onClick={testIPNSAnnouncement}
            disabled={!isInitialized || uploading || !result?.data?.cid}
            className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {uploading ? '📢 Announcing...' : '📢 Announce to IPFS Network'}
          </button>
          
          {!result?.data?.cid && (
            <p className="text-gray-500">Upload a file first to test IPNS</p>
          )}
        </div>
        <p className="text-sm text-gray-600 mt-2">
          This will announce your uploaded file to the IPFS network and publish to IPNS for better discoverability.
        </p>
      </div>

      {/* Content Verification */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">6. Verify Network Availability</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test specific CID (optional):
            </label>
            <input
              type="text"
              value={testCid}
              onChange={(e) => setTestCid(e.target.value)}
              placeholder="bafybeiautsn42h7m4igsvygc6wdzke22i6ishdr34hy3ksqqs7m5qdfzq"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="flex gap-4 items-center">
            <button
              onClick={verifyContentAvailability}
              disabled={!isInitialized || verifying}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              {verifying ? '🔍 Verifying...' : '🔍 Check Network Availability'}
            </button>
            
            {(!result?.data?.cid && !result?.data?.videoCID && !testCid.trim()) && (
              <p className="text-gray-500">Upload content or enter a CID to verify availability</p>
            )}
          </div>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          This will check if your content is accessible via public IPFS gateways and the broader network.
        </p>
      </div>

      {/* Results */}
      {result && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Results</h2>
          <div className="bg-green-50 p-4 rounded">
            <h3 className="font-medium text-green-800 mb-2">Success! 🎉</h3>
            <pre className="text-sm text-green-700 overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
            
            {result.type === 'video' && result.data.cid && (
              <div className="mt-4">
                <a
                  href={`https://ipfs.io/ipfs/${result.data.cid}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline"
                >
                  View on IPFS Gateway →
                </a>
              </div>
            )}

            {result.ipns && (
              <div className="mt-4 p-3 bg-purple-50 rounded">
                <h4 className="font-medium text-purple-800 mb-2">IPNS Announcement Results:</h4>
                <div className="text-sm text-purple-700">
                  <p>✅ {result.ipns.message}</p>
                  {result.ipns.ipnsKey && (
                    <p className="mt-1">
                      <strong>IPNS Key:</strong> 
                      <code className="bg-purple-100 px-1 rounded ml-1">{result.ipns.ipnsKey}</code>
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Availability Results */}
      {availability && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Network Availability Results</h2>
          <div className={`p-4 rounded ${availability.networkRetrievable ? 'bg-green-50' : 'bg-yellow-50'}`}>
            <h3 className={`font-medium mb-2 ${availability.networkRetrievable ? 'text-green-800' : 'text-yellow-800'}`}>
              {availability.networkRetrievable ? '✅ Content Available on Network!' : '⚠️ Content Not Yet Propagated'}
            </h3>
            <div className="text-sm space-y-2">
              <p><strong>Local Storage:</strong> {availability.localAvailable ? '✅ Available' : '❌ Not found'}</p>
              <p><strong>Network Access:</strong> {availability.networkRetrievable ? '✅ Accessible' : '❌ Not yet available'}</p>
              
              <div className="mt-3">
                <strong>Gateway Status:</strong>
                <ul className="mt-1 space-y-1">
                  {availability.gateways.map((gateway: any, index: number) => (
                    <li key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className={gateway.accessible ? 'text-green-600' : 'text-red-600'}>
                          {gateway.accessible ? '✅' : '❌'}
                        </span>
                        <span className="ml-2">{new URL(gateway.url).hostname}</span>
                      </div>
                      {gateway.responseTime && (
                        <span className="text-sm text-gray-500">
                          {gateway.responseTime}ms
                        </span>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
              
              {!availability.networkRetrievable && (
                <div className="mt-3 p-3 bg-blue-50 rounded">
                  <p className="text-blue-800">
                    <strong>💡 Tip:</strong> Content may take 5-15 minutes to propagate across the IPFS network. 
                    Try the verification again in a few minutes.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Errors */}
      {error && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Error</h2>
          <div className="bg-red-50 p-4 rounded">
            <h3 className="font-medium text-red-800 mb-2">Error ❌</h3>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Instructions</h2>
        <ol className="list-decimal list-inside space-y-2 text-gray-700">
          <li>First, click "Initialize Helia" to start the IPFS node</li>
          <li>Wait for initialization to complete (you'll see node stats)</li>
          <li>Test the API route to ensure server-side functionality works</li>
          <li>Test uploading the local video file (284568_small.mp4)</li>
          <li>Try uploading your own files (text, image, or video)</li>
          <li>After uploading, test IPNS announcement to improve discoverability</li>
          <li>Verify network availability to check if content is accessible via public gateways</li>
          <li>Check the browser console for detailed logs</li>
        </ol>
        
        <div className="mt-4 p-4 bg-yellow-50 rounded">
          <p className="text-yellow-800">
            <strong>Note:</strong> This is a fully decentralized IPFS implementation using Helia with IPNS support. 
            The first upload may take longer as the node establishes connections to the IPFS network.
            IPNS announcement helps improve file discoverability across the network. Use the verification tool
            to check if your content is accessible via public gateways.
          </p>
        </div>
      </div>
    </div>
  )
}
