# Apillon Setup Guide

## What is <PERSON><PERSON><PERSON>?

Apillon is an IPFS pinning service that ensures your IPFS content stays available on the network and can be accessed through public gateways. This solves the main issue we've been experiencing where IPFS content uploaded locally isn't available on public gateways.

## Why do we need it?

Our authentication system stores user profiles on IPFS, but when users try to log in, the content isn't available on public IPFS gateways because it's only stored on the local IPFS node. <PERSON><PERSON><PERSON> pins our content to their infrastructure, making it accessible worldwide.

Additionally, we use **IPNS (InterPlanetary Name System)** through <PERSON><PERSON><PERSON> to create mutable pointers to user profiles. This allows users to have stable usernames that always point to their latest profile data, even as the underlying IPFS content changes.

## Setup Steps

### 1. Create Apillon Account
- Go to https://app.apillon.io/
- Sign up for a free account
- Complete the verification process

### 2. Create a Storage Bucket
- In your Apillon dashboard, go to "Storage"
- Click "Create Bucket"
- Give it a name like "upsocial-user-profiles"
- Copy the Bucket UUID (you'll need this)

### 3. Create an IPNS Collection
- In your Apillon dashboard, go to "IPNS"
- Click "Create Collection"
- Give it a name like "upsocial-user-names"
- Copy the IPNS UUID (you'll need this)

### 4. Get API Credentials
- In your Apillon dashboard, go to "API Keys"
- Click "Create API Key"
- Give it a name like "upsocial-app"
- Copy the API Key and API Secret

### 5. Update Environment Variables
Open `.env.local` and replace the placeholder values:

# Apillon Integration Setup Guide

## Overview
This guide explains how to set up Apillon integration for guaranteed IPFS content pinning and fast gateway availability.

## Environment Variables

Add these environment variables to your `.env.local` file:

```bash
# Apillon Configuration
APILLON_API_KEY=your_apillon_api_key_here
APILLON_SECRET=your_apillon_secret_here
APILLON_BUCKET_UUID=your_bucket_uuid_here

# Public environment variables (for client-side use)
NEXT_PUBLIC_APILLON_API_KEY=your_apillon_api_key_here
NEXT_PUBLIC_APILLON_SECRET=your_apillon_secret_here
NEXT_PUBLIC_APILLON_BUCKET_UUID=your_bucket_uuid_here
```

## Getting Apillon Credentials

1. **Sign up for Apillon**: Visit [apillon.io](https://apillon.io) and create an account
2. **Create a Project**: Create a new project in your Apillon dashboard
3. **Generate API Keys**: 
   - Go to your project settings
   - Generate API Key and Secret
   - Copy these values to your environment variables
4. **Create Storage Bucket**:
   - Navigate to the Storage section
   - Create a new bucket for IPFS storage
   - Copy the bucket UUID to your environment variables

## Configuration Options

### Helia Client Configuration
```typescript
import { HeliaClient } from './lib/ipfs/helia-client'

const client = new HeliaClient({
  enableApillonPinning: true, // Enable automatic Apillon pinning
  enableWebRTC: false,        // Disable WebRTC for better browser compatibility
  enableMDNS: true,          // Enable local network discovery
})
```

### Upload with Apillon Pinning
```typescript
// Upload file with automatic Apillon pinning
const result = await client.uploadFile(file)

if (result.apillonPinned) {
  console.log('File pinned to Apillon:', result.apillonUrl)
  // File is now guaranteed to be available via Apillon's IPFS gateway
}
```

## Benefits of Apillon Integration

1. **Guaranteed Availability**: Content is pinned to Apillon's always-online IPFS infrastructure
2. **Fast Gateway Access**: Apillon provides a dedicated IPFS gateway at `ipfs.apillon.io`
3. **Instant Propagation**: Content becomes available immediately after pinning
4. **Reliability**: Fallback to server-side pinning if client-side fails
5. **Enhanced Discovery**: Multiple gateway URLs provided for better redundancy

## Gateway URLs

When Apillon pinning is enabled, content becomes available via multiple gateways:

1. **Apillon Gateway**: `https://ipfs.apillon.io/ipfs/{cid}` (fastest)
2. **Cloudflare**: `https://cf-ipfs.com/ipfs/{cid}`
3. **IPFS.io**: `https://ipfs.io/ipfs/{cid}`
4. **Dweb.link**: `https://dweb.link/ipfs/{cid}`
5. **Pinata**: `https://gateway.pinata.cloud/ipfs/{cid}`

## API Endpoints

### POST /api/apillon/upload
Server-side endpoint for Apillon pinning:

```typescript
// Request
FormData {
  file: File,
  cid: string
}

// Response
{
  success: boolean,
  cid?: string,
  uuid?: string,
  url?: string,
  error?: string
}
```

## Testing

Use the test page at `/test-apillon` to verify your Apillon integration:

1. Select a test file
2. Run "Test Helia + Apillon" to verify configuration
3. Test video upload with Apillon pinning
4. Verify content availability across gateways
5. Test server-side API route

## Troubleshooting

### Common Issues

1. **"Apillon API key not found"**
   - Ensure environment variables are properly set
   - Restart your development server after adding env vars

2. **"Apillon bucket UUID not configured"**
   - Create a storage bucket in your Apillon dashboard
   - Add the bucket UUID to your environment variables

3. **"Client-side pinning failed"**
   - This is normal - the system will fallback to server-side pinning
   - Check that your API route is working correctly

4. **"Upload failed - no results returned"**
   - Verify your Apillon API credentials
   - Check the Apillon dashboard for any account limitations

### Debug Mode

Enable debug logging to troubleshoot issues:

```typescript
// Enable detailed logging in browser console
localStorage.setItem('debug', 'ipfs:*')
```

## Performance Optimization

1. **Parallel Upload**: The system uploads to IPFS and pins to Apillon simultaneously
2. **Multiple Gateways**: Content URLs include multiple gateway options for redundancy
3. **Instant Availability**: Apillon-pinned content is immediately accessible
4. **Smart Fallbacks**: Client-side failures automatically trigger server-side pinning

## Security Notes

- API keys are used for both client and server-side operations
- Server-side operations provide better security for sensitive workflows
- Client-side operations enable faster user experience
- All uploads are pinned to IPFS for decentralized availability

## Support

For Apillon-specific issues:
- Documentation: [docs.apillon.io](https://docs.apillon.io)
- Support: Contact Apillon support team

For integration issues:
- Check the browser console for detailed error messages
- Use the `/test-apillon` page to diagnose problems
- Verify environment variables are correctly configured

### 6. Test the Integration
After setting up the environment variables:
1. Restart your development server (`npm run dev`)
2. Try signing up a new user
3. The system will automatically pin the user profile to Apillon
4. Try logging in - it should now work reliably

## How it Works

1. **Sign Up Process:**
   - User data is stored to local IPFS (Helia)
   - Data is simultaneously pinned to Apillon in the background
   - User gets immediate response (doesn't wait for pinning)

2. **Login Process:**
   - System first tries to retrieve from local cache
   - If not found, tries local IPFS (Helia)
   - If that fails, tries resolving user's IPNS name to get current profile CID
   - If that fails, tries Apillon gateway
   - If that fails, tries public IPFS gateways
   - Caches successful retrievals for faster future access

## Benefits

- **Reliability:** Content is guaranteed to be available on IPFS network
- **Mutable Profiles:** IPNS allows user profiles to be updated while keeping stable usernames
- **Speed:** Apillon provides fast, reliable gateways
- **Redundancy:** Multiple fallback options ensure login always works
- **SSL/CORS:** Apillon handles SSL certificates and CORS headers properly

## Cost

Apillon offers a generous free tier that should be sufficient for testing and initial users. Check their pricing page for current limits.
