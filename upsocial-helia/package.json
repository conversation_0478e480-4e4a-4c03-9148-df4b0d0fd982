{"name": "upsocial-helia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:skip-lint": "next build --no-lint", "start": "next start", "lint": "next lint"}, "dependencies": {"@apillon/sdk": "^3.12.0", "@helia/dag-cbor": "^4.0.7", "@helia/dag-json": "^4.0.7", "@helia/ipns": "^8.2.4", "@helia/strings": "^4.0.7", "@helia/unixfs": "^5.0.4", "@heroicons/react": "^2.2.0", "@libp2p/bootstrap": "^11.0.46", "@libp2p/circuit-relay-v2": "^3.2.23", "@libp2p/dcutr": "^2.0.37", "@libp2p/identify": "^3.0.38", "@libp2p/kad-dht": "^15.1.10", "@libp2p/mdns": "^11.0.46", "@libp2p/mplex": "^11.0.46", "@libp2p/noise": "^12.0.1", "@libp2p/ping": "^2.0.36", "@libp2p/webrtc": "^5.2.23", "@libp2p/websockets": "^9.2.18", "@react-spring/web": "^10.0.1", "@types/formidable": "^3.4.5", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "formidable": "^3.5.4", "helia": "^5.5.0", "libp2p": "^2.9.0", "lucide-react": "^0.534.0", "multiformats": "^13.3.7", "next": "15.4.4", "pinata": "^2.4.9", "process": "^0.11.10", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "stream-browserify": "^3.0.0", "util": "^0.12.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5", "vercel": "^44.6.4"}}