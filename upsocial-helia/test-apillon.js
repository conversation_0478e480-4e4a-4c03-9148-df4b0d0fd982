// Test script to verify <PERSON><PERSON><PERSON> integration
// Run this in the browser console or as a separate script

async function testApillonIntegration() {
  console.log('Testing Apillon Integration...')
  
  // Test data
  const testData = {
    type: 'test',
    data: { message: 'Hello Apillon!', timestamp: Date.now() },
    timestamp: Date.now()
  }
  
  const testCid = 'bafkreiehtestdataforverification'
  
  try {
    // Test upload endpoint
    console.log('1. Testing upload endpoint...')
    const uploadResponse = await fetch('/api/apillon/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        cid: testCid,
        data: JSON.stringify(testData)
      })
    })
    
    const uploadResult = await uploadResponse.json()
    console.log('Upload result:', uploadResult)
    
    if (uploadResponse.ok) {
      console.log('✅ Upload endpoint working')
    } else {
      console.log('❌ Upload endpoint failed:', uploadResult.error)
      return
    }
    
    // Test retrieve endpoint
    console.log('2. Testing retrieve endpoint...')
    const retrieveResponse = await fetch(`/api/apillon/retrieve?cid=${testCid}`)
    
    if (retrieveResponse.ok) {
      const retrieveResult = await retrieveResponse.text()
      console.log('Retrieve result:', retrieveResult)
      console.log('✅ Retrieve endpoint working')
      
      // Verify data integrity
      const parsedData = JSON.parse(retrieveResult)
      if (parsedData.data.message === testData.data.message) {
        console.log('✅ Data integrity verified')
      } else {
        console.log('❌ Data integrity check failed')
      }
    } else {
      const retrieveError = await retrieveResponse.json()
      console.log('❌ Retrieve endpoint failed:', retrieveError.error)
    }
    
  } catch (error) {
    console.error('Test failed with error:', error)
  }
}

// Function to test the complete authentication flow
async function testAuthenticationFlow() {
  console.log('Testing Complete Authentication Flow...')
  
  // Test user data
  const testUser = {
    username: `testuser_${Date.now()}`,
    email: `test_${Date.now()}@example.com`,
    password: 'TestPassword123!'
  }
  
  try {
    console.log('1. Testing user registration...')
    
    // Trigger registration through the UI or directly through AuthManager
    // This would normally be done through the SignUp component
    console.log('Register user:', testUser.username)
    
    // Note: Actual registration would happen through the UI
    // This is just a placeholder for the test structure
    
    console.log('2. Testing user login...')
    console.log('Login user:', testUser.username)
    
    // Note: Actual login would happen through the UI
    // This is just a placeholder for the test structure
    
  } catch (error) {
    console.error('Authentication test failed:', error)
  }
}

// Export functions for use
if (typeof window !== 'undefined') {
  window.testApillonIntegration = testApillonIntegration
  window.testAuthenticationFlow = testAuthenticationFlow
  console.log('Test functions available: testApillonIntegration(), testAuthenticationFlow()')
}
