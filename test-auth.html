<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upsocial Authentication Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container { 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .form-section { 
            margin: 20px 0; 
            padding: 20px; 
            background: rgba(255,255,255,0.05); 
            border-radius: 10px; 
        }
        input { 
            width: 100%; 
            padding: 12px; 
            margin: 5px 0; 
            border: none; 
            border-radius: 8px; 
            background: rgba(255,255,255,0.9);
            color: #333;
            font-size: 14px;
        }
        button { 
            background: linear-gradient(45deg, #ff6b6b, #ee5a24); 
            color: white; 
            padding: 12px 25px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        .result { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            word-wrap: break-word;
        }
        .success { 
            background: rgba(46, 204, 113, 0.2); 
            border: 2px solid #2ecc71;
        }
        .error { 
            background: rgba(231, 76, 60, 0.2); 
            border: 2px solid #e74c3c;
        }
        .info { 
            background: rgba(52, 152, 219, 0.2); 
            border: 2px solid #3498db;
        }
        h1 { text-align: center; margin-bottom: 30px; }
        h2 { color: #ecf0f1; border-bottom: 2px solid rgba(255,255,255,0.3); padding-bottom: 10px; }
        .test-results { 
            margin-top: 30px; 
            padding: 20px; 
            background: rgba(0,0,0,0.2); 
            border-radius: 10px;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Upsocial Authentication System Test</h1>
        
        <div class="form-section">
            <h2>📝 User Registration</h2>
            <input type="text" id="regUsername" placeholder="Username" value="testuser" />
            <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>" />
            <input type="password" id="regPassword" placeholder="Password" value="password123" />
            <button onclick="registerUser()">Register User</button>
            <button onclick="runRegistrationTests()">Run Registration Tests</button>
            <div id="regResult"></div>
        </div>

        <div class="form-section">
            <h2>🔐 User Login</h2>
            <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>" />
            <input type="password" id="loginPassword" placeholder="Password" value="password123" />
            <button onclick="loginUser()">Login User</button>
            <button onclick="runLoginTests()">Run Login Tests</button>
            <div id="loginResult"></div>
        </div>

        <div class="form-section">
            <h2>🔧 System Controls</h2>
            <button onclick="initializeDB()">Initialize Database</button>
            <button onclick="getAllUsers()">Get All Users</button>
            <button onclick="getSystemInfo()">System Info</button>
            <button onclick="runFullTestSuite()">🧪 Run Full Test Suite</button>
            <div id="systemResult"></div>
        </div>

        <div class="test-results">
            <h2>📊 Test Results</h2>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:4000/api/Upsocial';
        let testResults = [];

        function addResult(section, message, type = 'info') {
            const element = document.getElementById(section);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            element.appendChild(div);
            
            // Also add to test results
            testResults.push({
                timestamp: new Date().toISOString(),
                section: section,
                message: message,
                type: type
            });
            updateTestResults();
        }

        function updateTestResults() {
            const element = document.getElementById('testResults');
            element.innerHTML = testResults.map(result => 
                `<div class="result ${result.type}">
                    <strong>${result.section}</strong> (${new Date(result.timestamp).toLocaleTimeString()}): ${result.message}
                </div>`
            ).join('');
        }

        async function makeRequest(endpoint, method = 'GET', data = null) {
            try {
                const config = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                if (data) {
                    config.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE}${endpoint}`, config);
                const result = await response.json();
                return { success: true, data: result, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function registerUser() {
            const username = document.getElementById('regUsername').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;

            if (!username || !email || !password) {
                addResult('regResult', 'Please fill in all fields', 'error');
                return;
            }

            const result = await makeRequest('/users/register', 'POST', { username, email, password });
            
            if (result.success) {
                if (result.data.status) {
                    addResult('regResult', `✅ ${result.data.msg}`, 'success');
                } else {
                    addResult('regResult', `⚠️ ${result.data.msg}`, 'error');
                }
            } else {
                addResult('regResult', `❌ Registration failed: ${result.error}`, 'error');
            }
        }

        async function loginUser() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                addResult('loginResult', 'Please fill in email and password', 'error');
                return;
            }

            const result = await makeRequest('/users/login', 'POST', { email, password });
            
            if (result.success) {
                if (result.data.status) {
                    addResult('loginResult', `✅ ${result.data.msg} - User: ${result.data.curUser}`, 'success');
                    addResult('loginResult', `📄 User Data: ${JSON.stringify(result.data.Data, null, 2)}`, 'info');
                } else {
                    addResult('loginResult', `⚠️ ${result.data.msg}`, 'error');
                }
            } else {
                addResult('loginResult', `❌ Login failed: ${result.error}`, 'error');
            }
        }

        async function initializeDB() {
            const result = await makeRequest('/admin/createDB', 'POST');
            
            if (result.success) {
                addResult('systemResult', `🗄️ Database: ${result.data.msg} (Mode: ${result.data.mode})`, 
                    result.data.status ? 'success' : 'error');
            } else {
                addResult('systemResult', `❌ DB Init failed: ${result.error}`, 'error');
            }
        }

        async function getAllUsers() {
            const result = await makeRequest('/admin/getAllUsers', 'GET');
            
            if (result.success) {
                if (result.data.status) {
                    addResult('systemResult', `👥 Found ${result.data.count} users (Mode: ${result.data.mode})`, 'success');
                    addResult('systemResult', `📋 Users: ${JSON.stringify(result.data.users, null, 2)}`, 'info');
                } else {
                    addResult('systemResult', `⚠️ ${result.data.msg}`, 'error');
                }
            } else {
                addResult('systemResult', `❌ Get users failed: ${result.error}`, 'error');
            }
        }

        async function getSystemInfo() {
            const result = await makeRequest('/system/info', 'GET');
            
            if (result.success) {
                addResult('systemResult', `ℹ️ System: ${result.data.message} v${result.data.version}`, 'success');
                addResult('systemResult', `🔗 Endpoints: ${result.data.authEndpoints.join(', ')}`, 'info');
            } else {
                addResult('systemResult', `❌ System info failed: ${result.error}`, 'error');
            }
        }

        async function runRegistrationTests() {
            addResult('regResult', '🧪 Starting registration test suite...', 'info');
            
            // Test 1: Valid registration
            const testUser = {
                username: 'automated_test_' + Date.now(),
                email: 'test_' + Date.now() + '@example.com',
                password: 'TestPassword123!'
            };
            
            const reg1 = await makeRequest('/users/register', 'POST', testUser);
            if (reg1.success && reg1.data.status) {
                addResult('regResult', `✅ Test 1 PASSED: Valid registration successful`, 'success');
            } else {
                addResult('regResult', `❌ Test 1 FAILED: ${reg1.data?.msg || reg1.error}`, 'error');
            }

            // Test 2: Duplicate email registration
            const reg2 = await makeRequest('/users/register', 'POST', testUser);
            if (reg2.success && !reg2.data.status && reg2.data.msg.includes('already registered')) {
                addResult('regResult', `✅ Test 2 PASSED: Duplicate email correctly rejected`, 'success');
            } else {
                addResult('regResult', `❌ Test 2 FAILED: Duplicate email not handled properly`, 'error');
            }

            // Test 3: Missing fields
            const reg3 = await makeRequest('/users/register', 'POST', { username: 'test' });
            if (!reg3.success || reg3.status === 400) {
                addResult('regResult', `✅ Test 3 PASSED: Missing fields correctly rejected`, 'success');
            } else {
                addResult('regResult', `❌ Test 3 FAILED: Missing fields not validated`, 'error');
            }

            addResult('regResult', '🏁 Registration test suite completed', 'info');
        }

        async function runLoginTests() {
            addResult('loginResult', '🧪 Starting login test suite...', 'info');
            
            // First register a test user
            const testUser = {
                username: 'login_test_' + Date.now(),
                email: 'login_' + Date.now() + '@example.com',
                password: 'LoginTest123!'
            };
            
            await makeRequest('/users/register', 'POST', testUser);
            
            // Test 1: Valid login
            const login1 = await makeRequest('/users/login', 'POST', {
                email: testUser.email,
                password: testUser.password
            });
            if (login1.success && login1.data.status) {
                addResult('loginResult', `✅ Test 1 PASSED: Valid login successful`, 'success');
            } else {
                addResult('loginResult', `❌ Test 1 FAILED: ${login1.data?.msg || login1.error}`, 'error');
            }

            // Test 2: Wrong password
            const login2 = await makeRequest('/users/login', 'POST', {
                email: testUser.email,
                password: 'wrongpassword'
            });
            if (login2.success && !login2.data.status && login2.data.msg.includes('Invalid credentials')) {
                addResult('loginResult', `✅ Test 2 PASSED: Wrong password correctly rejected`, 'success');
            } else {
                addResult('loginResult', `❌ Test 2 FAILED: Wrong password not handled properly`, 'error');
            }

            // Test 3: Non-existent user
            const login3 = await makeRequest('/users/login', 'POST', {
                email: '<EMAIL>',
                password: 'password'
            });
            if (login3.success && !login3.data.status) {
                addResult('loginResult', `✅ Test 3 PASSED: Non-existent user correctly rejected`, 'success');
            } else {
                addResult('loginResult', `❌ Test 3 FAILED: Non-existent user not handled properly`, 'error');
            }

            // Test 4: Missing fields
            const login4 = await makeRequest('/users/login', 'POST', { email: '<EMAIL>' });
            if (!login4.success || login4.status === 400) {
                addResult('loginResult', `✅ Test 4 PASSED: Missing password correctly rejected`, 'success');
            } else {
                addResult('loginResult', `❌ Test 4 FAILED: Missing password not validated`, 'error');
            }

            addResult('loginResult', '🏁 Login test suite completed', 'info');
        }

        async function runFullTestSuite() {
            // Clear previous results
            testResults = [];
            updateTestResults();
            
            addResult('testResults', '🚀 Starting comprehensive authentication test suite...', 'info');
            
            // Initialize system
            await getSystemInfo();
            await initializeDB();
            
            // Run all tests
            await runRegistrationTests();
            await runLoginTests();
            
            // System check
            await getAllUsers();
            
            addResult('testResults', '🎉 Full test suite completed! Check results above.', 'success');
        }

        // Initialize on page load
        window.onload = function() {
            addResult('testResults', '🌟 Upsocial Authentication Test Page Loaded', 'info');
            addResult('testResults', '📡 Backend should be running on http://localhost:4000', 'info');
        };
    </script>
</body>
</html>