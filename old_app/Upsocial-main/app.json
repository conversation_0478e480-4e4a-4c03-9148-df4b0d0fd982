{"expo": {"name": "Upsocial", "slug": "Upsocial", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/logo_icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "proxy": "https://upload63f6f713447ec.cloud.bunnyroute.com", "platforms": ["ios", "android", "web"], "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/logo_icon.png", "backgroundColor": "#ffffff"}, "permissions": ["READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "NOTIFICATIONS"]}, "web": {"favicon": "./assets/logo_icon.png", "build": {"offline": true}, "turboModules": true}, "extra": {"API_URL": "https://upload63f6f713447ec.cloud.bunnyroute.com/api"}, "plugins": [["expo-av", {"microphonePermission": "Allow Upsocial to access your microphone."}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends.", "cameraPermission": "The app accesses your videos to let you share them with your friends.", "MediaLibraryPermission": "The app accesses your videos to let you share them with your friends."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera."}], ["expo-screen-orientation", {"initialOrientation": "ALL"}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}]]}}