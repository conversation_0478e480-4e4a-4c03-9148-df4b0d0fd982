import React, { useRef, useEffect, useState } from 'react';
import { Text, Dimensions, PanResponder, Animated, StyleSheet, View, TouchableOpacity } from 'react-native';
import { ResizeMode, Video } from "expo-av";

const { width, height } = Dimensions.get('window');
const swipeThreshold = width / 2;
const outOfScreen = Math.sqrt(height * height + width * width);

export const Card = (props) => {
    const topVideo = useRef(null);
    const nextVideo = useRef(null);
    const [error, setError] = useState(false);
    const [retries, setRetries] = useState(0);
    const [videoHeight, setVideoHeight] = useState(height);
    const [videoWidth, setVideoWidth] = useState(width);
    const [isPlaying, setIsPlaying] = useState(true);
    const [isLoading, setIsLoading] = useState(true); // Added loading state

    const onLayout = event => {
        const { width, height } = event.nativeEvent.layout;
        setVideoWidth(width);
        setVideoHeight(height);
    };

    const loadVideo = async () => {
        try {
            if (props.profile && topVideo.current && props.currentProfileID === props.profile.ID) {
                await topVideo.current.loadAsync({ uri: props.profile.ipfsUrl });
                if (!isLoading) {
                    await topVideo.current.playAsync();
                }
            }

            // Preload the next video but don't play it
            if (!isLoading && props.nextProfile && nextVideo.current && props.nextProfileID === props.nextProfile.ID) {
                await nextVideo.current.loadAsync({ uri: props.nextProfile.ipfsUrl });
            }
        } catch (error) {
            console.error('Failed to load video: ', error);
            setError(true);
        }
    };

    const handleVideoPress = () => {
        if (isPlaying) {
            topVideo.current.pauseAsync();
        } else {
            topVideo.current.playAsync();
        }
        setIsPlaying(!isPlaying);
    };

    useEffect(() => {
        if (error && retries < 3) {
            setTimeout(() => {
                console.log('Retrying video load'); // Log when a retry is happening
                loadVideo();
                setRetries(retries + 1);
            }, 9000);
        } else if (retries === 4) {
            props.skipToNextVideo();
        }
    }, [error, retries]);

    useEffect(() => {
        loadVideo();
    }, [topVideo, props.currentProfileID, nextVideo, props.nextProfileID]);

    const pan = useRef(new Animated.ValueXY()).current;
    const cardPanResponder = PanResponder.create({
        onStartShouldSetPanResponder: () => true,
        onPanResponderMove: Animated.event(
            [null, { dx: pan.x, dy: pan.y }],
            { useNativeDriver: false }
        ),
        onMoveShouldSetPanResponderCapture: () => true,
        onPanResponderRelease: (_event, { dx }) => {
            const absDx = Math.abs(dx);
            const direction = absDx / dx;

            if (absDx > swipeThreshold) {
                Animated.spring(pan, {
                    toValue: { x: outOfScreen * direction, y: 0 },
                    useNativeDriver: false,
                }).start(() => props.onSwipeOff(props.profile, direction));
            } else {
                Animated.spring(pan, {
                    toValue: { x: 0, y: 0 },
                    friction: 4.5,
                    useNativeDriver: false,
                }).start();
            }
        },
    });

    const rotateCard = pan.x.interpolate({
        inputRange: [-200, 0, 200],
        outputRange: ['10deg', '0deg', '-10deg'],
    });

    const animatedStyle = {
        transform: [
            { translateX: pan.x },
            { translateY: pan.y },
            { rotate: rotateCard },
        ],
    };

    const likeOpacity = pan.x.interpolate({
        inputRange: [-width / 2, 0, width / 2],
        outputRange: [0, 0, 1],
        extrapolate: 'clamp'
    });

    const nopeOpacity = pan.x.interpolate({
        inputRange: [-width / 2, 0, width / 2],
        outputRange: [1, 0, 0],
        extrapolate: 'clamp'
    });

    return (
        <Animated.View
            {...cardPanResponder.panHandlers}
            style={[styles.card, animatedStyle]}
            onLayout={onLayout}
        >
            <Animated.View
                style={{
                    opacity: likeOpacity,
                    transform: [{ rotate: "-30deg" }],
                    position: "absolute",
                    top: 70,
                    left: 40,
                    zIndex: 1000
                }}
            >
                <Text
                    style={{
                        borderWidth: 1,
                        borderColor: "green",
                        color: "green",
                        fontSize: 38,
                        fontWeight: "900",
                        padding: 10
                    }}
                >
                    UP
                </Text>
            </Animated.View>
            <Animated.View
                style={{
                    opacity: nopeOpacity,
                    transform: [{ rotate: "30deg" }],
                    position: "absolute",
                    top: 70,
                    right: 40,
                    zIndex: 1000
                }}
            >
                <Text
                    style={{
                        borderWidth: 1,
                        borderColor: "red",
                        color: "red",
                        fontSize: 38,
                        fontWeight: "900",
                        padding: 10
                    }}
                >
                    Down
                </Text>
            </Animated.View>
            <TouchableOpacity activeOpacity={1} onPress={handleVideoPress}>
  {props.profile && props.profile.ipfsUrl ? (
    <Video
      ref={topVideo}
      style={{ width: videoWidth, height: videoHeight, overflow: "hidden" }}
      videoStyle={{ width: "100%", height: "100%" }}
      source={{ uri: props.profile.ipfsUrl }}
      rate={1.0}
      volume={1.0}
      resizeMode={ResizeMode.CONTAIN}
      useNativeControls={false}
      isLooping
      shouldPlay={props.currentProfileID === props.profile.ID}
      onPlaybackStatusUpdate={(status) => {
        if (status.error) {
          console.error(status.error);
          setError(true);
        } else {
          setError(false);
          props.setTopCardVideos(topVideo);
        }
        setIsLoading(status.isBuffering);
      }}
    />
  ) : null}

  {props.nextProfile && props.nextProfile.ipfsUrl ? (
    <Video
      ref={nextVideo}
      style={{ width: 0, height: 0, position: 'absolute' }}
      source={{ uri: props.nextProfile.ipfsUrl }}
      onPlaybackStatusUpdate={(status) => {
        if (status.error) {
          console.error(status.error);
          setError(true);
        } else {
          setError(false);
        }
      }}
    />
  ) : null}
</TouchableOpacity>
        </Animated.View>
    );
};

const styles = StyleSheet.create({
    card: {
        position: 'absolute',
        width: "100%",
        height: "100%",
        overflow: 'hidden',
        backgroundColor: '#000',
        margin: 1,
        borderWidth: 0,
        borderColor: 'lightgrey',
        borderRadius: 8,
    },
});

export default Card;
