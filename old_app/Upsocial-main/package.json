{"name": "Upsocial", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "npm i expo && npx expo export:web", "homepage": "./", "scope": "./"}, "dependencies": {"@expo/match-media": "^0.4.0", "@expo/webpack-config": "^18.0.4", "@ffprobe-installer/ffprobe": "^2.0.0", "@rajesh896/video-thumbnails-generator": "^2.3.9", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/slider": "^4.4.2", "@react-navigation/drawer": "^6.4.1", "@react-navigation/material-top-tabs": "^6.2.1", "@react-navigation/native": "^6.0.10", "@react-navigation/stack": "^6.2.1", "axios": "^1.3.5", "country-state-city": "^3.1.4", "email-validator": "^2.0.4", "expo": "^51.0.8", "expo-av": "^13.2.1", "expo-camera": "^13.2.1", "expo-checkbox": "^2.3.1", "expo-document-picker": "^11.2.2", "expo-image-picker": "^14.1.1", "expo-linear-gradient": "^12.1.2", "expo-screen-orientation": "^5.1.1", "expo-sharing": "~11.2.2", "expo-status-bar": "~1.4.4", "expo-video-player": "^2.0.0", "expo-video-thumbnails": "^7.2.1", "offline-plugin": "^0.0.1", "qrcode.react": "^3.1.0", "react": "18.2.0", "react-confirm-alert": "^2.8.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "18.2.0", "react-email-validator": "^1.0.2", "react-hot-toast": "^2.4.1", "react-native": "0.71.6", "react-native-element-dropdown": "^2.9.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.9.0", "react-native-modal": "^13.0.1", "react-native-multiple-select": "^0.5.12", "react-native-reanimated": "^2.14.4", "react-native-safe-area-context": "^4.5.0", "react-native-screens": "^3.20.0", "react-native-select-dropdown": "^3.3.3", "react-native-swipe-gestures": "^1.0.5", "react-native-swipe-list-view": "^3.2.9", "react-native-table-component": "^1.2.2", "react-native-vector-icons": "^9.2.0", "react-native-video": "^5.2.1", "react-native-virtualized-view": "^1.0.0", "react-native-web": "~0.18.10", "react-native-webview": "^11.26.0", "react-redux": "^8.0.5", "react-responsive": "^9.0.2", "react-tinder-card": "^1.6.2", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "serve": "^14.2.0", "workbox-background-sync": "^6.5.4", "workbox-broadcast-update": "^6.5.4", "workbox-cacheable-response": "^6.5.4", "workbox-core": "^6.5.4", "workbox-expiration": "^6.5.4", "workbox-google-analytics": "^6.5.4", "workbox-navigation-preload": "^6.5.4", "workbox-precaching": "^6.5.4", "workbox-range-requests": "^6.5.4", "workbox-routing": "^6.5.4", "workbox-strategies": "^6.5.4", "workbox-streams": "^6.5.4", "workbox-webpack-plugin": "^6.5.4"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}