# ************************************
#       Bunnyshell Nginx Server      *
# ************************************
server {
  listen 443 ssl;
  listen [::]:443 ssl;

  ssl_certificate /etc/letsencrypt/live/upload63f6f713447ec.cloud.bunnyroute.com/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/upload63f6f713447ec.cloud.bunnyroute.com/privkey.pem;
  
  server_name upload63f6f713447ec.cloud.bunnyroute.com;

  # access directives
  #tes
  root /var/www/upload/app;
  
  location /api {
    proxy_pass http://localhost:4000;
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
    proxy_set_header X-Real-IP $remote_addr;
    allow all;
    if ($http_origin ~* "^https://throbbing-thunder-0577.on.fleek.co$") {
      add_header Access-Control-Allow-Origin "$http_origin";
      add_header Access-Control-Allow-Methods "OPTIONS, DELETE, POST, GET, PATCH, PUT";
      add_header Access-Control-Max-Age "3600";
      add_header Access-Control-Allow-Credentials "true";
      add_header Access-Control-Allow-Headers "Content-Type";
    }
  }

  location / {
    # No php is touched for static content.
    # Include the "?$args" part so non-default permalinks
    #   doesn't break when using query string
    try_files $uri $uri/ /index.php?$args;
  }

  location = /robots.txt {
    allow all;
    log_not_found off;
    access_log off;
  }

  location ~/\.ht {
    deny all;
  }

  index index.php;

  location ~ \.php$ {
    #NOTE: You should have "cgi.fix_pathinfo = 0;" in php.ini
    include /etc/nginx/fastcgi_params;
    fastcgi_intercept_errors on;
    # fastcgi_pass 127.0.0.1:9000;
    fastcgi_pass unix:/var/run/php/8.0_user_upload-fpm.sock;
  }

  location /.well-known/acme-challenge/ {
    root /var/www/upload/app;
    try_files $uri =404;
  }

  # Expire rules for static content
  location ~* \.(?:manifest|appcache|html?|xml|json)$ {
    add_header Cache-Control "max-age=0";
  }

  location ~* \.(?:rss|atom)$ {
    add_header Cache-Control "max-age=3600";
  }

  location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|mp4|ogg|ogv|webm|htc)$ {
    add_header Cache-Control "max-age=2592000";
    access_log off;
  }

  location ~* \.(?:css|js)$ {
    add_header Cache-Control "max-age=31536000";
    access_log off;
  }

  location ~* \.(?:ttf|ttc|otf|eot|woff|woff2)$ {
    add_header Cache-Control "max-age=2592000";
    access_log off;
  }

  

  # Gzip compression
  gzip on;
  gzip_comp_level 5;
  gzip_min_length 256;
  gzip_proxied any;
  gzip_vary on;
  gzip_types
    application/atom+xml
    application/javascript
    application/json
    application/ld+json
    application/manifest+json
    application/rss+xml
    application/vnd.geo+json
    application/vnd.ms-fontobject
    application/x-font-ttf
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/opentype
    image/bmp
    image/svg+xml
    image/x-icon
    text/cache-manifest
    text/css
    text/plain
    text/vcard
    text/vnd.rim.location.xloc
    text/vtt
    text/x-component
    text/x-cross-domain-policy;

  access_log /var/log/nginx/https_upload63f6f713447ec.cloud.bunnyroute.com.access.log;
  error_log /var/log/nginx/https_upload63f6f713447ec.cloud.bunnyroute.com.error.log;
}

