{"name": "Upsocial Backend", "version": "1.0.0", "description": "This is Upsocial backend System", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^7.7.0", "axios": "^1.3.6", "bcryptjs": "^2.4.3", "body-parser": "^1.20.1", "child_process": "^1.0.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.0.3", "encrypt-string": "^1.0.0", "ethers": "^5.7.2", "express": "^4.18.2", "express-session": "^1.17.3", "file-size": "^1.0.0", "fs": "^0.0.1-security", "https": "^1.0.0", "ipfs": "^0.44.0", "ipfs-http-client": "^47.0.1", "js-kubo-rpc-client": "^1.0.0", "js-recommender": "^1.0.5", "jsonwebtoken": "^8.5.1", "kubo-rpc-client": "^3.0.1", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^1.4.1", "multer-s3": "^2.9.0", "node-cron": "^3.0.2", "nodemailer": "^6.9.2", "nodemailer-direct-transport": "^3.3.2", "nodemailer-smtp-transport": "^2.7.4", "nodemon": "^2.0.20", "orbit-db": "^0.24.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "web3": "^1.8.1"}, "type": "commonjs"}