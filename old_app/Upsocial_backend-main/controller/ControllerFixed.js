require('dotenv').config();

// Safe imports with fallbacks
let OrbitDB, IPFS;
let isOrbitDBAvailable = false;

try {
    OrbitDB = require("orbit-db");
    IPFS = require("ipfs");
    isOrbitDBAvailable = true;
    console.log("✅ OrbitDB modules loaded successfully");
} catch (error) {
    console.log("❌ OrbitDB modules failed to load:", error.message);
    console.log("🔄 Using fallback mode...");
}

// Always use fallback encryption to avoid WASM issues
const crypto = require('crypto');

function encryptString(text, password) {
    const cipher = crypto.createCipher('aes256', password);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

function decryptString(encryptedText, password) {
    const decipher = crypto.createDecipher('aes256', password);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

const axios = require('axios');
const { exec } = require('node:child_process');
const fs = require('fs');
const filesize = require("file-size");
const nodemailer = require('nodemailer');
const smtpTransport = require('nodemailer-smtp-transport');

// recommendation system configure value (only if available)
let jsrecommender, recommender, table;
try {
    jsrecommender = require("js-recommender");
    recommender = new jsrecommender.Recommender();
    table = new jsrecommender.Table();
} catch (error) {
    console.log("⚠️ js-recommender not available, skipping...");
}

let converting = [];

const generateRandomString = length => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
};

const items = [
    { id: 1, name: 'Animation' },
    { id: 2, name: 'Autos & Vehicles' },
    { id: 3, name: 'Beauty & Fashion' },
    { id: 4, name: 'Comedy' },
    { id: 5, name: 'Cooking & Food' },
    { id: 6, name: 'DIY & Crafts' },
    { id: 7, name: 'Documentary' },
    { id: 8, name: 'Education' },
    { id: 9, name: 'Entertainment' },
    { id: 10, name: 'Film & Animation' },
    { id: 11, name: 'Gaming' },
    { id: 12, name: 'Health & Fitness' },
    { id: 13, name: 'How-to & Style' },
    { id: 14, name: 'Kids & Family' },
    { id: 15, name: 'Music' },
    { id: 16, name: 'News & Politics' },
    { id: 17, name: 'Nonprofits & Activism' },
    { id: 18, name: 'People & Blogs' },
    { id: 19, name: 'Pets & Animals' },
    { id: 20, name: 'Science & Technology' },
    { id: 21, name: 'Sports' },
    { id: 22, name: 'Travel & Events' },
    { id: 23, name: 'Unboxing & Reviews' },
    { id: 24, name: 'Blogs' },
];

let ipfs;
let orbitdb;

// Database references
let userDataDB; 
let contentDB; 
let channelDB; 
let playlistDB; 
let anonymouseDB; 

// Fallback in-memory storage
let users = [];
let userIdCounter = 0;

let hashHistories = [];

let ENCRYPT_PASS = process.env.ENCRYPT_PASS || "upsocial";

/////////////////////////////////////////////////////////////////////////
// Database Management
/////////////////////////////////////////////////////////////////////////

exports.CreateDBs = async (req, res) => {
    if (!isOrbitDBAvailable) {
        return res.status(200).json({
            msg: "Running in simple mode - in-memory database initialized",
            status: true,
            mode: "simple"
        });
    }
    
    if (userDataDB == undefined && contentDB == undefined && channelDB == undefined && playlistDB == undefined && anonymouseDB == undefined) {
        try {
            ipfs = await IPFS.create({
                EXPERIMENTAL: {
                    pubsub: true,
                },
                repo: "UpsocialRepo"
            });
            console.log("OrbitDB: IPFS instance created");

            orbitdb = await OrbitDB.createInstance(ipfs, {});
            console.log("OrbitDB: OrbitDB instance created");

            userDataDB = await orbitdb.kvstore("userDB", { overwrite: true });
            await userDataDB.load();
            console.log("OrbitDB: User database created");

            contentDB = await orbitdb.kvstore("contentDB", { overwrite: true });
            await contentDB.load();
            console.log("OrbitDB: Content database created");

            channelDB = await orbitdb.kvstore("channelDB", { overwrite: true });
            await channelDB.load();
            console.log("OrbitDB: Channel database created");

            playlistDB = await orbitdb.kvstore("playlistDB", { overwrite: true });
            await playlistDB.load();
            console.log("OrbitDB: Playlist database created");

            anonymouseDB = await orbitdb.kvstore("anonymouseDB", { overwrite: true });
            await anonymouseDB.load();
            console.log("OrbitDB: Anonymous database created");

            return res.status(200).json({
                msg: "OrbitDB databases created successfully",
                status: true,
                mode: "orbitdb"
            });
        } catch (error) {
            console.error("OrbitDB initialization failed:", error);
            return res.status(500).json({
                msg: "Database creation failed: " + error.message,
                status: false
            });
        }
    } else {
        return res.status(200).json({
            msg: "OrbitDB databases already exist",
            status: true,
            mode: "orbitdb"
        });
    }
};

/////////////////////////////////////////////////////////////////////////
// User Authentication
/////////////////////////////////////////////////////////////////////////

exports.userRegister = async (req, res) => {
    const username = req.body.username;
    const email = req.body.email;
    const password = req.body.password;

    if (!username || !email || !password) {
        return res.status(400).json({ 
            msg: "Username, email, and password are required", 
            status: false 
        });
    }

    try {
        const encrypted_password = encryptString(password, ENCRYPT_PASS);

        if (isOrbitDBAvailable && userDataDB != undefined) {
            // Use OrbitDB
            const curUsers = userDataDB.all;
            let userId = Object.keys(curUsers).length;
            let userTable = Object.values(curUsers);
            let userEmailTable = [];
            let userNameTable = [];
            let userExist = false;
            let userNameExist = false;

            if (userId > 0) {
                for (var i = 0; i < userTable.length; i++) {
                    userEmailTable.push(userTable[i]["email"]);
                    userNameTable.push(userTable[i]["username"]);
                }

                for (var i = 0; i < userEmailTable.length; i++) {
                    if (userEmailTable[i] == email) {
                        userExist = true
                    }
                }

                for (var i = 0; i < userNameTable.length; i++) {
                    if (userNameTable[i] == username) {
                        userNameExist = true
                    }
                }

                if (!userExist && !userNameExist) {
                    await userDataDB.put(userId, { 
                        ID: userId, username: username, email: email, password: encrypted_password, 
                        status: true, handle: "", description: "", location: "", photo: "", 
                        following: [], followers: [], Liked: [], Disliked: [], History: [] 
                    });
                    return res.status(200).json({ msg: `${email} is registered successfully!`, status: true });
                } else if (userExist) {
                    return res.status(200).json({ msg: `${email} is already registered!`, status: false });
                } else {
                    return res.status(200).json({ msg: `${username} is already taken!`, status: false });
                }
            } else {
                await userDataDB.put(userId, { 
                    ID: userId, username: username, email: email, password: encrypted_password, 
                    status: true, handle: "", description: "", location: "", photo: "", 
                    following: [], followers: [], Liked: [], Disliked: [], History: [] 
                });
                return res.status(200).json({ msg: `${email} is registered successfully!`, status: true });
            }
        } else {
            // Use in-memory storage
            const existingUser = users.find(user => user.email === email);
            if (existingUser) {
                return res.status(200).json({ 
                    msg: `${email} is already registered!`, 
                    status: false 
                });
            }

            const existingUsername = users.find(user => user.username === username);
            if (existingUsername) {
                return res.status(200).json({ 
                    msg: `${username} is already taken!`, 
                    status: false 
                });
            }

            const newUser = {
                ID: userIdCounter++,
                username: username,
                email: email,
                password: encrypted_password,
                status: true,
                handle: "",
                description: "",
                location: "",
                photo: "",
                following: [],
                followers: [],
                Liked: [],
                Disliked: [],
                History: []
            };

            users.push(newUser);
            return res.status(200).json({ 
                msg: `${email} is registered successfully!`, 
                status: true 
            });
        }
    } catch (error) {
        console.error('Registration error:', error);
        return res.status(500).json({ 
            msg: "Registration failed: " + error.message, 
            status: false 
        });
    }
};

exports.userLogin = async (req, res) => {
    const email = req.body.email;
    const password = req.body.password;

    if (!email || !password) {
        return res.status(400).json({ 
            msg: "Email and password are required", 
            status: false 
        });
    }

    try {
        if (isOrbitDBAvailable && userDataDB != undefined) {
            // Use OrbitDB
            const curUsers = userDataDB.all;
            let userTable = Object.values(curUsers);

            if (userTable.length > 0) {
                let userAuth = false;
                let userId = 0;

                for (var i = 0; i < userTable.length; i++) {
                    if (userTable[i]["email"] == email && userTable[i]["status"]) {
                        const decrypted_password = decryptString(userTable[i]["password"], ENCRYPT_PASS);
                        if (decrypted_password == password) {
                            userAuth = true;
                            userId = userTable[i]["ID"];
                        } else {
                            userAuth = false;
                        }
                    }
                }

                if (!userAuth) {
                    return res.status(200).json({ msg: `Invalid credentials`, status: false });
                } else {
                    const responseData = userDataDB.get(userId);
                    return res.status(200).json({ 
                        msg: `Login successful!`, 
                        status: true, 
                        curUser: email, 
                        Data: responseData 
                    });
                }
            } else {
                return res.status(200).json({ msg: `No users found!`, status: false });
            }
        } else {
            // Use in-memory storage
            const user = users.find(u => u.email === email && u.status === true);
            if (!user) {
                return res.status(200).json({ 
                    msg: "Invalid credentials", 
                    status: false 
                });
            }

            const decrypted_password = decryptString(user.password, ENCRYPT_PASS);
            if (decrypted_password !== password) {
                return res.status(200).json({ 
                    msg: "Invalid credentials", 
                    status: false 
                });
            }

            const userData = { ...user };
            delete userData.password;

            return res.status(200).json({ 
                msg: "Login successful!", 
                status: true, 
                curUser: email, 
                Data: userData 
            });
        }
    } catch (error) {
        console.error('Login error:', error);
        return res.status(500).json({ 
            msg: "Login failed: " + error.message, 
            status: false 
        });
    }
};

/////////////////////////////////////////////////////////////////////////
// Admin Functions
/////////////////////////////////////////////////////////////////////////

exports.getAllUsers = async (req, res) => {
    try {
        if (isOrbitDBAvailable && userDataDB != undefined) {
            const curUsers = userDataDB.all;
            const userTable = Object.values(curUsers);
            const publicUsers = userTable.map(user => {
                const { password, ...publicData } = user;
                return publicData;
            });

            return res.status(200).json({
                msg: "Users retrieved successfully",
                status: true,
                users: publicUsers,
                count: userTable.length,
                mode: "orbitdb"
            });
        } else {
            const publicUsers = users.map(user => {
                const { password, ...publicData } = user;
                return publicData;
            });

            return res.status(200).json({
                msg: "Users retrieved successfully",
                status: true,
                users: publicUsers,
                count: users.length,
                mode: "simple"
            });
        }
    } catch (error) {
        console.error('Get users error:', error);
        return res.status(500).json({ 
            msg: "Failed to retrieve users: " + error.message, 
            status: false 
        });
    }
};

// Placeholder functions for other endpoints that aren't critical for authentication
exports.getUsersByEmail = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.removeUser = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.get20HashCode = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.getNameHashCode = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.verify20HashCode = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.editUser = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.resetPassword = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.verifyCode = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.setNewPassword = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.changeUserStatus = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.followUser = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.unfollowUser = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.changeContentStatus = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.likeContent = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.likeVideos = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.dislikeContent = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.dislikeVideos = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.personalized = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.Web_uploadContent = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.remove_uploadedContent = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.setHistory = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.getHistory = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.setWatched = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.GetUploadedContent = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.GetUploadedContentByCategory = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.GetAllUploadedContent = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.generateIPFS = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.uploadPhoto = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.createChannel = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.getAllChannels = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.getChannelByUser = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.followChannel = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.unFollowChannel = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.uploadContentsChannel = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.createPlaylist = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.removePlaylist = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.getPlaylist = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.addVideoToPlaylist = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.removeVideoToPlaylist = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};

exports.getAllVideoFromPlaylist = async (req, res) => {
    return res.status(501).json({ msg: "Not implemented in current mode", status: false });
};