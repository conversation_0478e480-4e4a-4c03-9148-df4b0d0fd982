#!/bin/bash

# Demonstration script showing the authentication system working
echo "🚀 Upsocial Authentication System Demonstration"
echo "================================================"
echo ""

echo "📡 Backend Status:"
curl -s http://localhost:4000/api/Upsocial/system/info | jq -r '.message + " v" + .version'

echo ""
echo "👥 Current Users in System:"
curl -s http://localhost:4000/api/Upsocial/admin/getAllUsers | jq -r '.count + " users registered"'

echo ""
echo "📝 Testing New User Registration..."
TIMESTAMP=$(date +%s)
REG_RESULT=$(curl -s -X POST http://localhost:4000/api/Upsocial/users/register \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"demo_${TIMESTAMP}\", \"email\": \"demo_${TIMESTAMP}@test.com\", \"password\": \"DemoPass123!\"}")

echo "Registration Result: $(echo $REG_RESULT | jq -r '.msg')"

if echo $REG_RESULT | jq -r '.status' | grep -q "true"; then
    echo "✅ Registration successful!"
    
    echo ""
    echo "🔐 Testing Login with new user..."
    LOGIN_RESULT=$(curl -s -X POST http://localhost:4000/api/Upsocial/users/login \
      -H "Content-Type: application/json" \
      -d "{\"email\": \"demo_${TIMESTAMP}@test.com\", \"password\": \"DemoPass123!\"}")
    
    echo "Login Result: $(echo $LOGIN_RESULT | jq -r '.msg')"
    
    if echo $LOGIN_RESULT | jq -r '.status' | grep -q "true"; then
        echo "✅ Login successful!"
        echo "📊 User Data Retrieved: $(echo $LOGIN_RESULT | jq -r '.Data.username + " (" + .Data.email + ")"')"
    else
        echo "❌ Login failed"
    fi
else
    echo "❌ Registration failed"
fi

echo ""
echo "🎉 Authentication system fully functional!"
echo "🌐 Interactive test interface available at: http://localhost:3000"
echo "📋 Full test results available in: TEST_RESULTS.md"