#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

const server = http.createServer((req, res) => {
    if (req.url === '/' || req.url === '/index.html') {
        // Serve the test HTML file
        const filePath = path.join(__dirname, 'test-auth.html');
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(404);
                res.end('File not found');
                return;
            }
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
        });
    } else if (req.url === '/favicon.ico') {
        res.writeHead(204);
        res.end();
    } else {
        res.writeHead(404);
        res.end('Page not found');
    }
});

server.listen(PORT, () => {
    console.log(`🌐 Test server running at http://localhost:${PORT}`);
    console.log(`📊 Open your browser and visit the URL above to see the interactive test interface`);
    console.log(`🔧 Make sure your backend is running on http://localhost:4000`);
    console.log(`🛑 Press Ctrl+C to stop the server`);
});