# UpSocial Helia - Quick Start Development Guide

## Current Status & Next Steps

### ✅ What's Already Built
We have a solid foundation with:
- **Authentication**: SimpleAuthModal with login/signup forms
- **Upload System**: VideoUploadModal with progress tracking and file validation
- **Feed Layout**: CategoryTabs and VideoCard components with dark theme
- **Basic Infrastructure**: Next.js 15, TypeScript, Tailwind CSS, Helia IPFS

### 🎯 Immediate Priority: Complete Core Video Platform

#### Step 1: IPFS Video Storage Integration (Start Here!)
**Goal**: Make video uploads actually work with IPFS storage

**Create**: `src/lib/ipfs/video-storage.ts`
```typescript
import { createHelia } from 'helia'
import { unixfs } from '@helia/unixfs'

export class VideoStorageService {
  private helia: any
  private fs: any
  
  async initialize() {
    this.helia = await createHelia({
      // Add bootstrap nodes and configuration
    })
    this.fs = unixfs(this.helia)
  }
  
  async uploadVideo(file: File): Promise<string> {
    const bytes = new Uint8Array(await file.arrayBuffer())
    const cid = await this.fs.addFile({ content: bytes })
    return cid.toString()
  }
  
  async getVideo(cid: string): Promise<Blob> {
    const chunks = []
    for await (const chunk of this.fs.cat(cid)) {
      chunks.push(chunk)
    }
    return new Blob(chunks, { type: 'video/mp4' })
  }
}
```

**Update**: `src/components/upload/VideoUploadModal.tsx`
```typescript
// Replace mock upload with real IPFS upload
const handleSubmit = async () => {
  const storageService = new VideoStorageService()
  await storageService.initialize()
  
  const videoCID = await storageService.uploadVideo(selectedFile)
  
  // Store video metadata
  const videoData = {
    id: Date.now().toString(),
    title,
    description,
    category,
    ipfsHash: videoCID,
    // ... other metadata
  }
  
  // Add to videos list (temporary until we have database)
  const existingVideos = JSON.parse(localStorage.getItem('videos') || '[]')
  existingVideos.push(videoData)
  localStorage.setItem('videos', JSON.stringify(existingVideos))
}
```

#### Step 2: Video Player Component
**Create**: `src/components/video/VideoPlayer.tsx`
```typescript
'use client'

import { useEffect, useRef, useState } from 'react'
import { VideoStorageService } from '@/lib/ipfs/video-storage'

interface VideoPlayerProps {
  videoCID: string
  autoplay?: boolean
  controls?: boolean
  className?: string
}

export default function VideoPlayer({ videoCID, autoplay = false, controls = true, className }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const loadVideo = async () => {
      try {
        const storageService = new VideoStorageService()
        await storageService.initialize()
        
        const videoBlob = await storageService.getVideo(videoCID)
        const url = URL.createObjectURL(videoBlob)
        setVideoUrl(url)
        setLoading(false)
      } catch (error) {
        console.error('Failed to load video:', error)
        setLoading(false)
      }
    }
    
    if (videoCID) {
      loadVideo()
    }
  }, [videoCID])
  
  if (loading) {
    return (
      <div className={`bg-gray-800 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-white">Loading video...</div>
      </div>
    )
  }
  
  return (
    <video
      ref={videoRef}
      src={videoUrl}
      autoPlay={autoplay}
      controls={controls}
      className={`w-full h-full object-cover rounded-lg ${className}`}
      onError={() => console.error('Video playback error')}
    />
  )
}
```

#### Step 3: Enhanced Video Feed
**Update**: `src/app/page.tsx` to load real videos from localStorage
```typescript
// Add state to load videos from storage
const [videos, setVideos] = useState<Video[]>([])

useEffect(() => {
  // Load videos from localStorage (temporary storage)
  const storedVideos = JSON.parse(localStorage.getItem('videos') || '[]')
  setVideos(storedVideos)
}, [])
```

**Update**: `src/components/video/VideoCard.tsx` to use VideoPlayer
```typescript
import VideoPlayer from './VideoPlayer'

// Replace static thumbnail with actual video player
{video.ipfsHash ? (
  <VideoPlayer
    videoCID={video.ipfsHash}
    autoplay={false}
    controls={true}
    className="w-full h-48"
  />
) : (
  <img 
    src={video.thumbnail} 
    alt={video.title}
    className="w-full h-48 object-cover"
  />
)}
```

#### Step 4: Test the Upload-to-Feed Pipeline
1. **Upload a video** using VideoUploadModal
2. **Verify IPFS storage** - check if CID is generated
3. **See video in feed** - refresh page and verify video appears
4. **Test playback** - click on video and verify it plays

### 🔧 Development Commands

```bash
# Start development server
npm run dev

# Check for TypeScript errors
npm run build

# Run linting
npm run lint
```

### 🐛 Common Issues & Solutions

#### Issue: IPFS Connection Fails
**Solution**: Check browser console for errors. IPFS might need WebRTC or WebSocket configuration.
```typescript
// Add to video-storage.ts initialization
this.helia = await createHelia({
  libp2p: {
    addresses: {
      listen: ['/webrtc']
    }
  }
})
```

#### Issue: Video Upload Takes Too Long
**Solution**: Add progress feedback and consider compression
```typescript
// In VideoUploadModal, add progress tracking for IPFS upload
const uploadWithProgress = async (file: File) => {
  const chunks = Math.ceil(file.size / (1024 * 1024)) // 1MB chunks
  for (let i = 0; i < chunks; i++) {
    // Upload in chunks and update progress
    setUploadProgress((i / chunks) * 100)
  }
}
```

#### Issue: Videos Don't Play
**Solution**: Check video format and add fallback
```typescript
// Add error handling in VideoPlayer
const [error, setError] = useState<string>('')

const handleVideoError = () => {
  setError('Video format not supported')
}

// In JSX:
{error && <div className="text-red-500">{error}</div>}
```

### 📝 Testing Checklist

Before moving to next features:
- [ ] Can upload a video file
- [ ] Upload shows progress bar
- [ ] Video gets stored in IPFS (check console for CID)
- [ ] Video appears in feed after upload
- [ ] Video plays when clicked
- [ ] Upload form resets after successful upload
- [ ] Error handling works for failed uploads
- [ ] Dark theme is consistent across all components

### 🎯 Next Features to Add (After Core Works)

1. **User Authentication Integration**
   - Connect upload modal to user login
   - Show "Login to Upload" for anonymous users

2. **Video Metadata Enhancement**
   - Add thumbnail generation
   - Store video duration and file size
   - Add creator information

3. **Search Functionality**
   - Basic text search across titles
   - Category filtering
   - Sort by date/popularity

4. **Swipe Gestures**
   - Implement full-screen video modal
   - Add swipe left/right for like/dislike
   - Touch gesture support

### 💡 Pro Tips

1. **Use Browser DevTools**: Check Application > Local Storage to see stored videos
2. **IPFS Gateway**: Test CIDs directly in browser: `https://ipfs.io/ipfs/YOUR_CID`
3. **Mobile Testing**: Use Chrome DevTools device emulation for mobile UI
4. **Performance**: Use React DevTools Profiler to check component re-renders

### 📚 Resources

- **Helia Documentation**: https://helia.io/
- **IPFS Gateway List**: https://ipfs.github.io/public-gateway-checker/
- **Video Processing**: Look into Web APIs for video compression
- **PWA Features**: Consider service worker for offline support

---

**Start with Step 1 (IPFS Integration) and work through each step sequentially. Each step builds on the previous one and should result in a working feature before moving to the next.**

Need help with any specific step? Check the full REQUIREMENTS.md and IMPLEMENTATION_ROADMAP.md for detailed technical specifications.
