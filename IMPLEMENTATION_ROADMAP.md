# UpSocial Helia - Implementation Roadmap

## Overview

This document provides the technical implementation roadmap for building UpSocial Helia based on our requirements specification. It maps directly to our existing Next.js/Helia codebase and provides concrete steps for development.

## Current Implementation Status

### ✅ Completed Components
1. **SimpleAuthModal** (`src/components/auth/SimpleAuthModal.tsx`)
   - Login/signup forms with toggle
   - Dark theme styling
   - AuthState integration
   - Form validation

2. **VideoUploadModal** (`src/components/upload/VideoUploadModal.tsx`)
   - File drag & drop interface
   - Upload progress tracking
   - Category selection
   - Form validation with CSS modules

3. **CategoryTabs** (`src/components/feed/CategoryTabs.tsx`)
   - Horizontal scrollable tabs
   - Dark theme conversion
   - Smooth navigation
   - 24 category support

4. **VideoCard** (`src/components/video/VideoCard.tsx`)
   - Dark theme video cards
   - Swipe gesture hooks integration
   - Video metadata display

5. **Page Layout** (`src/app/page.tsx`)
   - Responsive masonry grid
   - Modal integration
   - Featured video section

### 🔄 In Progress
1. **IPFS Integration**: Helia configured but not fully integrated
2. **Swipe Gestures**: useSwipeGestures hook exists but needs full implementation
3. **Video Playback**: Basic structure exists, needs optimization

### ❌ Not Started
1. **User Management System**
2. **Content Search & Discovery**
3. **Video Processing Pipeline**
4. **Performance Optimization**

## Implementation Phases

### Phase 1: Core Video Platform (Current Sprint - 2 weeks)

#### Week 1: IPFS Video Storage Integration

**Day 1-2: Helia Video Upload**
```typescript
// File: src/lib/ipfs/video-storage.ts
import { createHelia } from 'helia'
import { unixfs } from '@helia/unixfs'

export class VideoStorageService {
  private helia: any
  private fs: any
  
  async initialize() {
    this.helia = await createHelia()
    this.fs = unixfs(this.helia)
  }
  
  async uploadVideo(file: File): Promise<string> {
    const bytes = new Uint8Array(await file.arrayBuffer())
    const cid = await this.fs.addFile({ content: bytes })
    return cid.toString()
  }
  
  async uploadMetadata(metadata: VideoMetadata): Promise<string> {
    const json = JSON.stringify(metadata)
    const bytes = new TextEncoder().encode(json)
    const cid = await this.fs.addFile({ content: bytes })
    return cid.toString()
  }
  
  async getVideo(cid: string): Promise<Blob> {
    const chunks = []
    for await (const chunk of this.fs.cat(cid)) {
      chunks.push(chunk)
    }
    return new Blob(chunks)
  }
}
```

**Day 3-4: Update VideoUploadModal Integration**
```typescript
// Update: src/components/upload/VideoUploadModal.tsx
import { VideoStorageService } from '@/lib/ipfs/video-storage'

const handleSubmit = async () => {
  // ... existing validation
  
  const storageService = new VideoStorageService()
  await storageService.initialize()
  
  // Upload video to IPFS
  const videoCID = await storageService.uploadVideo(selectedFile)
  
  // Generate and upload thumbnail
  const thumbnail = await generateThumbnail(selectedFile)
  const thumbnailCID = await storageService.uploadVideo(thumbnail)
  
  // Create metadata
  const metadata = {
    title,
    description,
    category,
    duration: await getVideoDuration(selectedFile),
    size: selectedFile.size,
    createdAt: new Date().toISOString(),
    videoCID,
    thumbnailCID
  }
  
  const metadataCID = await storageService.uploadMetadata(metadata)
  
  // Save to local state/database
  await saveVideoToApp(metadataCID, metadata)
}
```

**Day 5: Video Playback Component**
```typescript
// File: src/components/video/VideoPlayer.tsx
'use client'

import { useEffect, useRef, useState } from 'react'
import { VideoStorageService } from '@/lib/ipfs/video-storage'

interface VideoPlayerProps {
  videoCID: string
  autoplay?: boolean
  controls?: boolean
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
}

export default function VideoPlayer({ videoCID, autoplay, controls, onPlay, onPause, onEnded }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  
  useEffect(() => {
    const loadVideo = async () => {
      try {
        const storageService = new VideoStorageService()
        await storageService.initialize()
        
        const videoBlob = await storageService.getVideo(videoCID)
        const url = URL.createObjectURL(videoBlob)
        setVideoUrl(url)
        setLoading(false)
      } catch (err) {
        setError('Failed to load video')
        setLoading(false)
      }
    }
    
    loadVideo()
  }, [videoCID])
  
  if (loading) return <div className="flex items-center justify-center h-64">Loading...</div>
  if (error) return <div className="flex items-center justify-center h-64 text-red-500">{error}</div>
  
  return (
    <video
      ref={videoRef}
      src={videoUrl}
      autoPlay={autoplay}
      controls={controls}
      onPlay={onPlay}
      onPause={onPause}
      onEnded={onEnded}
      className="w-full h-full object-cover"
    />
  )
}
```

#### Week 2: Swipe Interface Implementation

**Day 1-2: Enhanced Swipe Gestures**
```typescript
// Update: src/hooks/useSwipeGestures.ts
import { useSpring, animated } from '@react-spring/web'
import { useDrag } from 'react-use-gesture'

export const useSwipeGestures = (onSwipeLeft: () => void, onSwipeRight: () => void, onSwipeUp: () => void) => {
  const [{ x, y, rotate }, api] = useSpring(() => ({ x: 0, y: 0, rotate: 0 }))
  
  const bind = useDrag(({ args: [index], down, movement: [mx, my], direction: [xDir, yDir], velocity }) => {
    const trigger = velocity > 0.2
    const isGone = !down && trigger
    
    if (isGone) {
      if (Math.abs(mx) > Math.abs(my)) {
        // Horizontal swipe
        if (xDir < 0) {
          onSwipeLeft()
        } else {
          onSwipeRight()
        }
      } else {
        // Vertical swipe
        if (yDir < 0) {
          onSwipeUp()
        }
      }
    }
    
    api.start({
      x: down ? mx : 0,
      y: down ? my : 0,
      rotate: down ? mx / 10 : 0,
      immediate: down
    })
  })
  
  return {
    bind,
    style: {
      transform: to([x, y, rotate], (x, y, r) => `translate3d(${x}px,${y}px,0) rotate(${r}deg)`)
    }
  }
}
```

**Day 3-4: Video Feed with Swipe**
```typescript
// File: src/components/feed/SwipeVideoFeed.tsx
'use client'

import { useState, useEffect } from 'react'
import { useSwipeGestures } from '@/hooks/useSwipeGestures'
import VideoPlayer from '@/components/video/VideoPlayer'
import { Video } from '@/types/video'

interface SwipeVideoFeedProps {
  videos: Video[]
  onLike: (videoId: string) => void
  onDislike: (videoId: string) => void
  onShare: (videoId: string) => void
}

export default function SwipeVideoFeed({ videos, onLike, onDislike }: SwipeVideoFeedProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [likedVideos, setLikedVideos] = useState<Set<string>>(new Set())
  
  const handleSwipeLeft = () => {
    const video = videos[currentIndex]
    onLike(video.id)
    setLikedVideos(prev => new Set([...prev, video.id]))
    nextVideo()
  }
  
  const handleSwipeRight = () => {
    const video = videos[currentIndex]
    onDislike(video.id)
    nextVideo()
  }
  
  const handleSwipeUp = () => {
    nextVideo()
  }
  
  const nextVideo = () => {
    setCurrentIndex(prev => (prev + 1) % videos.length)
  }
  
  const { bind, style } = useSwipeGestures(handleSwipeLeft, handleSwipeRight, handleSwipeUp)
  
  const currentVideo = videos[currentIndex]
  
  return (
    <div className="relative h-screen w-full overflow-hidden bg-black">
      <animated.div
        {...bind()}
        style={style}
        className="absolute inset-0 cursor-grab active:cursor-grabbing"
      >
        <VideoPlayer
          videoCID={currentVideo.ipfsHash || currentVideo.videoUrl}
          autoplay={true}
          controls={false}
        />
        
        {/* Video Info Overlay */}
        <div className="absolute bottom-20 left-4 right-20 z-10">
          <h3 className="text-white text-lg font-bold mb-2">{currentVideo.title}</h3>
          <p className="text-gray-300 text-sm mb-2">@{currentVideo.creator}</p>
          <p className="text-gray-300 text-sm">{currentVideo.description}</p>
        </div>
        
        {/* Action Buttons */}
        <div className="absolute bottom-20 right-4 flex flex-col space-y-4 z-10">
          <button
            onClick={handleSwipeLeft}
            className={`p-3 rounded-full ${
              likedVideos.has(currentVideo.id) ? 'bg-red-500' : 'bg-gray-800'
            } transition-colors`}
          >
            ❤️
          </button>
          
          <button
            onClick={() => onShare(currentVideo.id)}
            className="p-3 rounded-full bg-gray-800"
          >
            📤
          </button>
        </div>
        
        {/* Swipe Hints */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-xs opacity-50">
          ← Like | Dislike → | Next ↑
        </div>
      </animated.div>
    </div>
  )
}
```

**Day 5: Integration & Testing**
- Update main page to use SwipeVideoFeed
- Add video loading states and error handling
- Test swipe gestures across devices
- Performance optimization for video loading

### Phase 2: Enhanced User Experience (Sprint 2 - 2 weeks)

#### Week 1: User Management System

**Day 1-2: User State Management**
```typescript
// File: src/store/userStore.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface UserState {
  user: User | null
  isAuthenticated: boolean
  preferences: UserPreferences
  
  // Actions
  login: (user: User) => void
  logout: () => void
  updatePreferences: (prefs: Partial<UserPreferences>) => void
  updateProfile: (updates: Partial<User>) => void
}

export const useUserStore = create<UserState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      preferences: {
        theme: 'dark',
        autoplay: true,
        notifications: true,
        privacy: 'public'
      },
      
      login: (user) => set({ user, isAuthenticated: true }),
      logout: () => set({ user: null, isAuthenticated: false }),
      updatePreferences: (prefs) => set((state) => ({
        preferences: { ...state.preferences, ...prefs }
      })),
      updateProfile: (updates) => set((state) => ({
        user: state.user ? { ...state.user, ...updates } : null
      }))
    }),
    { name: 'user-storage' }
  )
)
```

**Day 3-4: Profile Management Component**
```typescript
// File: src/components/profile/UserProfile.tsx
'use client'

import { useState } from 'react'
import { useUserStore } from '@/store/userStore'
import { VideoStorageService } from '@/lib/ipfs/video-storage'

export default function UserProfile() {
  const { user, updateProfile } = useUserStore()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    username: user?.username || '',
    bio: user?.bio || '',
    avatar: user?.avatar || ''
  })
  
  const handleAvatarUpload = async (file: File) => {
    const storageService = new VideoStorageService()
    await storageService.initialize()
    
    const avatarCID = await storageService.uploadVideo(file)
    setFormData(prev => ({ ...prev, avatar: avatarCID }))
  }
  
  const handleSave = async () => {
    await updateProfile(formData)
    setIsEditing(false)
  }
  
  return (
    <div className="bg-gray-900 rounded-lg p-6">
      {/* Profile header with avatar, stats, edit button */}
      {/* Video grid showing user's uploads */}
      {/* Settings and preferences */}
    </div>
  )
}
```

**Day 5: Video Management Dashboard**
```typescript
// File: src/components/dashboard/VideoManagement.tsx
// Component for users to manage their uploaded videos
// Features: Edit metadata, delete videos, view analytics
```

#### Week 2: Search & Discovery

**Day 1-2: Search Infrastructure**
```typescript
// File: src/lib/search/videoSearch.ts
import { Video, VideoSearchFilters } from '@/types/video'

export class VideoSearchService {
  private videos: Video[] = []
  
  async searchVideos(filters: VideoSearchFilters): Promise<Video[]> {
    let results = [...this.videos]
    
    // Text search
    if (filters.query) {
      results = results.filter(video =>
        video.title.toLowerCase().includes(filters.query!.toLowerCase()) ||
        video.description?.toLowerCase().includes(filters.query!.toLowerCase()) ||
        video.tags?.some(tag => tag.toLowerCase().includes(filters.query!.toLowerCase()))
      )
    }
    
    // Category filter
    if (filters.category) {
      results = results.filter(video => video.categories.includes(filters.category!))
    }
    
    // Duration filter
    if (filters.duration) {
      results = results.filter(video => {
        const duration = this.parseDuration(video.duration)
        switch (filters.duration) {
          case 'short': return duration < 30
          case 'medium': return duration >= 30 && duration < 60
          case 'long': return duration >= 60
          default: return true
        }
      })
    }
    
    // Sort results
    return this.sortVideos(results, filters.sortBy || 'newest')
  }
  
  private sortVideos(videos: Video[], sortBy: string): Video[] {
    switch (sortBy) {
      case 'newest':
        return videos.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      case 'popular':
        return videos.sort((a, b) => b.views - a.views)
      case 'trending':
        return videos.sort((a, b) => b.likes - a.likes)
      default:
        return videos
    }
  }
}
```

**Day 3-4: Search UI Components**
```typescript
// File: src/components/search/SearchInterface.tsx
// Advanced search with filters, sort options, results grid
```

**Day 5: Content Recommendation Algorithm**
```typescript
// File: src/lib/recommendations/videoRecommendations.ts
// Basic recommendation system based on user interactions
```

### Phase 3: Social Features (Sprint 3 - 3 weeks)

#### Week 1: User Following System
- Follow/unfollow functionality
- Follower/following lists
- Subscription feed

#### Week 2: Comments & Interactions
- Threaded comment system
- Real-time updates
- Moderation tools

#### Week 3: Playlists & Collections
- Create/manage playlists
- Public/private playlists
- Collaborative playlists

### Phase 4: Advanced Features (Sprint 4 - 3 weeks)

#### Week 1: Performance Optimization
- Video compression pipeline
- Progressive loading
- Caching strategies

#### Week 2: Analytics & Monitoring
- User engagement metrics
- Video performance analytics
- Error tracking and monitoring

#### Week 3: PWA & Mobile Optimization
- Service worker implementation
- Push notifications
- Offline content caching

## File Structure Changes Needed

```
src/
├── components/
│   ├── auth/
│   │   ├── SimpleAuthModal.tsx        ✅ Done
│   │   ├── LoginForm.tsx              📝 New
│   │   └── SignupForm.tsx             📝 New
│   ├── video/
│   │   ├── VideoCard.tsx              ✅ Done
│   │   ├── VideoPlayer.tsx            📝 New
│   │   ├── SwipeVideoModal.tsx        🔄 Update
│   │   └── VideoControls.tsx          📝 New
│   ├── feed/
│   │   ├── CategoryTabs.tsx           ✅ Done
│   │   ├── SwipeVideoFeed.tsx         📝 New
│   │   └── VideoGrid.tsx              📝 New
│   ├── upload/
│   │   ├── VideoUploadModal.tsx       ✅ Done
│   │   ├── VideoProcessor.tsx         📝 New
│   │   └── ThumbnailGenerator.tsx     📝 New
│   ├── search/
│   │   ├── SearchInterface.tsx        📝 New
│   │   ├── SearchFilters.tsx          📝 New
│   │   └── SearchResults.tsx          📝 New
│   ├── profile/
│   │   ├── UserProfile.tsx            📝 New
│   │   ├── ProfileEditor.tsx          📝 New
│   │   └── UserStats.tsx              📝 New
│   └── dashboard/
│       ├── VideoManagement.tsx        📝 New
│       ├── Analytics.tsx              📝 New
│       └── Settings.tsx               📝 New
├── lib/
│   ├── ipfs/
│   │   ├── video-storage.ts           📝 New
│   │   ├── metadata-storage.ts        📝 New
│   │   └── content-delivery.ts        📝 New
│   ├── search/
│   │   ├── videoSearch.ts             📝 New
│   │   └── searchIndex.ts             📝 New
│   ├── recommendations/
│   │   ├── videoRecommendations.ts    📝 New
│   │   └── userBehavior.ts            📝 New
│   └── utils/
│       ├── video-processing.ts        📝 New
│       ├── thumbnail-generation.ts    📝 New
│       └── performance.ts             📝 New
├── hooks/
│   ├── useSwipeGestures.ts            🔄 Update
│   ├── useVideoPlayer.ts              📝 New
│   ├── useSearch.ts                   📝 New
│   └── useRecommendations.ts          📝 New
├── store/
│   ├── userStore.ts                   📝 New
│   ├── videoStore.ts                  📝 New
│   └── searchStore.ts                 📝 New
└── types/
    ├── video.ts                       ✅ Done
    ├── user.ts                        📝 New
    ├── search.ts                      📝 New
    └── api.ts                         📝 New
```

## Development Guidelines

### Code Quality Standards
1. **TypeScript First**: All new code must be TypeScript with strict typing
2. **Component Testing**: Each component needs unit tests
3. **Performance Monitoring**: Track bundle size and runtime performance
4. **Accessibility**: WCAG 2.1 AA compliance for all UI components

### Git Workflow
1. **Feature Branches**: One branch per feature/component
2. **PR Reviews**: All code requires review before merge
3. **Testing**: All PRs must pass automated tests
4. **Documentation**: Update docs with new features

### Performance Targets
- **Bundle Size**: < 500KB initial load
- **Video Loading**: < 2s to first frame
- **Smooth Interactions**: 60fps animations
- **Memory Usage**: < 100MB for 20 videos in feed

## Next Immediate Actions

1. **Complete IPFS Integration** (Priority 1)
   - Implement VideoStorageService
   - Update VideoUploadModal to use IPFS
   - Test video upload/retrieval pipeline

2. **Implement Swipe Video Feed** (Priority 1)
   - Create SwipeVideoFeed component
   - Update useSwipeGestures hook
   - Test on mobile devices

3. **User State Management** (Priority 2)
   - Implement userStore with Zustand
   - Connect authentication system
   - Add user preferences

4. **Performance Optimization** (Priority 2)
   - Implement video compression
   - Add lazy loading for video grid
   - Optimize IPFS gateway performance

## Risk Mitigation

### Technical Risks
1. **IPFS Performance**: Implement multiple gateway fallbacks
2. **Video Compression**: Client-side processing may be slow on mobile
3. **Browser Compatibility**: Test extensively across browsers

### Solutions
1. **Progressive Enhancement**: Core features work without advanced capabilities
2. **Graceful Degradation**: Fallbacks for unsupported features
3. **Comprehensive Testing**: Automated testing across environments

---

This roadmap provides a clear path from our current implementation to a fully-featured short-form video platform. Each phase builds on the previous one while maintaining a working application throughout development.
