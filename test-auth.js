#!/usr/bin/env node

const http = require('http');

// ANSI color codes for beautiful console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    bgRed: '\x1b[41m',
    bgGreen: '\x1b[42m',
    bgYellow: '\x1b[43m',
    bgBlue: '\x1b[44m'
};

const API_BASE = 'http://localhost:4000/api/Upsocial';
let testResults = [];
let testCount = 0;
let passedTests = 0;
let failedTests = 0;

function log(message, color = 'white') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logResult(testName, passed, message) {
    testCount++;
    if (passed) {
        passedTests++;
        log(`✅ ${testName}: ${message}`, 'green');
    } else {
        failedTests++;
        log(`❌ ${testName}: ${message}`, 'red');
    }
    testResults.push({ testName, passed, message, timestamp: new Date().toISOString() });
}

function makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(API_BASE + endpoint);
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname,
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk.toString();
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({ success: true, data: jsonData, status: res.statusCode });
                } catch (error) {
                    resolve({ success: false, error: 'Invalid JSON response', body, status: res.statusCode });
                }
            });
        });

        req.on('error', (error) => {
            resolve({ success: false, error: error.message });
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function testSystemInfo() {
    log('\n🔧 Testing System Information...', 'cyan');
    
    const result = await makeRequest('/system/info');
    if (result.success && result.data.message) {
        logResult('System Info', true, `${result.data.message} v${result.data.version}`);
        logResult('API Endpoints', true, `Available: ${result.data.authEndpoints.join(', ')}`);
    } else {
        logResult('System Info', false, `Failed to get system info: ${result.error || 'Unknown error'}`);
    }
}

async function testDatabaseInitialization() {
    log('\n🗄️ Testing Database Initialization...', 'cyan');
    
    const result = await makeRequest('/admin/createDB', 'POST');
    if (result.success && result.data.status) {
        logResult('Database Init', true, `${result.data.msg} (Mode: ${result.data.mode})`);
    } else {
        logResult('Database Init', false, `Failed: ${result.data?.msg || result.error}`);
    }
}

async function testUserRegistration() {
    log('\n📝 Testing User Registration...', 'cyan');
    
    // Test 1: Valid registration
    const testUser = {
        username: 'playwright_test_' + Date.now(),
        email: 'playwright_' + Date.now() + '@example.com',
        password: 'TestPassword123!'
    };
    
    const reg1 = await makeRequest('/users/register', 'POST', testUser);
    if (reg1.success && reg1.data.status) {
        logResult('Valid Registration', true, reg1.data.msg);
    } else {
        logResult('Valid Registration', false, reg1.data?.msg || reg1.error);
    }

    // Test 2: Duplicate email registration
    const reg2 = await makeRequest('/users/register', 'POST', testUser);
    if (reg2.success && !reg2.data.status && reg2.data.msg.includes('already registered')) {
        logResult('Duplicate Email Prevention', true, 'Correctly rejected duplicate email');
    } else {
        logResult('Duplicate Email Prevention', false, 'Failed to prevent duplicate email');
    }

    // Test 3: Duplicate username
    const userWithSameUsername = {
        username: testUser.username,
        email: 'different_' + Date.now() + '@example.com',
        password: 'TestPassword123!'
    };
    const reg3 = await makeRequest('/users/register', 'POST', userWithSameUsername);
    if (reg3.success && !reg3.data.status && reg3.data.msg.includes('already taken')) {
        logResult('Duplicate Username Prevention', true, 'Correctly rejected duplicate username');
    } else {
        logResult('Duplicate Username Prevention', false, 'Failed to prevent duplicate username');
    }

    // Test 4: Missing required fields
    const reg4 = await makeRequest('/users/register', 'POST', { username: 'incomplete' });
    if (!reg4.success || reg4.status === 400 || (reg4.data && !reg4.data.status)) {
        logResult('Required Fields Validation', true, 'Correctly rejected incomplete data');
    } else {
        logResult('Required Fields Validation', false, 'Failed to validate required fields');
    }

    // Test 5: Empty fields
    const reg5 = await makeRequest('/users/register', 'POST', { username: '', email: '', password: '' });
    if (!reg5.success || reg5.status === 400 || (reg5.data && !reg5.data.status)) {
        logResult('Empty Fields Validation', true, 'Correctly rejected empty fields');
    } else {
        logResult('Empty Fields Validation', false, 'Failed to validate empty fields');
    }

    return testUser; // Return for login tests
}

async function testUserLogin(registeredUser) {
    log('\n🔐 Testing User Login...', 'cyan');
    
    // Test 1: Valid login with registered user
    const login1 = await makeRequest('/users/login', 'POST', {
        email: registeredUser.email,
        password: registeredUser.password
    });
    if (login1.success && login1.data.status && login1.data.curUser === registeredUser.email) {
        logResult('Valid Login', true, `Successful login for ${login1.data.curUser}`);
        logResult('User Data Return', true, `Returned user data with ID: ${login1.data.Data.ID}`);
    } else {
        logResult('Valid Login', false, login1.data?.msg || login1.error);
    }

    // Test 2: Wrong password
    const login2 = await makeRequest('/users/login', 'POST', {
        email: registeredUser.email,
        password: 'wrongpassword123'
    });
    if (login2.success && !login2.data.status && login2.data.msg.includes('Invalid credentials')) {
        logResult('Wrong Password Rejection', true, 'Correctly rejected wrong password');
    } else {
        logResult('Wrong Password Rejection', false, 'Failed to reject wrong password');
    }

    // Test 3: Non-existent user
    const login3 = await makeRequest('/users/login', 'POST', {
        email: 'nonexistent_' + Date.now() + '@example.com',
        password: 'password123'
    });
    if (login3.success && !login3.data.status && login3.data.msg.includes('Invalid credentials')) {
        logResult('Non-existent User Rejection', true, 'Correctly rejected non-existent user');
    } else {
        logResult('Non-existent User Rejection', false, 'Failed to reject non-existent user');
    }

    // Test 4: Missing password
    const login4 = await makeRequest('/users/login', 'POST', { email: registeredUser.email });
    if (!login4.success || login4.status === 400 || (login4.data && !login4.data.status)) {
        logResult('Missing Password Validation', true, 'Correctly rejected missing password');
    } else {
        logResult('Missing Password Validation', false, 'Failed to validate missing password');
    }

    // Test 5: Missing email
    const login5 = await makeRequest('/users/login', 'POST', { password: 'password123' });
    if (!login5.success || login5.status === 400 || (login5.data && !login5.data.status)) {
        logResult('Missing Email Validation', true, 'Correctly rejected missing email');
    } else {
        logResult('Missing Email Validation', false, 'Failed to validate missing email');
    }
}

async function testPasswordSecurity() {
    log('\n🔒 Testing Password Security...', 'cyan');
    
    // Test password encryption (register user and verify password is not stored in plain text)
    const securityTestUser = {
        username: 'security_test_' + Date.now(),
        email: 'security_' + Date.now() + '@example.com',
        password: 'MySecretPassword123!'
    };
    
    const reg = await makeRequest('/users/register', 'POST', securityTestUser);
    if (reg.success && reg.data.status) {
        // Get all users to check if password is encrypted
        const users = await makeRequest('/admin/getAllUsers');
        if (users.success && users.data.users) {
            const user = users.data.users.find(u => u.email === securityTestUser.email);
            if (user && (!user.password || user.password !== securityTestUser.password)) {
                logResult('Password Encryption', true, 'Password is properly encrypted/hidden');
            } else {
                logResult('Password Encryption', false, 'Password appears to be stored in plain text');
            }
        } else {
            logResult('Password Encryption', false, 'Could not retrieve users to verify encryption');
        }
    } else {
        logResult('Password Encryption', false, 'Could not register user for security test');
    }
}

async function testEdgeCases() {
    log('\n🎯 Testing Edge Cases...', 'cyan');
    
    // Test very long inputs
    const longString = 'a'.repeat(1000);
    const longTest = await makeRequest('/users/register', 'POST', {
        username: longString,
        email: longString + '@example.com',
        password: longString
    });
    // This should ideally fail or be handled gracefully
    logResult('Long Input Handling', !longTest.success || !longTest.data?.status, 
        'System handles extremely long inputs gracefully');

    // Test special characters
    const specialTest = await makeRequest('/users/register', 'POST', {
        username: 'test<script>alert("xss")</script>',
        email: '<EMAIL>',
        password: 'pass<script>word'
    });
    logResult('Special Character Handling', true, 'System processes special characters');

    // Test SQL injection-like patterns (should be safe since we're not using SQL)
    const sqlTest = await makeRequest('/users/login', 'POST', {
        email: "' OR '1'='1",
        password: "' OR '1'='1"
    });
    if (sqlTest.success && !sqlTest.data.status) {
        logResult('SQL Injection Protection', true, 'System resistant to SQL injection patterns');
    } else {
        logResult('SQL Injection Protection', false, 'System may be vulnerable to injection');
    }
}

async function testConcurrentOperations() {
    log('\n⚡ Testing Concurrent Operations...', 'cyan');
    
    // Test multiple registrations at the same time
    const timestamp = Date.now();
    const concurrentPromises = [];
    
    for (let i = 0; i < 5; i++) {
        concurrentPromises.push(makeRequest('/users/register', 'POST', {
            username: `concurrent_${timestamp}_${i}`,
            email: `concurrent_${timestamp}_${i}@example.com`,
            password: 'ConcurrentTest123!'
        }));
    }
    
    const results = await Promise.all(concurrentPromises);
    const successCount = results.filter(r => r.success && r.data.status).length;
    
    logResult('Concurrent Registrations', successCount === 5, 
        `${successCount}/5 concurrent registrations succeeded`);

    // Test concurrent logins
    const loginPromises = results
        .filter(r => r.success && r.data.status)
        .map((_, i) => makeRequest('/users/login', 'POST', {
            email: `concurrent_${timestamp}_${i}@example.com`,
            password: 'ConcurrentTest123!'
        }));
    
    if (loginPromises.length > 0) {
        const loginResults = await Promise.all(loginPromises);
        const loginSuccessCount = loginResults.filter(r => r.success && r.data.status).length;
        
        logResult('Concurrent Logins', loginSuccessCount === loginPromises.length,
            `${loginSuccessCount}/${loginPromises.length} concurrent logins succeeded`);
    }
}

async function testAdminFunctions() {
    log('\n👑 Testing Admin Functions...', 'cyan');
    
    // Test get all users
    const users = await makeRequest('/admin/getAllUsers');
    if (users.success && users.data.status && Array.isArray(users.data.users)) {
        logResult('Get All Users', true, `Retrieved ${users.data.count} users`);
        logResult('User Data Structure', true, 'Users have proper data structure');
    } else {
        logResult('Get All Users', false, users.data?.msg || users.error);
    }
}

function printSummary() {
    log('\n' + '='.repeat(60), 'cyan');
    log('🏁 TEST SUITE SUMMARY', 'cyan');
    log('='.repeat(60), 'cyan');
    
    log(`📊 Total Tests: ${testCount}`, 'white');
    log(`✅ Passed: ${passedTests}`, 'green');
    log(`❌ Failed: ${failedTests}`, 'red');
    log(`📈 Success Rate: ${((passedTests / testCount) * 100).toFixed(1)}%`, 
        passedTests === testCount ? 'green' : 'yellow');
    
    if (failedTests === 0) {
        log('\n🎉 ALL TESTS PASSED! Authentication system is working perfectly!', 'bgGreen');
    } else if (passedTests > failedTests) {
        log('\n⚠️ Most tests passed, but some issues were found.', 'bgYellow');
    } else {
        log('\n🚨 Critical issues found! Authentication system needs attention.', 'bgRed');
    }
    
    log('\n📝 Detailed Results:', 'cyan');
    testResults.forEach(result => {
        const symbol = result.passed ? '✅' : '❌';
        const color = result.passed ? 'green' : 'red';
        log(`${symbol} ${result.testName}: ${result.message}`, color);
    });
    
    log('\n🔗 Test completed. You can also open test-auth.html in a browser for interactive testing.', 'blue');
}

async function runAllTests() {
    log('🚀 Starting Comprehensive Upsocial Authentication Test Suite', 'bgBlue');
    log('=' * 60, 'blue');
    
    try {
        await testSystemInfo();
        await testDatabaseInitialization();
        const registeredUser = await testUserRegistration();
        await testUserLogin(registeredUser);
        await testPasswordSecurity();
        await testEdgeCases();
        await testConcurrentOperations();
        await testAdminFunctions();
        
        printSummary();
        
        // Exit with appropriate code
        process.exit(failedTests === 0 ? 0 : 1);
        
    } catch (error) {
        log(`\n💥 Test suite crashed: ${error.message}`, 'bgRed');
        console.error(error);
        process.exit(1);
    }
}

// Check if server is running before starting tests
async function checkServerHealth() {
    log('🔍 Checking if backend server is running...', 'yellow');
    
    const health = await makeRequest('/system/info');
    if (health.success) {
        log('✅ Backend server is running and responsive!', 'green');
        return true;
    } else {
        log('❌ Backend server is not responding!', 'red');
        log('💡 Make sure to run: cd Upsocial_backend-main && node server.js', 'yellow');
        return false;
    }
}

// Main execution
async function main() {
    const serverHealthy = await checkServerHealth();
    if (serverHealthy) {
        await runAllTests();
    } else {
        process.exit(1);
    }
}

main();