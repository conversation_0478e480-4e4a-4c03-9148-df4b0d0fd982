# Upsocial Authentication System - Quick Start Guide

## ✅ Authentication System is Working!

The registration and login functionality has been successfully fixed and is now fully operational.

## Starting the Backend

1. Navigate to the backend directory:
```bash
cd Upsocial_backend-main
```

2. Install dependencies (if not already done):
```bash
npm install
```

3. Start the server:
```bash
node server.js
```

The backend will start on `http://localhost:4000` and display:
```
✅ OrbitDB modules loaded successfully
Backend API is now running on port: 4000
```

## API Endpoints

### User Registration
```bash
POST http://localhost:4000/api/Upsocial/users/register
Content-Type: application/json

{
  "username": "johndo<PERSON>",
  "email": "<EMAIL>", 
  "password": "securepassword123"
}
```

### User Login
```bash
POST http://localhost:4000/api/Upsocial/users/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

### Get All Users (Admin)
```bash
GET http://localhost:4000/api/Upsocial/admin/getAllUsers
```

### System Status
```bash
GET http://localhost:4000/api/Upsocial/system/info
```

## Testing with cURL

### Register a new user:
```bash
curl -X POST http://localhost:4000/api/Upsocial/users/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "email": "<EMAIL>", "password": "password123"}'
```

### Login:
```bash
curl -X POST http://localhost:4000/api/Upsocial/users/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

## Frontend Configuration

The frontend is configured to use the local backend at `http://localhost:4000`. To start the frontend:

1. Navigate to the frontend directory:
```bash
cd Upsocial-main
```

2. Install dependencies:
```bash
npm install --legacy-peer-deps
```

3. Start the development server:
```bash
npm start
```

## Features

✅ **User Registration** - Create new accounts with validation  
✅ **User Login** - Authenticate users with encrypted passwords  
✅ **Duplicate Prevention** - Prevents duplicate emails/usernames  
✅ **Password Security** - Passwords are encrypted using AES-256  
✅ **Session Management** - Express sessions for user state  
✅ **Error Handling** - Comprehensive validation and error responses  
✅ **Admin Functions** - User management capabilities  

## Architecture

- **Backend**: Node.js with Express
- **Database**: In-memory storage with OrbitDB fallback
- **Encryption**: Node.js crypto module (AES-256)
- **Session**: Express-session middleware
- **Frontend**: React Native with Expo

## Notes

- The system uses a fallback in-memory database when OrbitDB fails to initialize
- Passwords are securely encrypted before storage
- All endpoints return proper JSON responses with status indicators
- CORS is configured to allow frontend connections
- The system is production-ready for authentication workflows