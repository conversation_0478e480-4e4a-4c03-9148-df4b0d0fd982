---
applyTo: '**'
---
Always use the memory, sequential thinking, and playwright MCP's. When needed, use the contect7 MCP, especially when working with SDK's or npm packages. 

To run the dev server ALWAYS use: cd /home/<USER>/Documents/code/upsocial/upsocial-helia && npm run dev   and always use a new terminal after starting a serer so you don't kill it by mistake

Always use modualarity, KISS and DRY when coding. Make all specs with the spec-driven MCP. 

Always remove test files or scripts when they are no longer needed. 

